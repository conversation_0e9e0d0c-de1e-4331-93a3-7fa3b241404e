#!/usr/bin/env python3
"""
Real-time Progress Monitor for Crawl4AI Testing
Displays live statistics and progress toward 300+ websites
"""

import time
import sqlite3
import json
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live
from rich.layout import Layout
from rich.text import Text

console = Console()

def get_comprehensive_stats():
    """Get comprehensive testing statistics"""
    conn = sqlite3.connect('test_results.db')
    cursor = conn.cursor()
    
    # Basic stats
    cursor.execute("SELECT COUNT(*) FROM test_results")
    total_tests = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM test_results WHERE status = 'PASS'")
    passed_tests = cursor.fetchone()[0]
    
    cursor.execute("SELECT AVG(extraction_rate) FROM test_results WHERE extraction_rate IS NOT NULL")
    avg_extraction = cursor.fetchone()[0] or 0
    
    cursor.execute("SELECT COUNT(*) FROM website_classifications")
    cache_entries = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM discovered_websites")
    discovered_sites = cursor.fetchone()[0]
    
    # Unique websites tested
    cursor.execute("SELECT COUNT(DISTINCT url) FROM test_results")
    unique_websites = cursor.fetchone()[0]
    
    # Technology breakdown
    cursor.execute("SELECT technology_stack FROM website_classifications")
    tech_data = cursor.fetchall()
    tech_counts = {}
    for row in tech_data:
        if row[0]:
            techs = json.loads(row[0])
            for tech in techs:
                tech_counts[tech] = tech_counts.get(tech, 0) + 1
    
    # Anti-bot measures
    cursor.execute("SELECT anti_bot_measures FROM website_classifications")
    antibot_data = cursor.fetchall()
    antibot_counts = {}
    for row in antibot_data:
        if row[0]:
            measures = json.loads(row[0])
            for measure in measures:
                antibot_counts[measure] = antibot_counts.get(measure, 0) + 1
    
    # Recent activity
    cursor.execute("SELECT test_id, url, status, timestamp FROM test_results ORDER BY id DESC LIMIT 5")
    recent_tests = cursor.fetchall()
    
    conn.close()
    
    return {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
        'avg_extraction': avg_extraction * 100,
        'cache_entries': cache_entries,
        'discovered_sites': discovered_sites,
        'unique_websites': unique_websites,
        'tech_counts': tech_counts,
        'antibot_counts': antibot_counts,
        'recent_tests': recent_tests
    }

def create_progress_display():
    """Create the main progress display"""
    stats = get_comprehensive_stats()
    
    # Main progress table
    progress_table = Table(title="🕷️ Crawl4AI Testing Progress Dashboard")
    progress_table.add_column("Metric", style="cyan")
    progress_table.add_column("Current", style="green")
    progress_table.add_column("Target", style="yellow")
    progress_table.add_column("Status", style="magenta")
    
    websites_tested = stats['unique_websites']
    progress_pct = (websites_tested / 300 * 100) if websites_tested > 0 else 0
    
    progress_table.add_row("Websites Tested", f"{websites_tested}", "300", f"{progress_pct:.1f}%")
    progress_table.add_row("Total Tests", f"{stats['total_tests']}", "80+", "📊")
    progress_table.add_row("Success Rate", f"{stats['success_rate']:.1f}%", ">80%", 
                          "✅" if stats['success_rate'] > 80 else "⚠️")
    progress_table.add_row("Extraction Rate", f"{stats['avg_extraction']:.1f}%", ">85%", 
                          "✅" if stats['avg_extraction'] > 85 else "⚠️")
    progress_table.add_row("Cache Entries", f"{stats['cache_entries']}", "300+", "🗄️")
    progress_table.add_row("Discovered Sites", f"{stats['discovered_sites']}", "500+", "🔍")
    
    # Technology breakdown
    tech_table = Table(title="🔧 Technology Stack Analysis")
    tech_table.add_column("Technology", style="cyan")
    tech_table.add_column("Count", style="green")
    tech_table.add_column("Percentage", style="yellow")
    
    total_sites = stats['cache_entries']
    for tech, count in sorted(stats['tech_counts'].items(), key=lambda x: x[1], reverse=True)[:10]:
        percentage = (count / total_sites * 100) if total_sites > 0 else 0
        tech_table.add_row(tech, str(count), f"{percentage:.1f}%")
    
    # Anti-bot measures
    antibot_table = Table(title="🛡️ Anti-Bot Protection Analysis")
    antibot_table.add_column("Protection", style="cyan")
    antibot_table.add_column("Count", style="red")
    antibot_table.add_column("Percentage", style="yellow")
    
    for measure, count in sorted(stats['antibot_counts'].items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_sites * 100) if total_sites > 0 else 0
        antibot_table.add_row(measure, str(count), f"{percentage:.1f}%")
    
    # Recent activity
    recent_table = Table(title="📋 Recent Test Activity")
    recent_table.add_column("Test ID", style="cyan")
    recent_table.add_column("URL", style="blue")
    recent_table.add_column("Status", style="green")
    recent_table.add_column("Time", style="yellow")
    
    for test in stats['recent_tests']:
        url_short = test[1][:50] + "..." if len(test[1]) > 50 else test[1]
        status_emoji = "✅" if test[2] == "PASS" else "❌" if test[2] == "FAIL" else "⚠️"
        recent_table.add_row(test[0], url_short, f"{status_emoji} {test[2]}", test[3][:19])
    
    # Create layout
    layout = Layout()
    layout.split_column(
        Layout(progress_table, name="progress"),
        Layout().split_row(
            Layout(tech_table, name="tech"),
            Layout(antibot_table, name="antibot")
        ),
        Layout(recent_table, name="recent")
    )
    
    return layout

def main():
    """Main monitoring loop"""
    
    console.print(Panel("🚀 Crawl4AI Testing - Real-time Monitor", style="bold green"))
    console.print("Monitoring progress toward 300+ websites...")
    console.print("Press Ctrl+C to stop monitoring\n")
    
    try:
        with Live(create_progress_display(), refresh_per_second=0.5, console=console) as live:
            while True:
                time.sleep(2)  # Update every 2 seconds
                live.update(create_progress_display())
                
                # Check if target reached
                stats = get_comprehensive_stats()
                if stats['unique_websites'] >= 300:
                    live.stop()
                    console.print(Panel("🎉 TARGET REACHED! 300+ Websites Tested!", style="bold green"))
                    break
                    
    except KeyboardInterrupt:
        console.print("\n[yellow]Monitoring stopped by user[/yellow]")
        
        # Show final stats
        stats = get_comprehensive_stats()
        console.print(f"\n📊 Final Statistics:")
        console.print(f"   Websites Tested: {stats['unique_websites']}/300")
        console.print(f"   Success Rate: {stats['success_rate']:.1f}%")
        console.print(f"   Cache Entries: {stats['cache_entries']}")

if __name__ == "__main__":
    main()
