{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://workstations.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Workstations", "description": "Allows administrators to create managed developer environments in the cloud. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/workstations", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "workstations:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://workstations.mtls.googleapis.com/", "name": "workstations", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "workstations.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "workstations.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "workstations.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "workstations.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "workstationClusters": {"methods": {"create": {"description": "Creates a new workstation cluster.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "location": "query", "type": "boolean"}, "workstationClusterId": {"description": "Required. ID to use for the workstation cluster.", "location": "query", "type": "string"}}, "path": "v1beta/{+parent}/workstationClusters", "request": {"$ref": "WorkstationCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified workstation cluster.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}", "httpMethod": "DELETE", "id": "workstations.projects.locations.workstationClusters.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If set, the request will be rejected if the latest version of the workstation cluster on the server does not have this ETag.", "location": "query", "type": "string"}, "force": {"description": "Optional. If set, any workstation configurations and workstations in the workstation cluster are also deleted. Otherwise, the request only works if the workstation cluster has no configurations or workstations.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the workstation cluster to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not apply it.", "location": "query", "type": "boolean"}}, "path": "v1beta/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the requested workstation cluster.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the requested resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "WorkstationCluster"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns all workstation clusters in the specified location.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter the WorkstationClusters to be listed. Possible filters are described in https://google.aip.dev/160.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of items to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/workstationClusters", "response": {"$ref": "ListWorkstationClustersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing workstation cluster.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}", "httpMethod": "PATCH", "id": "workstations.projects.locations.workstationClusters.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set, and the workstation cluster is not found, a new workstation cluster will be created. In this situation, update_mask is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Full name of this workstation cluster.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask that specifies which fields in the workstation cluster should be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "location": "query", "type": "boolean"}}, "path": "v1beta/{+name}", "request": {"$ref": "WorkstationCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"workstationConfigs": {"methods": {"create": {"description": "Creates a new workstation configuration.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "location": "query", "type": "boolean"}, "workstationConfigId": {"description": "Required. ID to use for the workstation configuration.", "location": "query", "type": "string"}}, "path": "v1beta/{+parent}/workstationConfigs", "request": {"$ref": "WorkstationConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified workstation configuration.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}", "httpMethod": "DELETE", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If set, the request is rejected if the latest version of the workstation configuration on the server does not have this ETag.", "location": "query", "type": "string"}, "force": {"description": "Optional. If set, any workstations in the workstation configuration are also deleted. Otherwise, the request works only if the workstation configuration has no workstations.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the workstation configuration to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "location": "query", "type": "boolean"}}, "path": "v1beta/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the requested workstation configuration.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the requested resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "WorkstationConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}:getIamPolicy", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns all workstation configurations in the specified cluster.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter the WorkstationConfigs to be listed. Possible filters are described in https://google.aip.dev/160.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of items to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/workstationConfigs", "response": {"$ref": "ListWorkstationConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listUsable": {"description": "Returns all workstation configurations in the specified cluster on which the caller has the \"workstations.workstation.create\" permission.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs:listUsable", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.listUsable", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Maximum number of items to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/workstationConfigs:listUsable", "response": {"$ref": "ListUsableWorkstationConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing workstation configuration.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}", "httpMethod": "PATCH", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set and the workstation configuration is not found, a new workstation configuration will be created. In this situation, update_mask is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Full name of this workstation configuration.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask specifying which fields in the workstation configuration should be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "location": "query", "type": "boolean"}}, "path": "v1beta/{+name}", "request": {"$ref": "WorkstationConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}:setIamPolicy", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}:testIamPermissions", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"workstations": {"methods": {"create": {"description": "Creates a new workstation.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "location": "query", "type": "boolean"}, "workstationId": {"description": "Required. ID to use for the workstation.", "location": "query", "type": "string"}}, "path": "v1beta/{+parent}/workstations", "request": {"$ref": "Workstation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified workstation.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}", "httpMethod": "DELETE", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If set, the request will be rejected if the latest version of the workstation on the server does not have this ETag.", "location": "query", "type": "string"}, "name": {"description": "Required. Name of the workstation to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "location": "query", "type": "boolean"}}, "path": "v1beta/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generateAccessToken": {"description": "Returns a short-lived credential that can be used to send authenticated and authorized traffic to a workstation. Once generated this token cannot be revoked and is good for the lifetime of the token.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:generateAccessToken", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.generateAccessToken", "parameterOrder": ["workstation"], "parameters": {"workstation": {"description": "Required. Name of the workstation for which the access token should be generated.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+workstation}:generateAccessToken", "request": {"$ref": "GenerateAccessTokenRequest"}, "response": {"$ref": "GenerateAccessTokenResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the requested workstation.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the requested resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "Workstation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:getIamPolicy", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns all Workstations using the specified workstation configuration.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter the Workstations to be listed. Possible filters are described in https://google.aip.dev/160.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of items to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/workstations", "response": {"$ref": "ListWorkstationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listUsable": {"description": "Returns all workstations using the specified workstation configuration on which the caller has the \"workstations.workstations.use\" permission.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations:listUsable", "httpMethod": "GET", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.listUsable", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Maximum number of items to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/workstations:listUsable", "response": {"$ref": "ListUsableWorkstationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing workstation.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}", "httpMethod": "PATCH", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set and the workstation configuration is not found, a new workstation configuration is created. In this situation, update_mask is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Full name of this workstation.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask specifying which fields in the workstation configuration should be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "location": "query", "type": "boolean"}}, "path": "v1beta/{+name}", "request": {"$ref": "Workstation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:setIamPolicy", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "start": {"description": "Starts running a workstation so that users can connect to it.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:start", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.start", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the workstation to start.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:start", "request": {"$ref": "StartWorkstationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "stop": {"description": "Stops running a workstation, reducing costs.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:stop", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.stop", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the workstation to stop.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:stop", "request": {"$ref": "StopWorkstationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:testIamPermissions", "httpMethod": "POST", "id": "workstations.projects.locations.workstationClusters.workstationConfigs.workstations.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workstationClusters/[^/]+/workstationConfigs/[^/]+/workstations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}}}, "revision": "20250326", "rootUrl": "https://workstations.googleapis.com/", "schemas": {"Accelerator": {"description": "An accelerator card attached to the instance.", "id": "Accelerator", "properties": {"count": {"description": "Optional. Number of accelerator cards exposed to the instance.", "format": "int32", "type": "integer"}, "type": {"description": "Optional. Type of accelerator resource to attach to the instance, for example, `\"nvidia-tesla-p100\"`.", "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "BoostConfig": {"description": "A boost configuration is a set of resources that a workstation can use to increase its performance. If you specify a boost configuration, upon startup, workstation users can choose to use a VM provisioned under the boost config by passing the boost config ID in the start request. If the workstation user does not provide a boost config ID in the start request, the system will choose a VM from the pool provisioned under the default config.", "id": "BoostConfig", "properties": {"accelerators": {"description": "Optional. A list of the type and count of accelerator cards attached to the boost instance. Defaults to `none`.", "items": {"$ref": "Accelerator"}, "type": "array"}, "bootDiskSizeGb": {"description": "Optional. The size of the boot disk for the VM in gigabytes (GB). The minimum boot disk size is `30` GB. Defaults to `50` GB.", "format": "int32", "type": "integer"}, "enableNestedVirtualization": {"description": "Optional. Whether to enable nested virtualization on boosted Cloud Workstations VMs running using this boost configuration. Defaults to false. Nested virtualization lets you run virtual machine (VM) instances inside your workstation. Before enabling nested virtualization, consider the following important considerations. Cloud Workstations instances are subject to the [same restrictions as Compute Engine instances](https://cloud.google.com/compute/docs/instances/nested-virtualization/overview#restrictions): * **Organization policy**: projects, folders, or organizations may be restricted from creating nested VMs if the **Disable VM nested virtualization** constraint is enforced in the organization policy. For more information, see the Compute Engine section, [Checking whether nested virtualization is allowed](https://cloud.google.com/compute/docs/instances/nested-virtualization/managing-constraint#checking_whether_nested_virtualization_is_allowed). * **Performance**: nested VMs might experience a 10% or greater decrease in performance for workloads that are CPU-bound and possibly greater than a 10% decrease for workloads that are input/output bound. * **Machine Type**: nested virtualization can only be enabled on boost configurations that specify a machine_type in the N1 or N2 machine series.", "type": "boolean"}, "id": {"description": "Required. The ID to be used for the boost configuration.", "type": "string"}, "machineType": {"description": "Optional. The type of machine that boosted VM instances will use—for example, `e2-standard-4`. For more information about machine types that Cloud Workstations supports, see the list of [available machine types](https://cloud.google.com/workstations/docs/available-machine-types). Defaults to `e2-standard-4`.", "type": "string"}, "poolSize": {"description": "Optional. The number of boost VMs that the system should keep idle so that workstations can be boosted quickly. Defaults to `0`.", "format": "int32", "type": "integer"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "Container": {"description": "A Docker container.", "id": "Container", "properties": {"args": {"description": "Optional. Arguments passed to the entrypoint.", "items": {"type": "string"}, "type": "array"}, "command": {"description": "Optional. If set, overrides the default ENTRYPOINT specified by the image.", "items": {"type": "string"}, "type": "array"}, "env": {"additionalProperties": {"type": "string"}, "description": "Optional. Environment variables passed to the container's entrypoint.", "type": "object"}, "image": {"description": "Optional. A Docker container image that defines a custom environment. Cloud Workstations provides a number of [preconfigured images](https://cloud.google.com/workstations/docs/preconfigured-base-images), but you can create your own [custom container images](https://cloud.google.com/workstations/docs/custom-container-images). If using a private image, the `host.gceInstance.serviceAccount` field must be specified in the workstation configuration. If using a custom container image, the service account must have [Artifact Registry Reader](https://cloud.google.com/artifact-registry/docs/access-control#roles) permission to pull the specified image. Otherwise, the image must be publicly accessible.", "type": "string"}, "runAsUser": {"description": "Optional. If set, overrides the USER specified in the image with the given uid.", "format": "int32", "type": "integer"}, "workingDir": {"description": "Optional. If set, overrides the default DIR specified by the image.", "type": "string"}}, "type": "object"}, "CustomerEncryptionKey": {"description": "A customer-managed encryption key (CMEK) for the Compute Engine resources of the associated workstation configuration. Specify the name of your Cloud KMS encryption key and the default service account. We recommend that you use a separate service account and follow [Cloud KMS best practices](https://cloud.google.com/kms/docs/separation-of-duties).", "id": "CustomerEncryptionKey", "properties": {"kmsKey": {"description": "Immutable. The name of the Google Cloud KMS encryption key. For example, `\"projects/PROJECT_ID/locations/REGION/keyRings/KEY_RING/cryptoKeys/KEY_NAME\"`. The key must be in the same region as the workstation configuration.", "type": "string"}, "kmsKeyServiceAccount": {"description": "Immutable. The service account to use with the specified KMS key. We recommend that you use a separate service account and follow KMS best practices. For more information, see [Separation of duties](https://cloud.google.com/kms/docs/separation-of-duties) and `gcloud kms keys add-iam-policy-binding` [`--member`](https://cloud.google.com/sdk/gcloud/reference/kms/keys/add-iam-policy-binding#--member).", "type": "string"}}, "type": "object"}, "DomainConfig": {"description": "Configuration options for a custom domain.", "id": "DomainConfig", "properties": {"domain": {"description": "Immutable. Domain used by Workstations for HTTP ingress.", "type": "string"}}, "type": "object"}, "EphemeralDirectory": {"description": "An ephemeral directory which won't persist across workstation sessions. It is freshly created on every workstation start operation.", "id": "EphemeralDirectory", "properties": {"gcePd": {"$ref": "GcePersistentDisk", "description": "An EphemeralDirectory backed by a Compute Engine persistent disk."}, "mountPath": {"description": "Required. Location of this directory in the running workstation.", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GceConfidentialInstanceConfig": {"description": "A set of Compute Engine Confidential VM instance options.", "id": "GceConfidentialInstanceConfig", "properties": {"enableConfidentialCompute": {"description": "Optional. Whether the instance has confidential compute enabled.", "type": "boolean"}}, "type": "object"}, "GceInstance": {"description": "A runtime using a Compute Engine instance.", "id": "GceInstance", "properties": {"accelerators": {"description": "Optional. A list of the type and count of accelerator cards attached to the instance.", "items": {"$ref": "Accelerator"}, "type": "array"}, "boostConfigs": {"description": "Optional. A list of the boost configurations that workstations created using this workstation configuration are allowed to use. If specified, users will have the option to choose from the list of boost configs when starting a workstation.", "items": {"$ref": "BoostConfig"}, "type": "array"}, "bootDiskSizeGb": {"description": "Optional. The size of the boot disk for the VM in gigabytes (GB). The minimum boot disk size is `30` GB. Defaults to `50` GB.", "format": "int32", "type": "integer"}, "confidentialInstanceConfig": {"$ref": "GceConfidentialInstanceConfig", "description": "Optional. A set of Compute Engine Confidential VM instance options."}, "disablePublicIpAddresses": {"description": "Optional. When set to true, disables public IP addresses for VMs. If you disable public IP addresses, you must set up Private Google Access or Cloud NAT on your network. If you use Private Google Access and you use `private.googleapis.com` or `restricted.googleapis.com` for Container Registry and Artifact Registry, make sure that you set up DNS records for domains `*.gcr.io` and `*.pkg.dev`. Defaults to false (VMs have public IP addresses).", "type": "boolean"}, "disableSsh": {"description": "Optional. Whether to disable SSH access to the VM.", "type": "boolean"}, "enableNestedVirtualization": {"description": "Optional. Whether to enable nested virtualization on Cloud Workstations VMs created using this workstation configuration. Defaults to false. Nested virtualization lets you run virtual machine (VM) instances inside your workstation. Before enabling nested virtualization, consider the following important considerations. Cloud Workstations instances are subject to the [same restrictions as Compute Engine instances](https://cloud.google.com/compute/docs/instances/nested-virtualization/overview#restrictions): * **Organization policy**: projects, folders, or organizations may be restricted from creating nested VMs if the **Disable VM nested virtualization** constraint is enforced in the organization policy. For more information, see the Compute Engine section, [Checking whether nested virtualization is allowed](https://cloud.google.com/compute/docs/instances/nested-virtualization/managing-constraint#checking_whether_nested_virtualization_is_allowed). * **Performance**: nested VMs might experience a 10% or greater decrease in performance for workloads that are CPU-bound and possibly greater than a 10% decrease for workloads that are input/output bound. * **Machine Type**: nested virtualization can only be enabled on workstation configurations that specify a machine_type in the N1 or N2 machine series.", "type": "boolean"}, "machineType": {"description": "Optional. The type of machine to use for VM instances—for example, `\"e2-standard-4\"`. For more information about machine types that Cloud Workstations supports, see the list of [available machine types](https://cloud.google.com/workstations/docs/available-machine-types).", "type": "string"}, "poolSize": {"description": "Optional. The number of VMs that the system should keep idle so that new workstations can be started quickly for new users. Defaults to `0` in the API.", "format": "int32", "type": "integer"}, "pooledInstances": {"description": "Output only. Number of instances currently available in the pool for faster workstation startup.", "format": "int32", "readOnly": true, "type": "integer"}, "serviceAccount": {"description": "Optional. The email address of the service account for Cloud Workstations VMs created with this configuration. When specified, be sure that the service account has `logging.logEntries.create` and `monitoring.timeSeries.create` permissions on the project so it can write logs out to Cloud Logging. If using a custom container image, the service account must have [Artifact Registry Reader](https://cloud.google.com/artifact-registry/docs/access-control#roles) permission to pull the specified image. If you as the administrator want to be able to `ssh` into the underlying VM, you need to set this value to a service account for which you have the `iam.serviceAccounts.actAs` permission. Conversely, if you don't want anyone to be able to `ssh` into the underlying VM, use a service account where no one has that permission. If not set, VMs run with a service account provided by the Cloud Workstations service, and the image must be publicly accessible.", "type": "string"}, "serviceAccountScopes": {"description": "Optional. Scopes to grant to the service_account. When specified, users of workstations under this configuration must have `iam.serviceAccounts.actAs` on the service account.", "items": {"type": "string"}, "type": "array"}, "shieldedInstanceConfig": {"$ref": "GceShieldedInstanceConfig", "description": "Optional. A set of Compute Engine Shielded instance options."}, "tags": {"description": "Optional. Network tags to add to the Compute Engine VMs backing the workstations. This option applies [network tags](https://cloud.google.com/vpc/docs/add-remove-network-tags) to VMs created with this configuration. These network tags enable the creation of [firewall rules](https://cloud.google.com/workstations/docs/configure-firewall-rules).", "items": {"type": "string"}, "type": "array"}, "vmTags": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource manager tags to be bound to this instance. Tag keys and values have the same definition as [resource manager tags](https://cloud.google.com/resource-manager/docs/tags/tags-overview). Keys must be in the format `tagKeys/{tag_key_id}`, and values are in the format `tagValues/456`.", "type": "object"}}, "type": "object"}, "GceInstanceHost": {"description": "The Compute Engine instance host.", "id": "GceInstanceHost", "properties": {"id": {"description": "Optional. Output only. The ID of the Compute Engine instance.", "readOnly": true, "type": "string"}, "name": {"description": "Optional. Output only. The name of the Compute Engine instance.", "readOnly": true, "type": "string"}, "zone": {"description": "Optional. Output only. The zone of the Compute Engine instance.", "readOnly": true, "type": "string"}}, "type": "object"}, "GcePersistentDisk": {"description": "An EphemeralDirectory is backed by a Compute Engine persistent disk.", "id": "GcePersistentDisk", "properties": {"diskType": {"description": "Optional. Type of the disk to use. Defaults to `\"pd-standard\"`.", "type": "string"}, "readOnly": {"description": "Optional. Whether the disk is read only. If true, the disk may be shared by multiple VMs and source_snapshot must be set.", "type": "boolean"}, "sourceImage": {"description": "Optional. Name of the disk image to use as the source for the disk. Must be empty if source_snapshot is set. Updating source_image will update content in the ephemeral directory after the workstation is restarted. Only file systems supported by Container-Optimized OS (COS) are explicitly supported. For a list of supported file systems, please refer to the [COS documentation](https://cloud.google.com/container-optimized-os/docs/concepts/supported-filesystems). This field is mutable.", "type": "string"}, "sourceSnapshot": {"description": "Optional. Name of the snapshot to use as the source for the disk. Must be empty if source_image is set. Must be empty if read_only is false. Updating source_snapshot will update content in the ephemeral directory after the workstation is restarted. Only file systems supported by Container-Optimized OS (COS) are explicitly supported. For a list of supported file systems, see [the filesystems available in Container-Optimized OS](https://cloud.google.com/container-optimized-os/docs/concepts/supported-filesystems). This field is mutable.", "type": "string"}}, "type": "object"}, "GceRegionalPersistentDisk": {"description": "A Persistent Directory backed by a Compute Engine regional persistent disk. The persistent_directories field is repeated, but it may contain only one entry. It creates a [persistent disk](https://cloud.google.com/compute/docs/disks/persistent-disks) that mounts to the workstation VM at `/home` when the session starts and detaches when the session ends. If this field is empty, workstations created with this configuration do not have a persistent home directory.", "id": "GceRegionalPersistentDisk", "properties": {"diskType": {"description": "Optional. The [type of the persistent disk](https://cloud.google.com/compute/docs/disks#disk-types) for the home directory. Defaults to `\"pd-standard\"`.", "type": "string"}, "fsType": {"description": "Optional. Type of file system that the disk should be formatted with. The workstation image must support this file system type. Must be empty if source_snapshot is set. Defaults to `\"ext4\"`.", "type": "string"}, "reclaimPolicy": {"description": "Optional. Whether the persistent disk should be deleted when the workstation is deleted. Valid values are `DELETE` and `RETAIN`. Defaults to `DELETE`.", "enum": ["RECLAIM_POLICY_UNSPECIFIED", "DELETE", "RETAIN"], "enumDescriptions": ["Do not use.", "Delete the persistent disk when deleting the workstation.", "Keep the persistent disk when deleting the workstation. An administrator must manually delete the disk."], "type": "string"}, "sizeGb": {"description": "Optional. The GB capacity of a persistent home directory for each workstation created with this configuration. Must be empty if source_snapshot is set. Valid values are `10`, `50`, `100`, `200`, `500`, or `1000`. Defaults to `200`. If less than `200` GB, the disk_type must be `\"pd-balanced\"` or `\"pd-ssd\"`.", "format": "int32", "type": "integer"}, "sourceSnapshot": {"description": "Optional. Name of the snapshot to use as the source for the disk. If set, size_gb and fs_type must be empty. Must be formatted as ext4 file system with no partitions.", "type": "string"}}, "type": "object"}, "GceShieldedInstanceConfig": {"description": "A set of Compute Engine Shielded instance options.", "id": "GceShieldedInstanceConfig", "properties": {"enableIntegrityMonitoring": {"description": "Optional. Whether the instance has integrity monitoring enabled.", "type": "boolean"}, "enableSecureBoot": {"description": "Optional. Whether the instance has Secure Boot enabled.", "type": "boolean"}, "enableVtpm": {"description": "Optional. Whether the instance has the vTPM enabled.", "type": "boolean"}}, "type": "object"}, "GenerateAccessTokenRequest": {"description": "Request message for GenerateAccessToken.", "id": "GenerateAccessTokenRequest", "properties": {"expireTime": {"description": "Desired expiration time of the access token. This value must be at most 24 hours in the future. If a value is not specified, the token's expiration time will be set to a default value of 1 hour in the future.", "format": "google-datetime", "type": "string"}, "port": {"description": "Optional. Port for which the access token should be generated. If specified, the generated access token grants access only to the specified port of the workstation. If specified, values must be within the range [1 - 65535]. If not specified, the generated access token grants access to all ports of the workstation.", "format": "int32", "type": "integer"}, "ttl": {"description": "Desired lifetime duration of the access token. This value must be at most 24 hours. If a value is not specified, the token's lifetime will be set to a default value of 1 hour.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GenerateAccessTokenResponse": {"description": "Response message for GenerateAccessToken.", "id": "GenerateAccessTokenResponse", "properties": {"accessToken": {"description": "The generated bearer access token. To use this token, include it in an Authorization header of an HTTP request sent to the associated workstation's hostname—for example, `Authorization: Bearer `.", "type": "string"}, "expireTime": {"description": "Time at which the generated token will expire.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "Host": {"description": "Runtime host for a workstation.", "id": "Host", "properties": {"gceInstance": {"$ref": "GceInstance", "description": "Specifies a Compute Engine instance as the host."}}, "type": "object"}, "HttpOptions": {"description": "HTTP options for the running workstations.", "id": "HttpOptions", "properties": {"allowedUnauthenticatedCorsPreflightRequests": {"description": "Optional. By default, the workstations service makes sure that all requests to the workstation are authenticated. CORS preflight requests do not include cookies or custom headers, and so are considered unauthenticated and blocked by the workstations service. Enabling this option allows these unauthenticated CORS preflight requests through to the workstation, where it becomes the responsibility of the destination server in the workstation to validate the request.", "type": "boolean"}, "disableLocalhostReplacement": {"description": "Optional. By default, the workstations service replaces references to localhost, 127.0.0.1, and 0.0.0.0 with the workstation's hostname in http responses from the workstation so that applications under development run properly on the workstation. This may intefere with some applications, and so this option allows that behavior to be disabled.", "type": "boolean"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListUsableWorkstationConfigsResponse": {"description": "Response message for ListUsableWorkstationConfigs.", "id": "ListUsableWorkstationConfigsResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "Unreachable resources.", "items": {"type": "string"}, "type": "array"}, "workstationConfigs": {"description": "The requested configs.", "items": {"$ref": "WorkstationConfig"}, "type": "array"}}, "type": "object"}, "ListUsableWorkstationsResponse": {"description": "Response message for ListUsableWorkstations.", "id": "ListUsableWorkstationsResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "Unreachable resources.", "items": {"type": "string"}, "type": "array"}, "workstations": {"description": "The requested workstations.", "items": {"$ref": "Workstation"}, "type": "array"}}, "type": "object"}, "ListWorkstationClustersResponse": {"description": "Response message for ListWorkstationClusters.", "id": "ListWorkstationClustersResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "Unreachable resources.", "items": {"type": "string"}, "type": "array"}, "workstationClusters": {"description": "The requested workstation clusters.", "items": {"$ref": "WorkstationCluster"}, "type": "array"}}, "type": "object"}, "ListWorkstationConfigsResponse": {"description": "Response message for ListWorkstationConfigs.", "id": "ListWorkstationConfigsResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "Unreachable resources.", "items": {"type": "string"}, "type": "array"}, "workstationConfigs": {"description": "The requested configs.", "items": {"$ref": "WorkstationConfig"}, "type": "array"}}, "type": "object"}, "ListWorkstationsResponse": {"description": "Response message for ListWorkstations.", "id": "ListWorkstationsResponse", "properties": {"nextPageToken": {"description": "Optional. Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "Optional. Unreachable resources.", "items": {"type": "string"}, "type": "array"}, "workstations": {"description": "The requested workstations.", "items": {"$ref": "Workstation"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Metadata for long-running operations.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time that the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. Time that the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "PersistentDirectory": {"description": "A directory to persist across workstation sessions. Updates to this field will not update existing workstations and will only take effect on new workstations.", "id": "PersistentDirectory", "properties": {"gcePd": {"$ref": "GceRegionalPersistentDisk", "description": "A PersistentDirectory backed by a Compute Engine persistent disk."}, "mountPath": {"description": "Optional. Location of this directory in the running workstation.", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PortRange": {"description": "A PortRange defines a range of ports. Both first and last are inclusive. To specify a single port, both first and last should be the same.", "id": "PortRange", "properties": {"first": {"description": "Required. Starting port number for the current range of ports. Valid ports are 22, 80, and ports within the range 1024-65535.", "format": "int32", "type": "integer"}, "last": {"description": "Required. Ending port number for the current range of ports. Valid ports are 22, 80, and ports within the range 1024-65535.", "format": "int32", "type": "integer"}}, "type": "object"}, "PrivateClusterConfig": {"description": "Configuration options for private workstation clusters.", "id": "PrivateClusterConfig", "properties": {"allowedProjects": {"description": "Optional. Additional projects that are allowed to attach to the workstation cluster's service attachment. By default, the workstation cluster's project and the VPC host project (if different) are allowed.", "items": {"type": "string"}, "type": "array"}, "clusterHostname": {"description": "Output only. Hostname for the workstation cluster. This field will be populated only when private endpoint is enabled. To access workstations in the workstation cluster, create a new DNS zone mapping this domain name to an internal IP address and a forwarding rule mapping that address to the service attachment.", "readOnly": true, "type": "string"}, "enablePrivateEndpoint": {"description": "Immutable. Whether Workstations endpoint is private.", "type": "boolean"}, "serviceAttachmentUri": {"description": "Output only. Service attachment URI for the workstation cluster. The service attachment is created when private endpoint is enabled. To access workstations in the workstation cluster, configure access to the managed service using [Private Service Connect](https://cloud.google.com/vpc/docs/configure-private-service-connect-services).", "readOnly": true, "type": "string"}}, "type": "object"}, "ReadinessCheck": {"description": "A readiness check to be performed on a workstation.", "id": "ReadinessCheck", "properties": {"path": {"description": "Optional. Path to which the request should be sent.", "type": "string"}, "port": {"description": "Optional. Port to which the request should be sent.", "format": "int32", "type": "integer"}}, "type": "object"}, "RuntimeHost": {"description": "Runtime host for the workstation.", "id": "RuntimeHost", "properties": {"gceInstanceHost": {"$ref": "GceInstanceHost", "description": "Specifies a Compute Engine instance as the host."}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "StartWorkstationRequest": {"description": "Request message for StartWorkstation.", "id": "StartWorkstationRequest", "properties": {"boostConfig": {"description": "Optional. If set, the workstation starts using the boost configuration with the specified ID.", "type": "string"}, "etag": {"description": "Optional. If set, the request will be rejected if the latest version of the workstation on the server does not have this ETag.", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "type": "boolean"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StopWorkstationRequest": {"description": "Request message for StopWorkstation.", "id": "StopWorkstationRequest", "properties": {"etag": {"description": "Optional. If set, the request will be rejected if the latest version of the workstation on the server does not have this ETag.", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually apply it.", "type": "boolean"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Workstation": {"description": "A single instance of a developer workstation with its own persistent storage.", "id": "Workstation", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Client-specified annotations.", "type": "object"}, "boostConfigs": {"description": "Output only. List of available boost configuration IDs that this workstation can be boosted up to.", "items": {"$ref": "WorkstationBoostConfig"}, "readOnly": true, "type": "array"}, "conditions": {"description": "Output only. Status conditions describing the workstation's current state.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. Time when this workstation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "degraded": {"description": "Output only. Whether this workstation is in degraded mode, in which case it may require user action to restore full functionality. The conditions field contains detailed information about the status of the workstation.", "readOnly": true, "type": "boolean"}, "deleteTime": {"description": "Output only. Time when this workstation was soft-deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. Human-readable name for this workstation.", "type": "string"}, "env": {"additionalProperties": {"type": "string"}, "description": "Optional. Environment variables passed to the workstation container's entrypoint.", "type": "object"}, "etag": {"description": "Optional. Checksum computed by the server. May be sent on update and delete requests to make sure that the client has an up-to-date value before proceeding.", "type": "string"}, "host": {"description": "Output only. Host to which clients can send HTTPS traffic that will be received by the workstation. Authorized traffic will be received to the workstation as HTTP on port 80. To send traffic to a different port, clients may prefix the host with the destination port in the format `{port}-{host}`.", "readOnly": true, "type": "string"}, "kmsKey": {"description": "Output only. The name of the Google Cloud KMS encryption key used to encrypt this workstation. The KMS key can only be configured in the WorkstationConfig. The expected format is `projects/*/locations/*/keyRings/*/cryptoKeys/*`.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. [Labels](https://cloud.google.com/workstations/docs/label-resources) that are applied to the workstation and that are also propagated to the underlying Compute Engine resources.", "type": "object"}, "name": {"description": "Identifier. Full name of this workstation.", "type": "string"}, "reconciling": {"description": "Output only. Indicates whether this workstation is currently being updated to match its intended state.", "readOnly": true, "type": "boolean"}, "runtimeHost": {"$ref": "RuntimeHost", "description": "Optional. Output only. Runtime host for the workstation when in STATE_RUNNING.", "readOnly": true}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "sourceWorkstation": {"description": "Optional. The source workstation from which this workstation's persistent directories were cloned on creation.", "type": "string"}, "startTime": {"description": "Output only. Time when this workstation was most recently successfully started, regardless of the workstation's initial state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Current state of the workstation.", "enum": ["STATE_UNSPECIFIED", "STATE_STARTING", "STATE_RUNNING", "STATE_STOPPING", "STATE_STOPPED"], "enumDescriptions": ["Do not use.", "The workstation is not yet ready to accept requests from users but will be soon.", "The workstation is ready to accept requests from users.", "The workstation is being stopped.", "The workstation is stopped and will not be able to receive requests until it is started."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. A system-assigned unique identifier for this workstation.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when this workstation was most recently updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "WorkstationBoostConfig": {"description": "Boost configuration for this workstation. This object is populated from the parent workstation configuration.", "id": "WorkstationBoostConfig", "properties": {"id": {"description": "Output only. Boost configuration ID.", "readOnly": true, "type": "string"}}, "type": "object"}, "WorkstationCluster": {"description": "A workstation cluster resource in the Cloud Workstations API. Defines a group of workstations in a particular region and the VPC network they're attached to.", "id": "WorkstationCluster", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Client-specified annotations.", "type": "object"}, "conditions": {"description": "Output only. Status conditions describing the workstation cluster's current state.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "controlPlaneIp": {"description": "Output only. The private IP address of the control plane for this workstation cluster. Workstation VMs need access to this IP address to work with the service, so make sure that your firewall rules allow egress from the workstation VMs to this address.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time when this workstation cluster was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "degraded": {"description": "Output only. Whether this workstation cluster is in degraded mode, in which case it may require user action to restore full functionality. The conditions field contains detailed information about the status of the cluster.", "readOnly": true, "type": "boolean"}, "deleteTime": {"description": "Output only. Time when this workstation cluster was soft-deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. Human-readable name for this workstation cluster.", "type": "string"}, "domainConfig": {"$ref": "DomainConfig", "description": "Optional. Configuration options for a custom domain."}, "etag": {"description": "Optional. Checksum computed by the server. May be sent on update and delete requests to make sure that the client has an up-to-date value before proceeding.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. [Labels](https://cloud.google.com/workstations/docs/label-resources) that are applied to the workstation cluster and that are also propagated to the underlying Compute Engine resources.", "type": "object"}, "name": {"description": "Identifier. Full name of this workstation cluster.", "type": "string"}, "network": {"description": "Immutable. Name of the Compute Engine network in which instances associated with this workstation cluster will be created.", "type": "string"}, "privateClusterConfig": {"$ref": "PrivateClusterConfig", "description": "Optional. Configuration for private workstation cluster."}, "reconciling": {"description": "Output only. Indicates whether this workstation cluster is currently being updated to match its intended state.", "readOnly": true, "type": "boolean"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "subnetwork": {"description": "Immutable. Name of the Compute Engine subnetwork in which instances associated with this workstation cluster will be created. Must be part of the subnetwork specified for this workstation cluster.", "type": "string"}, "tags": {"additionalProperties": {"type": "string"}, "description": "Optional. Input only. Immutable. Tag keys/values directly bound to this resource. For example: \"123/environment\": \"production\", \"123/costCenter\": \"marketing\"", "type": "object"}, "uid": {"description": "Output only. A system-assigned unique identifier for this workstation cluster.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when this workstation cluster was most recently updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "WorkstationConfig": {"description": "A workstation configuration resource in the Cloud Workstations API. Workstation configurations act as templates for workstations. The workstation configuration defines details such as the workstation virtual machine (VM) instance type, persistent storage, container image defining environment, which IDE or Code Editor to use, and more. Administrators and platform teams can also use [Identity and Access Management (IAM)](https://cloud.google.com/iam/docs/overview) rules to grant access to teams or to individual developers.", "id": "WorkstationConfig", "properties": {"allowedPorts": {"description": "Optional. A list of PortRanges specifying single ports or ranges of ports that are externally accessible in the workstation. Allowed ports must be one of 22, 80, or within range 1024-65535. If not specified defaults to ports 22, 80, and ports 1024-65535.", "items": {"$ref": "PortRange"}, "type": "array"}, "annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Client-specified annotations.", "type": "object"}, "conditions": {"description": "Output only. Status conditions describing the workstation configuration's current state.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "container": {"$ref": "Container", "description": "Optional. Container that runs upon startup for each workstation using this workstation configuration."}, "createTime": {"description": "Output only. Time when this workstation configuration was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "degraded": {"description": "Output only. Whether this workstation configuration is in degraded mode, in which case it may require user action to restore full functionality. The conditions field contains detailed information about the status of the configuration.", "readOnly": true, "type": "boolean"}, "deleteTime": {"description": "Output only. Time when this workstation configuration was soft-deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "disableTcpConnections": {"description": "Optional. Disables support for plain TCP connections in the workstation. By default the service supports TCP connections through a websocket relay. Setting this option to true disables that relay, which prevents the usage of services that require plain TCP connections, such as SSH. When enabled, all communication must occur over HTTPS or WSS.", "type": "boolean"}, "displayName": {"description": "Optional. Human-readable name for this workstation configuration.", "type": "string"}, "enableAuditAgent": {"description": "Optional. Whether to enable Linux `auditd` logging on the workstation. When enabled, a service_account must also be specified that has `roles/logging.logWriter` and `roles/monitoring.metricWriter` on the project. Operating system audit logging is distinct from [Cloud Audit Logs](https://cloud.google.com/workstations/docs/audit-logging) and [Container output logging](https://cloud.google.com/workstations/docs/container-output-logging#overview). Operating system audit logs are available in the [Cloud Logging](https://cloud.google.com/logging/docs) console by querying: resource.type=\"gce_instance\" log_name:\"/logs/linux-auditd\"", "type": "boolean"}, "encryptionKey": {"$ref": "CustomerEncryptionKey", "description": "Immutable. Encrypts resources of this workstation configuration using a customer-managed encryption key (CMEK). If specified, the boot disk of the Compute Engine instance and the persistent disk are encrypted using this encryption key. If this field is not set, the disks are encrypted using a generated key. Customer-managed encryption keys do not protect disk metadata. If the customer-managed encryption key is rotated, when the workstation instance is stopped, the system attempts to recreate the persistent disk with the new version of the key. Be sure to keep older versions of the key until the persistent disk is recreated. Otherwise, data on the persistent disk might be lost. If the encryption key is revoked, the workstation session automatically stops within 7 hours. Immutable after the workstation configuration is created."}, "ephemeralDirectories": {"description": "Optional. Ephemeral directories which won't persist across workstation sessions.", "items": {"$ref": "EphemeralDirectory"}, "type": "array"}, "etag": {"description": "Optional. Checksum computed by the server. May be sent on update and delete requests to make sure that the client has an up-to-date value before proceeding.", "type": "string"}, "grantWorkstationAdminRoleOnCreate": {"description": "Optional. Grant creator of a workstation `roles/workstations.policyAdmin` role along with `roles/workstations.user` role on the workstation created by them. This allows workstation users to share access to either their entire workstation, or individual ports. Defaults to false.", "type": "boolean"}, "host": {"$ref": "Host", "description": "Optional. Runtime host for the workstation."}, "httpOptions": {"$ref": "HttpOptions", "description": "Optional. HTTP options that customize the behavior of the workstation service's HTTP proxy."}, "idleTimeout": {"description": "Optional. Number of seconds to wait before automatically stopping a workstation after it last received user traffic. A value of `\"0s\"` indicates that Cloud Workstations VMs created with this configuration should never time out due to idleness. Provide [duration](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#duration) terminated by `s` for seconds—for example, `\"7200s\"` (2 hours). The default is `\"1200s\"` (20 minutes).", "format": "google-duration", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. [Labels](https://cloud.google.com/workstations/docs/label-resources) that are applied to the workstation configuration and that are also propagated to the underlying Compute Engine resources.", "type": "object"}, "maxUsableWorkstations": {"description": "Optional. Maximum number of workstations under this configuration a user can have `workstations.workstation.use` permission on. Only enforced on CreateWorkstation API calls on the user issuing the API request. Can be overridden by: - granting a user workstations.workstationConfigs.exemptMaxUsableWorkstationLimit permission, or - having a user with that permission create a workstation and granting another user `workstations.workstation.use` permission on that workstation. If not specified, defaults to `0`, which indicates unlimited.", "format": "int32", "type": "integer"}, "name": {"description": "Identifier. Full name of this workstation configuration.", "type": "string"}, "persistentDirectories": {"description": "Optional. Directories to persist across workstation sessions.", "items": {"$ref": "PersistentDirectory"}, "type": "array"}, "readinessChecks": {"description": "Optional. Readiness checks to perform when starting a workstation using this workstation configuration. Mark a workstation as running only after all specified readiness checks return 200 status codes.", "items": {"$ref": "ReadinessCheck"}, "type": "array"}, "reconciling": {"description": "Output only. Indicates whether this workstation configuration is currently being updated to match its intended state.", "readOnly": true, "type": "boolean"}, "replicaZones": {"description": "Optional. Immutable. Specifies the zones used to replicate the VM and disk resources within the region. If set, exactly two zones within the workstation cluster's region must be specified—for example, `['us-central1-a', 'us-central1-f']`. If this field is empty, two default zones within the region are used. Immutable after the workstation configuration is created.", "items": {"type": "string"}, "type": "array"}, "runningTimeout": {"description": "Optional. Number of seconds that a workstation can run until it is automatically shut down. We recommend that workstations be shut down daily to reduce costs and so that security updates can be applied upon restart. The idle_timeout and running_timeout fields are independent of each other. Note that the running_timeout field shuts down VMs after the specified time, regardless of whether or not the VMs are idle. Provide duration terminated by `s` for seconds—for example, `\"54000s\"` (15 hours). Defaults to `\"43200s\"` (12 hours). A value of `\"0s\"` indicates that workstations using this configuration should never time out. If encryption_key is set, it must be greater than `\"0s\"` and less than `\"86400s\"` (24 hours). Warning: A value of `\"0s\"` indicates that Cloud Workstations VMs created with this configuration have no maximum running time. This is strongly discouraged because you incur costs and will not pick up security updates.", "format": "google-duration", "type": "string"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "uid": {"description": "Output only. A system-assigned unique identifier for this workstation configuration.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when this workstation configuration was most recently updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Workstations API", "version": "v1beta", "version_module": true}