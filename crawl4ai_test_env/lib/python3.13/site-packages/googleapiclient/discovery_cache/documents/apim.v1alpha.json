{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://apim.googleapis.com/", "batchPath": "batch", "canonicalName": "API Management", "description": "Enables users to discover shadow APIs in existing Google Cloud infrastructure.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/apigee/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "apim:v1alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://apim.mtls.googleapis.com/", "name": "apim", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "apim.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1alpha/projects/{projectsId}/locations", "httpMethod": "GET", "id": "apim.projects.locations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listApiObservationTags": {"description": "ListApiObservationTags lists all extant tags on any observation in the given project.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}:listApiObservationTags", "httpMethod": "GET", "id": "apim.projects.locations.listApiObservationTags", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of tags to return. The service may return fewer than this value. If unspecified, at most 10 tags will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListApiObservationTags` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiObservationTags` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of tags. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}:listApiObservationTags", "response": {"$ref": "ListApiObservationTagsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"observationJobs": {"methods": {"create": {"description": "CreateObservationJob creates a new ObservationJob but does not have any effecton its own. It is a configuration that can be used in an Observation Job to collect data about existing APIs.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs", "httpMethod": "POST", "id": "apim.projects.locations.observationJobs.create", "parameterOrder": ["parent"], "parameters": {"observationJobId": {"description": "Required. The ID to use for the Observation Job. This value should be 4-63 characters, and valid characters are /a-z-/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this ObservationJob will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/observationJobs", "request": {"$ref": "ObservationJob"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "DeleteObservationJob deletes an ObservationJob. This method will fail if the observation job is currently being used by any ObservationSource, even if not enabled.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}", "httpMethod": "DELETE", "id": "apim.projects.locations.observationJobs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource Format: projects/{project}/locations/{location}/observationJobs/{observation_job}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "disable": {"description": "Disables the given ObservationJob.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}:disable", "httpMethod": "POST", "id": "apim.projects.locations.observationJobs.disable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ObservationJob to disable. Format: projects/{project}/locations/{location}/observationJobs/{job}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:disable", "request": {"$ref": "DisableObservationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enable": {"description": "Enables the given ObservationJob.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}:enable", "httpMethod": "POST", "id": "apim.projects.locations.observationJobs.enable", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ObservationJob to enable. Format: projects/{project}/locations/{location}/observationJobs/{job}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:enable", "request": {"$ref": "EnableObservationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "GetObservationJob retrieves a single ObservationJob by name.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}", "httpMethod": "GET", "id": "apim.projects.locations.observationJobs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ObservationJob to retrieve. Format: projects/{project}/locations/{location}/observationJobs/{job}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "ObservationJob"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "ListObservationJobs gets all ObservationJobs for a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs", "httpMethod": "GET", "id": "apim.projects.locations.observationJobs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of ObservationJobs to return. The service may return fewer than this value. If unspecified, at most 10 ObservationJobs will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListObservationJobs` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListObservationJobs` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of ObservationJobs. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/observationJobs", "response": {"$ref": "ListObservationJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"apiObservations": {"methods": {"batchEditTags": {"description": "BatchEditTagsApiObservations adds or removes Tags for ApiObservations.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}/apiObservations:batchEditTags", "httpMethod": "POST", "id": "apim.projects.locations.observationJobs.apiObservations.batchEditTags", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource shared by all ApiObservations being edited. Format: projects/{project}/locations/{location}/observationJobs/{observation_job}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/apiObservations:batchEditTags", "request": {"$ref": "BatchEditTagsApiObservationsRequest"}, "response": {"$ref": "BatchEditTagsApiObservationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "GetApiObservation retrieves a single ApiObservation by name.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}/apiObservations/{apiObservationsId}", "httpMethod": "GET", "id": "apim.projects.locations.observationJobs.apiObservations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ApiObservation to retrieve. Format: projects/{project}/locations/{location}/observationJobs/{observation_job}/apiObservations/{api_observation}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+/apiObservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "ApiObservation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "ListApiObservations gets all ApiObservations for a given project and location and ObservationJob.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}/apiObservations", "httpMethod": "GET", "id": "apim.projects.locations.observationJobs.apiObservations.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of ApiObservations to return. The service may return fewer than this value. If unspecified, at most 10 ApiObservations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListApiObservations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiObservations` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of ApiObservations. Format: projects/{project}/locations/{location}/observationJobs/{observation_job}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/apiObservations", "response": {"$ref": "ListApiObservationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"apiOperations": {"methods": {"get": {"description": "GetApiOperation retrieves a single ApiOperation by name.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}/apiObservations/{apiObservationsId}/apiOperations/{apiOperationsId}", "httpMethod": "GET", "id": "apim.projects.locations.observationJobs.apiObservations.apiOperations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ApiOperation to retrieve. Format: projects/{project}/locations/{location}/observationJobs/{observation_job}/apiObservations/{api_observation}/apiOperation/{api_operation}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+/apiObservations/[^/]+/apiOperations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "ApiOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "ListApiOperations gets all ApiOperations for a given project and location and ObservationJob and ApiObservation.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationJobs/{observationJobsId}/apiObservations/{apiObservationsId}/apiOperations", "httpMethod": "GET", "id": "apim.projects.locations.observationJobs.apiObservations.apiOperations.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of ApiOperations to return. The service may return fewer than this value. If unspecified, at most 10 ApiOperations will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListApiApiOperations` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListApiApiOperations` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of ApiOperations. Format: projects/{project}/locations/{location}/observationJobs/{observation_job}/apiObservations/{api_observation}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationJobs/[^/]+/apiObservations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/apiOperations", "response": {"$ref": "ListApiOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "observationSources": {"methods": {"create": {"description": "CreateObservationSource creates a new ObservationSource but does not affect any deployed infrastructure. It is a configuration that can be used in an Observation Job to collect data about APIs running in user's dataplane.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationSources", "httpMethod": "POST", "id": "apim.projects.locations.observationSources.create", "parameterOrder": ["parent"], "parameters": {"observationSourceId": {"description": "Required. The ID to use for the Observation Source. This value should be 4-63 characters, and valid characters are /a-z-/.", "location": "query", "type": "string"}, "parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/observationSources", "request": {"$ref": "ObservationSource"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "DeleteObservationSource deletes an observation source. This method will fail if the observation source is currently being used by any ObservationJob, even if not enabled.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationSources/{observationSourcesId}", "httpMethod": "DELETE", "id": "apim.projects.locations.observationSources.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource Format: projects/{project}/locations/{location}/observationSources/{source}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "GetObservationSource retrieves a single ObservationSource by name.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationSources/{observationSourcesId}", "httpMethod": "GET", "id": "apim.projects.locations.observationSources.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ObservationSource to retrieve. Format: projects/{project}/locations/{location}/observationSources/{source}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/observationSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "ObservationSource"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "ListObservationSources gets all ObservationSources for a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/observationSources", "httpMethod": "GET", "id": "apim.projects.locations.observationSources.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of ObservationSources to return. The service may return fewer than this value. If unspecified, at most 10 ObservationSources will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListObservationSources` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListObservationSources` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of ObservationSources. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/observationSources", "response": {"$ref": "ListObservationSourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "apim.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "apim.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "apim.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "apim.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20240717", "rootUrl": "https://apim.googleapis.com/", "schemas": {"ApiObservation": {"description": "Message describing ApiObservation object", "id": "ApiObservation", "properties": {"apiOperationCount": {"description": "The number of observed API Operations.", "format": "int64", "type": "string"}, "createTime": {"description": "Create time stamp", "format": "google-datetime", "type": "string"}, "hostname": {"description": "The hostname of requests processed for this Observation.", "type": "string"}, "lastEventDetectedTime": {"description": "Last event detected time stamp", "format": "google-datetime", "type": "string"}, "name": {"description": "Identifier. Name of resource", "type": "string"}, "serverIps": {"description": "The IP address (IPv4 or IPv6) of the origin server that the request was sent to. This field can include port information. Examples: `\"***********\"`, `\"********:80\"`, `\"FE80::0202:B3FF:FE1E:8329\"`.", "items": {"type": "string"}, "type": "array"}, "sourceLocations": {"description": "Location of the Observation Source, for example \"us-central1\" or \"europe-west1.\"", "items": {"type": "string"}, "type": "array"}, "style": {"description": "Style of ApiObservation", "enum": ["STYLE_UNSPECIFIED", "REST", "GRPC", "GRAPHQL"], "enumDescriptions": ["Unknown style", "Style is Rest API", "Style is Grpc API", "Style is GraphQL API"], "type": "string"}, "tags": {"description": "User-defined tags to organize and sort", "items": {"type": "string"}, "type": "array"}, "updateTime": {"description": "Update time stamp", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ApiOperation": {"description": "Message describing ApiOperation object", "id": "ApiOperation", "properties": {"count": {"description": "The number of occurrences of this API Operation.", "format": "int64", "type": "string"}, "firstSeenTime": {"description": "First seen time stamp", "format": "google-datetime", "type": "string"}, "httpOperation": {"$ref": "HttpOperation", "description": "An HTTP Operation."}, "lastSeenTime": {"description": "Last seen time stamp", "format": "google-datetime", "type": "string"}, "name": {"description": "Identifier. Name of resource", "type": "string"}}, "type": "object"}, "BatchEditTagsApiObservationsRequest": {"description": "Message for requesting batch edit tags for ApiObservations", "id": "BatchEditTagsApiObservationsRequest", "properties": {"requests": {"description": "Required. The request message specifying the resources to update. A maximum of 1000 apiObservations can be modified in a batch.", "items": {"$ref": "EditTagsApiObservationsRequest"}, "type": "array"}}, "type": "object"}, "BatchEditTagsApiObservationsResponse": {"description": "Message for response to edit Tags for ApiObservations", "id": "BatchEditTagsApiObservationsResponse", "properties": {"apiObservations": {"description": "ApiObservations that were changed", "items": {"$ref": "ApiObservation"}, "type": "array"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "DisableObservationJobRequest": {"description": "Message for disabling an ObservationJob", "id": "DisableObservationJobRequest", "properties": {}, "type": "object"}, "EditTagsApiObservationsRequest": {"description": "Message for requesting edit tags for ApiObservation", "id": "EditTagsApiObservationsRequest", "properties": {"apiObservationId": {"description": "Required. Identifier of ApiObservation need to be edit tags Format example: \"apigee.googleapis.com|us-west1|443\"", "type": "string"}, "tagActions": {"description": "Required. Tag actions to be applied", "items": {"$ref": "TagAction"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EnableObservationJobRequest": {"description": "Message for enabling an ObservationJob", "id": "EnableObservationJobRequest", "properties": {}, "type": "object"}, "GclbObservationSource": {"description": "The GCLB observation source.", "id": "GclbObservationSource", "properties": {"pscNetworkConfigs": {"description": "Required. The VPC networks where traffic will be observed. All load balancers within this network will be observed. Currently, this is limited to only one network.", "items": {"$ref": "GclbObservationSourcePscNetworkConfig"}, "type": "array"}}, "type": "object"}, "GclbObservationSourcePscNetworkConfig": {"description": "Network information for setting up a PSC connection.", "id": "GclbObservationSourcePscNetworkConfig", "properties": {"network": {"description": "Required. The VPC network. Format: `projects/{project_id}/global/networks/{network}`", "type": "string"}, "subnetwork": {"description": "Required. The subnetwork in the source region that will be used to connect to the Cloud Load Balancers via PSC NEGs. Must belong to `network`. Format: projects/{project_id}/regions/{region}/subnetworks/{subnet}", "type": "string"}}, "type": "object"}, "HttpOperation": {"description": "An HTTP-based API Operation, sometimes called a \"REST\" Operation.", "id": "HttpOperation", "properties": {"method": {"description": "HTTP Method.", "enum": ["HTTP_METHOD_UNSPECIFIED", "GET", "HEAD", "POST", "PUT", "PATCH", "DELETE", "TRACE", "OPTIONS", "CONNECT"], "enumDescriptions": ["Unspecified HTTP method", "GET HTTP method", "HEAD HTTP method", "POST HTTP method", "PUT HTTP method", "PATCH HTTP method", "DELETE HTTP method", "TRACE HTTP method", "OPTIONS HTTP method", "CONNECT HTTP method"], "type": "string"}, "path": {"description": "Path of the HTTP request.", "type": "string"}, "pathParams": {"description": "Path params of HttpOperation", "items": {"$ref": "HttpOperationPathParam"}, "type": "array"}, "queryParams": {"additionalProperties": {"$ref": "HttpOperationQueryParam"}, "description": "Query params of HttpOperation", "type": "object"}, "request": {"$ref": "HttpOperationHttpRequest", "description": "Request metadata."}, "response": {"$ref": "HttpOperationHttpResponse", "description": "Response metadata."}}, "type": "object"}, "HttpOperationHeader": {"description": "An aggregation of HTTP header occurrences.", "id": "HttpOperationHeader", "properties": {"count": {"description": "The number of occurrences of this Header across transactions.", "format": "int64", "type": "string"}, "dataType": {"description": "Data type of header", "enum": ["DATA_TYPE_UNSPECIFIED", "BOOL", "INTEGER", "FLOAT", "STRING", "UUID"], "enumDescriptions": ["Unspecified data type", "Boolean data type", "Integer data type", "Float data type", "String data type", "UUID data type"], "type": "string"}, "name": {"description": "Header name.", "type": "string"}}, "type": "object"}, "HttpOperationHttpRequest": {"description": "An aggregation of HTTP requests.", "id": "HttpOperationHttpRequest", "properties": {"headers": {"additionalProperties": {"$ref": "HttpOperationHeader"}, "description": "Unordered map from header name to header metadata", "type": "object"}}, "type": "object"}, "HttpOperationHttpResponse": {"description": "An aggregation of HTTP responses.", "id": "HttpOperationHttpResponse", "properties": {"headers": {"additionalProperties": {"$ref": "HttpOperationHeader"}, "description": "Unordered map from header name to header metadata", "type": "object"}, "responseCodes": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Map of status code to observed count", "type": "object"}}, "type": "object"}, "HttpOperationPathParam": {"description": "HTTP Path parameter.", "id": "HttpOperationPathParam", "properties": {"dataType": {"description": "Data type of path param", "enum": ["DATA_TYPE_UNSPECIFIED", "BOOL", "INTEGER", "FLOAT", "STRING", "UUID"], "enumDescriptions": ["Unspecified data type", "Boolean data type", "Integer data type", "Float data type", "String data type", "UUID data type"], "type": "string"}, "position": {"description": "Segment location in the path, 1-indexed", "format": "int32", "type": "integer"}}, "type": "object"}, "HttpOperationQueryParam": {"description": "An aggregation of HTTP query parameter occurrences.", "id": "HttpOperationQueryParam", "properties": {"count": {"description": "The number of occurrences of this query parameter across transactions.", "format": "int64", "type": "string"}, "dataType": {"description": "Data type of path param", "enum": ["DATA_TYPE_UNSPECIFIED", "BOOL", "INTEGER", "FLOAT", "STRING", "UUID"], "enumDescriptions": ["Unspecified data type", "Boolean data type", "Integer data type", "Float data type", "String data type", "UUID data type"], "type": "string"}, "name": {"description": "Name of query param", "type": "string"}}, "type": "object"}, "ListApiObservationTagsResponse": {"description": "Message for response to listing tags", "id": "ListApiObservationTagsResponse", "properties": {"apiObservationTags": {"description": "The tags from the specified project", "items": {"type": "string"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListApiObservationsResponse": {"description": "Message for response to listing ApiObservations", "id": "ListApiObservationsResponse", "properties": {"apiObservations": {"description": "The ApiObservation from the specified project and location and ObservationJobs.", "items": {"$ref": "ApiObservation"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListApiOperationsResponse": {"description": "Message for response to listing ApiOperations", "id": "ListApiOperationsResponse", "properties": {"apiOperations": {"description": "The ApiOperations from the specified project and location and ObservationJob and ApiObservation.", "items": {"$ref": "ApiOperation"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListObservationJobsResponse": {"description": "Message for response to listing ObservationJobs", "id": "ListObservationJobsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "observationJobs": {"description": "The ObservationJob from the specified project and location.", "items": {"$ref": "ObservationJob"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListObservationSourcesResponse": {"description": "Message for response to listing ObservationSources", "id": "ListObservationSourcesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "observationSources": {"description": "The ObservationSource from the specified project and location.", "items": {"$ref": "ObservationSource"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "ObservationJob": {"description": "Message describing ObservationJob object", "id": "ObservationJob", "properties": {"createTime": {"description": "Output only. [Output only] Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. name of resource Format: projects/{project}/locations/{location}/observationJobs/{observation_job}", "type": "string"}, "sources": {"description": "Optional. These should be of the same kind of source.", "items": {"type": "string"}, "type": "array"}, "state": {"description": "Output only. The observation job state", "enum": ["STATE_UNSPECIFIED", "CREATING", "ENABLING", "ENABLED", "DISABLING", "DISABLED", "DELETING", "ERROR"], "enumDescriptions": ["Unspecified state", "Job is in the creating state", "Job is in the enabling state", "Job is enabled", "Job is in the disabling state", "Job is disabled", "Job is being deleted", "Job is in an error state"], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. [Output only] Update time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ObservationSource": {"description": "Observation source configuration types", "id": "ObservationSource", "properties": {"createTime": {"description": "Output only. [Output only] Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "gclbObservationSource": {"$ref": "GclbObservationSource", "description": "The GCLB observation source"}, "name": {"description": "Identifier. name of resource For MVP, each region can only have 1 source.", "type": "string"}, "state": {"description": "Output only. The observation source state", "enum": ["STATE_UNSPECIFIED", "CREATING", "CREATED", "DELETING", "ERROR"], "enumDescriptions": ["Unspecified state", "Source is in the creating state", "Source has been created and is ready to use", "Source is being deleted", "Source is in an error state"], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. [Output only] Update time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TagAction": {"description": "Message for edit tag action", "id": "TagAction", "properties": {"action": {"description": "Required. Action to be applied", "enum": ["ACTION_UNSPECIFIED", "ADD", "REMOVE"], "enumDescriptions": ["Unspecified.", "Addition of a Tag.", "Removal of a Tag."], "type": "string"}, "tag": {"description": "Required. Tag to be added or removed", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "API Management API", "version": "v1alpha", "version_module": true}