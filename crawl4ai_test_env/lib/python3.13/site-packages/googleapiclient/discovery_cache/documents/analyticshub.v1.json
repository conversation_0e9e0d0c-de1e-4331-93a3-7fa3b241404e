{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/bigquery": {"description": "View and manage your data in Google BigQuery and see the email address for your Google Account"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://analyticshub.googleapis.com/", "batchPath": "batch", "canonicalName": "Analytics Hub", "description": "Exchange data and analytics assets securely and efficiently.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/bigquery/docs/analytics-hub-introduction", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "analyticshub:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://analyticshub.mtls.googleapis.com/", "name": "analyticshub", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"resources": {"locations": {"resources": {"dataExchanges": {"methods": {"list": {"description": "Lists all data exchanges from projects in a given organization and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/dataExchanges", "httpMethod": "GET", "id": "analyticshub.organizations.locations.dataExchanges.list", "parameterOrder": ["organization"], "parameters": {"organization": {"description": "Required. The organization resource path of the projects containing DataExchanges. e.g. `organizations/myorg/locations/us`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}}, "path": "v1/{+organization}/dataExchanges", "response": {"$ref": "ListOrgDataExchangesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "projects": {"resources": {"locations": {"resources": {"dataExchanges": {"methods": {"create": {"description": "Creates a new data exchange.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.create", "parameterOrder": ["parent"], "parameters": {"dataExchangeId": {"description": "Required. The ID of the data exchange. Must contain only Unicode letters, numbers (0-9), underscores (_). Max length: 100 bytes.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource path of the data exchange. e.g. `projects/myproject/locations/us`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/dataExchanges", "request": {"$ref": "DataExchange"}, "response": {"$ref": "DataExchange"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an existing data exchange.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}", "httpMethod": "DELETE", "id": "analyticshub.projects.locations.dataExchanges.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full name of the data exchange resource that you want to delete. For example, `projects/myproject/locations/us/dataExchanges/123`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a data exchange.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}", "httpMethod": "GET", "id": "analyticshub.projects.locations.dataExchanges.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the data exchange. e.g. `projects/myproject/locations/us/dataExchanges/123`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DataExchange"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}:getIamPolicy", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all data exchanges in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges", "httpMethod": "GET", "id": "analyticshub.projects.locations.dataExchanges.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource path of the data exchanges. e.g. `projects/myproject/locations/us`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/dataExchanges", "response": {"$ref": "ListDataExchangesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "listSubscriptions": {"description": "Lists all subscriptions on a given Data Exchange or Listing.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}:listSubscriptions", "httpMethod": "GET", "id": "analyticshub.projects.locations.dataExchanges.listSubscriptions", "parameterOrder": ["resource"], "parameters": {"includeDeletedSubscriptions": {"description": "If selected, includes deleted subscriptions in the response (up to 63 days after deletion).", "location": "query", "type": "boolean"}, "pageSize": {"description": "The maximum number of results to return in a single response page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call.", "location": "query", "type": "string"}, "resource": {"description": "Required. Resource name of the requested target. This resource may be either a Listing or a DataExchange. e.g. projects/123/locations/us/dataExchanges/456 OR e.g. projects/123/locations/us/dataExchanges/456/listings/789", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:listSubscriptions", "response": {"$ref": "ListSharedResourceSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing data exchange.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}", "httpMethod": "PATCH", "id": "analyticshub.projects.locations.dataExchanges.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the data exchange. e.g. `projects/myproject/locations/us/dataExchanges/123`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask specifies the fields to update in the data exchange resource. The fields specified in the `updateMask` are relative to the resource and are not a full request.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "DataExchange"}, "response": {"$ref": "DataExchange"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}:setIamPolicy", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "subscribe": {"description": "Creates a Subscription to a Data Clean Room. This is a long-running operation as it will create one or more linked datasets. Throws a Bad Request error if the Data Exchange does not contain any listings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}:subscribe", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.subscribe", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Data Exchange. e.g. `projects/publisherproject/locations/us/dataExchanges/123`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:subscribe", "request": {"$ref": "SubscribeDataExchangeRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the permissions that a caller has.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}:testIamPermissions", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"listings": {"methods": {"create": {"description": "Creates a new listing.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.listings.create", "parameterOrder": ["parent"], "parameters": {"listingId": {"description": "Required. The ID of the listing to create. Must contain only Unicode letters, numbers (0-9), underscores (_). Max length: 100 bytes.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource path of the listing. e.g. `projects/myproject/locations/us/dataExchanges/123`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/listings", "request": {"$ref": "Listing"}, "response": {"$ref": "Listing"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a listing.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings/{listingsId}", "httpMethod": "DELETE", "id": "analyticshub.projects.locations.dataExchanges.listings.delete", "parameterOrder": ["name"], "parameters": {"deleteCommercial": {"description": "Optional. If the listing is commercial then this field must be set to true, otherwise a failure is thrown. This acts as a safety guard to avoid deleting commercial listings accidentally.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Resource name of the listing to delete. e.g. `projects/myproject/locations/us/dataExchanges/123/listings/456`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+/listings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a listing.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings/{listingsId}", "httpMethod": "GET", "id": "analyticshub.projects.locations.dataExchanges.listings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the listing. e.g. `projects/myproject/locations/us/dataExchanges/123/listings/456`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+/listings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Listing"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings/{listingsId}:getIamPolicy", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.listings.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+/listings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all listings in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings", "httpMethod": "GET", "id": "analyticshub.projects.locations.dataExchanges.listings.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource path of the listing. e.g. `projects/myproject/locations/us/dataExchanges/123`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/listings", "response": {"$ref": "ListListingsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "listSubscriptions": {"description": "Lists all subscriptions on a given Data Exchange or Listing.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings/{listingsId}:listSubscriptions", "httpMethod": "GET", "id": "analyticshub.projects.locations.dataExchanges.listings.listSubscriptions", "parameterOrder": ["resource"], "parameters": {"includeDeletedSubscriptions": {"description": "If selected, includes deleted subscriptions in the response (up to 63 days after deletion).", "location": "query", "type": "boolean"}, "pageSize": {"description": "The maximum number of results to return in a single response page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call.", "location": "query", "type": "string"}, "resource": {"description": "Required. Resource name of the requested target. This resource may be either a Listing or a DataExchange. e.g. projects/123/locations/us/dataExchanges/456 OR e.g. projects/123/locations/us/dataExchanges/456/listings/789", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+/listings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:listSubscriptions", "response": {"$ref": "ListSharedResourceSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing listing.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings/{listingsId}", "httpMethod": "PATCH", "id": "analyticshub.projects.locations.dataExchanges.listings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the listing. e.g. `projects/myproject/locations/us/dataExchanges/123/listings/456`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+/listings/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask specifies the fields to update in the listing resource. The fields specified in the `updateMask` are relative to the resource and are not a full request.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Listing"}, "response": {"$ref": "Listing"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings/{listingsId}:setIamPolicy", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.listings.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+/listings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "subscribe": {"description": "Subscribes to a listing. Currently, with Analytics Hub, you can create listings that reference only BigQuery datasets. Upon subscription to a listing for a BigQuery dataset, Analytics Hub creates a linked dataset in the subscriber's project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings/{listingsId}:subscribe", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.listings.subscribe", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the listing that you want to subscribe to. e.g. `projects/myproject/locations/us/dataExchanges/123/listings/456`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+/listings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:subscribe", "request": {"$ref": "SubscribeListingRequest"}, "response": {"$ref": "SubscribeListingResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the permissions that a caller has.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataExchanges/{dataExchangesId}/listings/{listingsId}:testIamPermissions", "httpMethod": "POST", "id": "analyticshub.projects.locations.dataExchanges.listings.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataExchanges/[^/]+/listings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}}}, "subscriptions": {"methods": {"delete": {"description": "Deletes a subscription.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}", "httpMethod": "DELETE", "id": "analyticshub.projects.locations.subscriptions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the subscription to delete. e.g. projects/123/locations/us/subscriptions/456", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a Subscription.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}", "httpMethod": "GET", "id": "analyticshub.projects.locations.subscriptions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the subscription. e.g. projects/123/locations/us/subscriptions/456", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}:getIamPolicy", "httpMethod": "POST", "id": "analyticshub.projects.locations.subscriptions.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all subscriptions in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/subscriptions", "httpMethod": "GET", "id": "analyticshub.projects.locations.subscriptions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression for filtering the results of the request. Eligible fields for filtering are: + `listing` + `data_exchange` Alternatively, a literal wrapped in double quotes may be provided. This will be checked for an exact match against both fields above. In all cases, the full Data Exchange or Listing resource name must be provided. Some example of using filters: + data_exchange=\"projects/myproject/locations/us/dataExchanges/123\" + listing=\"projects/123/locations/us/dataExchanges/456/listings/789\" + \"projects/myproject/locations/us/dataExchanges/123\"", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource path of the subscription. e.g. projects/myproject/locations/us", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/subscriptions", "response": {"$ref": "ListSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "refresh": {"description": "Refreshes a Subscription to a Data Exchange. A Data Exchange can become stale when a publisher adds or removes data. This is a long-running operation as it may create many linked datasets.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}:refresh", "httpMethod": "POST", "id": "analyticshub.projects.locations.subscriptions.refresh", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Subscription to refresh. e.g. `projects/subscriberproject/locations/us/subscriptions/123`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:refresh", "request": {"$ref": "RefreshSubscriptionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "revoke": {"description": "Revokes a given subscription.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}:revoke", "httpMethod": "POST", "id": "analyticshub.projects.locations.subscriptions.revoke", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the subscription to revoke. e.g. projects/123/locations/us/subscriptions/456", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:revoke", "request": {"$ref": "RevokeSubscriptionRequest"}, "response": {"$ref": "RevokeSubscriptionResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}:setIamPolicy", "httpMethod": "POST", "id": "analyticshub.projects.locations.subscriptions.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250507", "rootUrl": "https://analyticshub.googleapis.com/", "schemas": {"AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AvroConfig": {"description": "Configuration for writing message data in Avro format. Message payloads and metadata will be written to files as an Avro binary.", "id": "AvroConfig", "properties": {"useTopicSchema": {"description": "Optional. When true, the output Cloud Storage file will be serialized using the topic schema, if it exists.", "type": "boolean"}, "writeMetadata": {"description": "Optional. When true, write the subscription name, message_id, publish_time, attributes, and ordering_key as additional fields in the output. The subscription name, message_id, and publish_time fields are put in their own fields while all other message properties other than data (for example, an ordering_key, if present) are added as entries in the attributes map.", "type": "boolean"}}, "type": "object"}, "BigQueryConfig": {"description": "Configuration for a BigQuery subscription.", "id": "BigQueryConfig", "properties": {"dropUnknownFields": {"description": "Optional. When true and use_topic_schema is true, any fields that are a part of the topic schema that are not part of the BigQuery table schema are dropped when writing to BigQuery. Otherwise, the schemas must be kept in sync and any messages with extra fields are not written and remain in the subscription's backlog.", "type": "boolean"}, "serviceAccountEmail": {"description": "Optional. The service account to use to write to BigQuery. The subscription creator or updater that specifies this field must have `iam.serviceAccounts.actAs` permission on the service account. If not specified, the Pub/Sub [service agent](https://cloud.google.com/iam/docs/service-agents), service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com, is used.", "type": "string"}, "table": {"description": "Optional. The name of the table to which to write data, of the form {projectId}.{datasetId}.{tableId}", "type": "string"}, "useTableSchema": {"description": "Optional. When true, use the BigQuery table's schema as the columns to write to in BigQuery. `use_table_schema` and `use_topic_schema` cannot be enabled at the same time.", "type": "boolean"}, "useTopicSchema": {"description": "Optional. When true, use the topic's schema as the columns to write to in BigQuery, if it exists. `use_topic_schema` and `use_table_schema` cannot be enabled at the same time.", "type": "boolean"}, "writeMetadata": {"description": "Optional. When true, write the subscription name, message_id, publish_time, attributes, and ordering_key to additional columns in the table. The subscription name, message_id, and publish_time fields are put in their own columns while all other message properties (other than data) are written to a JSON object in the attributes column.", "type": "boolean"}}, "type": "object"}, "BigQueryDatasetSource": {"description": "A reference to a shared dataset. It is an existing BigQuery dataset with a collection of objects such as tables and views that you want to share with subscribers. When subscriber's subscribe to a listing, Analytics Hub creates a linked dataset in the subscriber's project. A Linked dataset is an opaque, read-only BigQuery dataset that serves as a _symbolic link_ to a shared dataset.", "id": "BigQueryDatasetSource", "properties": {"dataset": {"description": "Optional. Resource name of the dataset source for this listing. e.g. `projects/myproject/datasets/123`", "type": "string"}, "restrictedExportPolicy": {"$ref": "RestrictedExportPolicy", "description": "Optional. If set, restricted export policy will be propagated and enforced on the linked dataset."}, "selectedResources": {"description": "Optional. Resource in this dataset that is selectively shared. This field is required for data clean room exchanges.", "items": {"$ref": "SelectedResource"}, "type": "array"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CloudStorageConfig": {"description": "Configuration for a Cloud Storage subscription.", "id": "CloudStorageConfig", "properties": {"avroConfig": {"$ref": "AvroConfig", "description": "Optional. If set, message data will be written to Cloud Storage in Avro format."}, "bucket": {"description": "Required. User-provided name for the Cloud Storage bucket. The bucket must be created by the user. The bucket name must be without any prefix like \"gs://\". See the [bucket naming requirements] (https://cloud.google.com/storage/docs/buckets#naming).", "type": "string"}, "filenameDatetimeFormat": {"description": "Optional. User-provided format string specifying how to represent datetimes in Cloud Storage filenames. See the [datetime format guidance](https://cloud.google.com/pubsub/docs/create-cloudstorage-subscription#file_names).", "type": "string"}, "filenamePrefix": {"description": "Optional. User-provided prefix for Cloud Storage filename. See the [object naming requirements](https://cloud.google.com/storage/docs/objects#naming).", "type": "string"}, "filenameSuffix": {"description": "Optional. User-provided suffix for Cloud Storage filename. See the [object naming requirements](https://cloud.google.com/storage/docs/objects#naming). Must not end in \"/\".", "type": "string"}, "maxBytes": {"description": "Optional. The maximum bytes that can be written to a Cloud Storage file before a new file is created. Min 1 KB, max 10 GiB. The max_bytes limit may be exceeded in cases where messages are larger than the limit.", "format": "int64", "type": "string"}, "maxDuration": {"description": "Optional. File batching settings. If no max_duration setting is specified, a max_duration of 5 minutes will be set by default. max_duration is required regardless of whether other file batching settings are specified. The maximum duration that can elapse before a new Cloud Storage file is created. Min 1 minute, max 10 minutes, default 5 minutes. May not exceed the subscription's acknowledgement deadline.", "format": "google-duration", "type": "string"}, "maxMessages": {"description": "Optional. The maximum number of messages that can be written to a Cloud Storage file before a new file is created. Min 1000 messages.", "format": "int64", "type": "string"}, "serviceAccountEmail": {"description": "Optional. The service account to use to write to Cloud Storage. The subscription creator or updater that specifies this field must have `iam.serviceAccounts.actAs` permission on the service account. If not specified, the Pub/Sub [service agent](https://cloud.google.com/iam/docs/service-agents), service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com, is used.", "type": "string"}, "textConfig": {"$ref": "TextConfig", "description": "Optional. If set, message data will be written to Cloud Storage in text format."}}, "type": "object"}, "DataExchange": {"description": "A data exchange is a container that lets you share data. Along with the descriptive information about the data exchange, it contains listings that reference shared datasets.", "id": "DataExchange", "properties": {"description": {"description": "Optional. Description of the data exchange. The description must not contain Unicode non-characters as well as C0 and C1 control codes except tabs (HT), new lines (LF), carriage returns (CR), and page breaks (FF). Default value is an empty string. Max length: 2000 bytes.", "type": "string"}, "discoveryType": {"description": "Optional. Type of discovery on the discovery page for all the listings under this exchange. Updating this field also updates (overwrites) the discovery_type field for all the listings under this exchange.", "enum": ["DISCOVERY_TYPE_UNSPECIFIED", "DISCOVERY_TYPE_PRIVATE", "DISCOVERY_TYPE_PUBLIC"], "enumDescriptions": ["Unspecified. Defaults to DISCOVERY_TYPE_PRIVATE.", "The Data exchange/listing can be discovered in the 'Private' results list.", "The Data exchange/listing can be discovered in the 'Public' results list."], "type": "string"}, "displayName": {"description": "Required. Human-readable display name of the data exchange. The display name must contain only Unicode letters, numbers (0-9), underscores (_), dashes (-), spaces ( ), ampersands (&) and must not start or end with spaces. Default value is an empty string. Max length: 63 bytes.", "type": "string"}, "documentation": {"description": "Optional. Documentation describing the data exchange.", "type": "string"}, "icon": {"description": "Optional. Base64 encoded image representing the data exchange. Max Size: 3.0MiB Expected image dimensions are 512x512 pixels, however the API only performs validation on size of the encoded data. Note: For byte fields, the content of the fields are base64-encoded (which increases the size of the data by 33-36%) when using JSON on the wire.", "format": "byte", "type": "string"}, "listingCount": {"description": "Output only. Number of listings contained in the data exchange.", "format": "int32", "readOnly": true, "type": "integer"}, "logLinkedDatasetQueryUserEmail": {"description": "Optional. By default, false. If true, the DataExchange has an email sharing mandate enabled.", "type": "boolean"}, "name": {"description": "Output only. The resource name of the data exchange. e.g. `projects/myproject/locations/us/dataExchanges/123`.", "readOnly": true, "type": "string"}, "primaryContact": {"description": "Optional. Email or URL of the primary point of contact of the data exchange. Max Length: 1000 bytes.", "type": "string"}, "sharingEnvironmentConfig": {"$ref": "SharingEnvironmentConfig", "description": "Optional. Configurable data sharing environment option for a data exchange."}}, "type": "object"}, "DataProvider": {"description": "Contains details of the data provider.", "id": "DataProvider", "properties": {"name": {"description": "Optional. Name of the data provider.", "type": "string"}, "primaryContact": {"description": "Optional. Email or URL of the data provider. Max Length: 1000 bytes.", "type": "string"}}, "type": "object"}, "DcrExchangeConfig": {"description": "Data Clean Room (DCR), used for privacy-safe and secured data sharing.", "id": "DcrExchangeConfig", "properties": {"singleLinkedDatasetPerCleanroom": {"description": "Output only. If True, when subscribing to this DCR, it will create only one linked dataset containing all resources shared within the cleanroom. If False, when subscribing to this DCR, it will create 1 linked dataset per listing. This is not configurable, and by default, all new DCRs will have the restriction set to True.", "readOnly": true, "type": "boolean"}, "singleSelectedResourceSharingRestriction": {"description": "Output only. If True, this DCR restricts the contributors to sharing only a single resource in a Listing. And no two resources should have the same IDs. So if a contributor adds a view with a conflicting name, the CreateListing API will reject the request. if False, the data contributor can publish an entire dataset (as before). This is not configurable, and by default, all new DCRs will have the restriction set to True.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "DeadLetterPolicy": {"description": "Dead lettering is done on a best effort basis. The same message might be dead lettered multiple times. If validation on any of the fields fails at subscription creation/updation, the create/update subscription request will fail.", "id": "DeadLetterPolicy", "properties": {"deadLetterTopic": {"description": "Optional. The name of the topic to which dead letter messages should be published. Format is `projects/{project}/topics/{topic}`.The Pub/Sub service account associated with the enclosing subscription's parent project (i.e., service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com) must have permission to Publish() to this topic. The operation will fail if the topic does not exist. Users should ensure that there is a subscription attached to this topic since messages published to a topic with no subscriptions are lost.", "type": "string"}, "maxDeliveryAttempts": {"description": "Optional. The maximum number of delivery attempts for any message. The value must be between 5 and 100. The number of delivery attempts is defined as 1 + (the sum of number of NACKs and number of times the acknowledgement deadline has been exceeded for the message). A NACK is any call to ModifyAckDeadline with a 0 deadline. Note that client libraries may automatically extend ack_deadlines. This field will be honored on a best effort basis. If this parameter is 0, a default value of 5 is used.", "format": "int32", "type": "integer"}}, "type": "object"}, "DefaultExchangeConfig": {"description": "Default Analytics Hub data exchange, used for secured data sharing.", "id": "DefaultExchangeConfig", "properties": {}, "type": "object"}, "DestinationDataset": {"description": "Defines the destination bigquery dataset.", "id": "DestinationDataset", "properties": {"datasetReference": {"$ref": "DestinationDatasetReference", "description": "Required. A reference that identifies the destination dataset."}, "description": {"description": "Optional. A user-friendly description of the dataset.", "type": "string"}, "friendlyName": {"description": "Optional. A descriptive name for the dataset.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels associated with this dataset. You can use these to organize and group your datasets. You can set this property when inserting or updating a dataset. See https://cloud.google.com/resource-manager/docs/creating-managing-labels for more information.", "type": "object"}, "location": {"description": "Required. The geographic location where the dataset should reside. See https://cloud.google.com/bigquery/docs/locations for supported locations.", "type": "string"}}, "type": "object"}, "DestinationDatasetReference": {"id": "DestinationDatasetReference", "properties": {"datasetId": {"description": "Required. A unique ID for this dataset, without the project name. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "type": "string"}, "projectId": {"description": "Required. The ID of the project containing this dataset.", "type": "string"}}, "type": "object"}, "DestinationPubSubSubscription": {"description": "Defines the destination Pub/Sub subscription.", "id": "DestinationPubSubSubscription", "properties": {"pubsubSubscription": {"$ref": "GooglePubsubV1Subscription", "description": "Required. Destination Pub/Sub subscription resource."}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ExpirationPolicy": {"description": "A policy that specifies the conditions for resource expiration (i.e., automatic resource deletion).", "id": "ExpirationPolicy", "properties": {"ttl": {"description": "Optional. Specifies the \"time-to-live\" duration for an associated resource. The resource expires if it is not active for a period of `ttl`. The definition of \"activity\" depends on the type of the associated resource. The minimum and maximum allowed values for `ttl` depend on the type of the associated resource, as well. If `ttl` is not set, the associated resource never expires.", "format": "google-duration", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudBigqueryAnalyticshubV1ListingCommercialInfo": {"description": "Commercial info contains the information about the commercial data products associated with the listing.", "id": "GoogleCloudBigqueryAnalyticshubV1ListingCommercialInfo", "properties": {"cloudMarketplace": {"$ref": "GoogleCloudBigqueryAnalyticshubV1ListingCommercialInfoGoogleCloudMarketplaceInfo", "description": "Output only. Details of the Marketplace Data Product associated with the Listing.", "readOnly": true}}, "type": "object"}, "GoogleCloudBigqueryAnalyticshubV1ListingCommercialInfoGoogleCloudMarketplaceInfo": {"description": "Specifies the details of the Marketplace Data Product associated with the Listing.", "id": "GoogleCloudBigqueryAnalyticshubV1ListingCommercialInfoGoogleCloudMarketplaceInfo", "properties": {"commercialState": {"description": "Output only. Commercial state of the Marketplace Data Product.", "enum": ["COMMERCIAL_STATE_UNSPECIFIED", "ONBOARDING", "ACTIVE"], "enumDescriptions": ["Commercialization is incomplete and cannot be used.", "Commercialization has been initialized.", "Commercialization is complete and available for use."], "readOnly": true, "type": "string"}, "service": {"description": "Output only. Resource name of the commercial service associated with the Marketplace Data Product. e.g. example.com", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBigqueryAnalyticshubV1SubscriptionCommercialInfo": {"description": "Commercial info metadata for this subscription.", "id": "GoogleCloudBigqueryAnalyticshubV1SubscriptionCommercialInfo", "properties": {"cloudMarketplace": {"$ref": "GoogleCloudBigqueryAnalyticshubV1SubscriptionCommercialInfoGoogleCloudMarketplaceInfo", "description": "Output only. This is set when the subscription is commercialised via Cloud Marketplace.", "readOnly": true}}, "type": "object"}, "GoogleCloudBigqueryAnalyticshubV1SubscriptionCommercialInfoGoogleCloudMarketplaceInfo": {"description": "Cloud Marketplace commercial metadata for this subscription.", "id": "GoogleCloudBigqueryAnalyticshubV1SubscriptionCommercialInfoGoogleCloudMarketplaceInfo", "properties": {"order": {"description": "Resource name of the Marketplace Order.", "type": "string"}}, "type": "object"}, "GooglePubsubV1Subscription": {"description": "Defines the destination Pub/Sub subscription. If none of `push_config`, `bigquery_config`, `cloud_storage_config`, `pubsub_export_config`, or `pubsublite_export_config` is set, then the subscriber will pull and ack messages using API methods. At most one of these fields may be set.", "id": "GooglePubsubV1Subscription", "properties": {"ackDeadlineSeconds": {"description": "Optional. The approximate amount of time (on a best-effort basis) Pub/Sub waits for the subscriber to acknowledge receipt before resending the message. In the interval after the message is delivered and before it is acknowledged, it is considered to be _outstanding_. During that time period, the message will not be redelivered (on a best-effort basis). For pull subscriptions, this value is used as the initial value for the ack deadline. To override this value for a given message, call `ModifyAckDeadline` with the corresponding `ack_id` if using non-streaming pull or send the `ack_id` in a `StreamingModifyAckDeadlineRequest` if using streaming pull. The minimum custom deadline you can specify is 10 seconds. The maximum custom deadline you can specify is 600 seconds (10 minutes). If this parameter is 0, a default value of 10 seconds is used. For push delivery, this value is also used to set the request timeout for the call to the push endpoint. If the subscriber never acknowledges the message, the Pub/Sub system will eventually redeliver the message.", "format": "int32", "type": "integer"}, "bigqueryConfig": {"$ref": "BigQueryConfig", "description": "Optional. If delivery to BigQuery is used with this subscription, this field is used to configure it."}, "cloudStorageConfig": {"$ref": "CloudStorageConfig", "description": "Optional. If delivery to Google Cloud Storage is used with this subscription, this field is used to configure it."}, "deadLetterPolicy": {"$ref": "DeadLetterPolicy", "description": "Optional. A policy that specifies the conditions for dead lettering messages in this subscription. If dead_letter_policy is not set, dead lettering is disabled. The Pub/Sub service account associated with this subscriptions's parent project (i.e., service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com) must have permission to Acknowledge() messages on this subscription."}, "detached": {"description": "Optional. Indicates whether the subscription is detached from its topic. Detached subscriptions don't receive messages from their topic and don't retain any backlog. `Pull` and `StreamingPull` requests will return FAILED_PRECONDITION. If the subscription is a push subscription, pushes to the endpoint will not be made.", "type": "boolean"}, "enableExactlyOnceDelivery": {"description": "Optional. If true, Pub/Sub provides the following guarantees for the delivery of a message with a given value of `message_id` on this subscription: * The message sent to a subscriber is guaranteed not to be resent before the message's acknowledgement deadline expires. * An acknowledged message will not be resent to a subscriber. Note that subscribers may still receive multiple copies of a message when `enable_exactly_once_delivery` is true if the message was published multiple times by a publisher client. These copies are considered distinct by Pub/Sub and have distinct `message_id` values.", "type": "boolean"}, "enableMessageOrdering": {"description": "Optional. If true, messages published with the same `ordering_key` in `PubsubMessage` will be delivered to the subscribers in the order in which they are received by the Pub/Sub system. Otherwise, they may be delivered in any order.", "type": "boolean"}, "expirationPolicy": {"$ref": "ExpirationPolicy", "description": "Optional. A policy that specifies the conditions for this subscription's expiration. A subscription is considered active as long as any connected subscriber is successfully consuming messages from the subscription or is issuing operations on the subscription. If `expiration_policy` is not set, a *default policy* with `ttl` of 31 days will be used. The minimum allowed value for `expiration_policy.ttl` is 1 day. If `expiration_policy` is set, but `expiration_policy.ttl` is not set, the subscription never expires."}, "filter": {"description": "Optional. An expression written in the Pub/Sub [filter language](https://cloud.google.com/pubsub/docs/filtering). If non-empty, then only `PubsubMessage`s whose `attributes` field matches the filter are delivered on this subscription. If empty, then no messages are filtered out.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. See [Creating and managing labels](https://cloud.google.com/pubsub/docs/labels).", "type": "object"}, "messageRetentionDuration": {"description": "Optional. How long to retain unacknowledged messages in the subscription's backlog, from the moment a message is published. If `retain_acked_messages` is true, then this also configures the retention of acknowledged messages, and thus configures how far back in time a `Seek` can be done. Defaults to 7 days. Cannot be more than 31 days or less than 10 minutes.", "format": "google-duration", "type": "string"}, "messageTransforms": {"description": "Optional. Transforms to be applied to messages before they are delivered to subscribers. Transforms are applied in the order specified.", "items": {"$ref": "MessageTransform"}, "type": "array"}, "name": {"description": "Required. Name of the subscription. Format is `projects/{project}/subscriptions/{sub}`.", "type": "string"}, "pushConfig": {"$ref": "PushConfig", "description": "Optional. If push delivery is used with this subscription, this field is used to configure it."}, "retainAckedMessages": {"description": "Optional. Indicates whether to retain acknowledged messages. If true, then messages are not expunged from the subscription's backlog, even if they are acknowledged, until they fall out of the `message_retention_duration` window. This must be true if you would like to [`Seek` to a timestamp] (https://cloud.google.com/pubsub/docs/replay-overview#seek_to_a_time) in the past to replay previously-acknowledged messages.", "type": "boolean"}, "retryPolicy": {"$ref": "RetryPolicy", "description": "Optional. A policy that specifies how Pub/Sub retries message delivery for this subscription. If not set, the default retry policy is applied. This generally implies that messages will be retried as soon as possible for healthy subscribers. RetryPolicy will be triggered on NACKs or acknowledgement deadline exceeded events for a given message."}}, "type": "object"}, "JavaScriptUDF": {"description": "User-defined JavaScript function that can transform or filter a Pub/Sub message.", "id": "JavaScriptUDF", "properties": {"code": {"description": "Required. JavaScript code that contains a function `function_name` with the below signature: ``` /** * Transforms a Pub/Sub message. * @return {(Object)>|null)} - To * filter a message, return `null`. To transform a message return a map * with the following keys: * - (required) 'data' : {string} * - (optional) 'attributes' : {Object} * Returning empty `attributes` will remove all attributes from the * message. * * @param {(Object)>} Pub/Sub * message. Keys: * - (required) 'data' : {string} * - (required) 'attributes' : {Object} * * @param {Object} metadata - Pub/Sub message metadata. * Keys: * - (required) 'message_id' : {string} * - (optional) 'publish_time': {string} YYYY-MM-DDTHH:MM:SSZ format * - (optional) 'ordering_key': {string} */ function (message, metadata) { } ```", "type": "string"}, "functionName": {"description": "Required. Name of the JavasScript function that should applied to Pub/Sub messages.", "type": "string"}}, "type": "object"}, "LinkedResource": {"description": "Reference to a linked resource tracked by this Subscription.", "id": "LinkedResource", "properties": {"linkedDataset": {"description": "Output only. Name of the linked dataset, e.g. projects/subscriberproject/datasets/linked_dataset", "readOnly": true, "type": "string"}, "linkedPubsubSubscription": {"description": "Output only. Name of the Pub/Sub subscription, e.g. projects/subscriberproject/subscriptions/subscriptions/sub_id", "readOnly": true, "type": "string"}, "listing": {"description": "Output only. Listing for which linked resource is created.", "readOnly": true, "type": "string"}}, "type": "object"}, "ListDataExchangesResponse": {"description": "Message for response to the list of data exchanges.", "id": "ListDataExchangesResponse", "properties": {"dataExchanges": {"description": "The list of data exchanges.", "items": {"$ref": "DataExchange"}, "type": "array"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}, "type": "object"}, "ListListingsResponse": {"description": "Message for response to the list of Listings.", "id": "ListListingsResponse", "properties": {"listings": {"description": "The list of Listing.", "items": {"$ref": "Listing"}, "type": "array"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}, "type": "object"}, "ListOrgDataExchangesResponse": {"description": "Message for response to listing data exchanges in an organization and location.", "id": "ListOrgDataExchangesResponse", "properties": {"dataExchanges": {"description": "The list of data exchanges.", "items": {"$ref": "DataExchange"}, "type": "array"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}, "type": "object"}, "ListSharedResourceSubscriptionsResponse": {"description": "Message for response to the listing of shared resource subscriptions.", "id": "ListSharedResourceSubscriptionsResponse", "properties": {"nextPageToken": {"description": "Next page token.", "type": "string"}, "sharedResourceSubscriptions": {"description": "The list of subscriptions.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "ListSubscriptionsResponse": {"description": "Message for response to the listing of subscriptions.", "id": "ListSubscriptionsResponse", "properties": {"nextPageToken": {"description": "Next page token.", "type": "string"}, "subscriptions": {"description": "The list of subscriptions.", "items": {"$ref": "Subscription"}, "type": "array"}}, "type": "object"}, "Listing": {"description": "A listing is what gets published into a data exchange that a subscriber can subscribe to. It contains a reference to the data source along with descriptive information that will help subscribers find and subscribe the data.", "id": "Listing", "properties": {"allowOnlyMetadataSharing": {"description": "Optional. If true, the listing is only available to get the resource metadata. Listing is non subscribable.", "type": "boolean"}, "bigqueryDataset": {"$ref": "BigQueryDatasetSource", "description": "Shared dataset i.e. BigQuery dataset source."}, "categories": {"description": "Optional. Categories of the listing. Up to two categories are allowed.", "items": {"enum": ["CATEGORY_UNSPECIFIED", "CATEGORY_OTHERS", "CATEGORY_ADVERTISING_AND_MARKETING", "CATEGORY_COMMERCE", "CATEGORY_CLIMATE_AND_ENVIRONMENT", "CATEGORY_DEMOGRAPHICS", "CATEGORY_ECONOMICS", "CATEGORY_EDUCATION", "CATEGORY_ENERGY", "CATEGORY_FINANCIAL", "CATEGORY_GAMING", "CATEGORY_GEOSPATIAL", "CATEGORY_HEALTHCARE_AND_LIFE_SCIENCE", "CATEGORY_MEDIA", "CATEGORY_PUBLIC_SECTOR", "CATEGORY_RETAIL", "CATEGORY_SPORTS", "CATEGORY_SCIENCE_AND_RESEARCH", "CATEGORY_TRANSPORTATION_AND_LOGISTICS", "CATEGORY_TRAVEL_AND_TOURISM"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "type": "string"}, "type": "array"}, "commercialInfo": {"$ref": "GoogleCloudBigqueryAnalyticshubV1ListingCommercialInfo", "description": "Output only. Commercial info contains the information about the commercial data products associated with the listing.", "readOnly": true}, "dataProvider": {"$ref": "DataProvider", "description": "Optional. Details of the data provider who owns the source data."}, "description": {"description": "Optional. Short description of the listing. The description must not contain Unicode non-characters and C0 and C1 control codes except tabs (HT), new lines (LF), carriage returns (CR), and page breaks (FF). Default value is an empty string. Max length: 2000 bytes.", "type": "string"}, "discoveryType": {"description": "Optional. Type of discovery of the listing on the discovery page.", "enum": ["DISCOVERY_TYPE_UNSPECIFIED", "DISCOVERY_TYPE_PRIVATE", "DISCOVERY_TYPE_PUBLIC"], "enumDescriptions": ["Unspecified. Defaults to DISCOVERY_TYPE_PRIVATE.", "The Data exchange/listing can be discovered in the 'Private' results list.", "The Data exchange/listing can be discovered in the 'Public' results list."], "type": "string"}, "displayName": {"description": "Required. Human-readable display name of the listing. The display name must contain only Unicode letters, numbers (0-9), underscores (_), dashes (-), spaces ( ), ampersands (&) and can't start or end with spaces. Default value is an empty string. Max length: 63 bytes.", "type": "string"}, "documentation": {"description": "Optional. Documentation describing the listing.", "type": "string"}, "icon": {"description": "Optional. Base64 encoded image representing the listing. Max Size: 3.0MiB Expected image dimensions are 512x512 pixels, however the API only performs validation on size of the encoded data. Note: For byte fields, the contents of the field are base64-encoded (which increases the size of the data by 33-36%) when using JSON on the wire.", "format": "byte", "type": "string"}, "logLinkedDatasetQueryUserEmail": {"description": "Optional. By default, false. If true, the Listing has an email sharing mandate enabled.", "type": "boolean"}, "name": {"description": "Output only. The resource name of the listing. e.g. `projects/myproject/locations/us/dataExchanges/123/listings/456`", "readOnly": true, "type": "string"}, "primaryContact": {"description": "Optional. Email or URL of the primary point of contact of the listing. Max Length: 1000 bytes.", "type": "string"}, "publisher": {"$ref": "Publisher", "description": "Optional. Details of the publisher who owns the listing and who can share the source data."}, "pubsubTopic": {"$ref": "PubSubTopicSource", "description": "Pub/Sub topic source."}, "requestAccess": {"description": "Optional. Email or URL of the request access of the listing. Subscribers can use this reference to request access. Max Length: 1000 bytes.", "type": "string"}, "resourceType": {"description": "Output only. Listing shared asset type.", "enum": ["SHARED_RESOURCE_TYPE_UNSPECIFIED", "BIGQUERY_DATASET", "PUBSUB_TOPIC"], "enumDescriptions": ["Not specified.", "BigQuery Dataset Asset.", "Pub/Sub Topic Asset."], "readOnly": true, "type": "string"}, "restrictedExportConfig": {"$ref": "RestrictedExportConfig", "description": "Optional. If set, restricted export configuration will be propagated and enforced on the linked dataset."}, "state": {"description": "Output only. Current state of the listing.", "enum": ["STATE_UNSPECIFIED", "ACTIVE"], "enumDescriptions": ["Default value. This value is unused.", "Subscribable state. Users with dataexchange.listings.subscribe permission can subscribe to this listing."], "readOnly": true, "type": "string"}}, "type": "object"}, "MessageTransform": {"description": "All supported message transforms types.", "id": "MessageTransform", "properties": {"disabled": {"description": "Optional. If true, the transform is disabled and will not be applied to messages. Defaults to `false`.", "type": "boolean"}, "enabled": {"deprecated": true, "description": "Optional. This field is deprecated, use the `disabled` field to disable transforms.", "type": "boolean"}, "javascriptUdf": {"$ref": "JavaScriptUDF", "description": "Optional. JavaScript User Defined Function. If multiple JavaScriptUDF's are specified on a resource, each must have a unique `function_name`."}}, "type": "object"}, "NoWrapper": {"description": "Sets the `data` field as the HTTP body for delivery.", "id": "NoWrapper", "properties": {"writeMetadata": {"description": "Optional. When true, writes the Pub/Sub message metadata to `x-goog-pubsub-:` headers of the HTTP request. Writes the Pub/Sub message attributes to `:` headers of the HTTP request.", "type": "boolean"}}, "type": "object"}, "OidcToken": {"description": "Contains information needed for generating an [OpenID Connect token](https://developers.google.com/identity/protocols/OpenIDConnect).", "id": "OidcToken", "properties": {"audience": {"description": "Optional. Audience to be used when generating OIDC token. The audience claim identifies the recipients that the JWT is intended for. The audience value is a single case-sensitive string. Having multiple values (array) for the audience field is not supported. More info about the OIDC JWT token audience here: https://tools.ietf.org/html/rfc7519#section-4.1.3 Note: if not specified, the Push endpoint URL will be used.", "type": "string"}, "serviceAccountEmail": {"description": "Optional. [Service account email](https://cloud.google.com/iam/docs/service-accounts) used for generating the OIDC token. For more information on setting up authentication, see [Push subscriptions](https://cloud.google.com/pubsub/docs/push).", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of a long-running operation in Analytics Hub.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PubSubTopicSource": {"description": "Pub/Sub topic source.", "id": "PubSubTopicSource", "properties": {"dataAffinityRegions": {"description": "Optional. Region hint on where the data might be published. Data affinity regions are modifiable. See https://cloud.google.com/about/locations for full listing of possible Cloud regions.", "items": {"type": "string"}, "type": "array"}, "topic": {"description": "Required. Resource name of the Pub/Sub topic source for this listing. e.g. projects/myproject/topics/topicId", "type": "string"}}, "type": "object"}, "Publisher": {"description": "Contains details of the listing publisher.", "id": "Publisher", "properties": {"name": {"description": "Optional. Name of the listing publisher.", "type": "string"}, "primaryContact": {"description": "Optional. Email or URL of the listing publisher. Max Length: 1000 bytes.", "type": "string"}}, "type": "object"}, "PubsubWrapper": {"description": "The payload to the push endpoint is in the form of the JSON representation of a PubsubMessage (https://cloud.google.com/pubsub/docs/reference/rpc/google.pubsub.v1#pubsubmessage).", "id": "PubsubWrapper", "properties": {}, "type": "object"}, "PushConfig": {"description": "Configuration for a push delivery endpoint.", "id": "PushConfig", "properties": {"attributes": {"additionalProperties": {"type": "string"}, "description": "Optional. Endpoint configuration attributes that can be used to control different aspects of the message delivery. The only currently supported attribute is `x-goog-version`, which you can use to change the format of the pushed message. This attribute indicates the version of the data expected by the endpoint. This controls the shape of the pushed message (i.e., its fields and metadata). If not present during the `CreateSubscription` call, it will default to the version of the Pub/Sub API used to make such call. If not present in a `ModifyPushConfig` call, its value will not be changed. `GetSubscription` calls will always return a valid version, even if the subscription was created without this attribute. The only supported values for the `x-goog-version` attribute are: * `v1beta1`: uses the push format defined in the v1beta1 Pub/Sub API. * `v1` or `v1beta2`: uses the push format defined in the v1 Pub/Sub API. For example: `attributes { \"x-goog-version\": \"v1\" }`", "type": "object"}, "noWrapper": {"$ref": "NoWrapper", "description": "Optional. When set, the payload to the push endpoint is not wrapped."}, "oidcToken": {"$ref": "OidcToken", "description": "Optional. If specified, Pub/Sub will generate and attach an OIDC JWT token as an `Authorization` header in the HTTP request for every pushed message."}, "pubsubWrapper": {"$ref": "PubsubWrapper", "description": "Optional. When set, the payload to the push endpoint is in the form of the JSON representation of a PubsubMessage (https://cloud.google.com/pubsub/docs/reference/rpc/google.pubsub.v1#pubsubmessage)."}, "pushEndpoint": {"description": "Optional. A URL locating the endpoint to which messages should be pushed. For example, a Webhook endpoint might use `https://example.com/push`.", "type": "string"}}, "type": "object"}, "RefreshSubscriptionRequest": {"description": "Message for refreshing a subscription.", "id": "RefreshSubscriptionRequest", "properties": {}, "type": "object"}, "RefreshSubscriptionResponse": {"description": "Message for response when you refresh a subscription.", "id": "RefreshSubscriptionResponse", "properties": {"subscription": {"$ref": "Subscription", "description": "The refreshed subscription resource."}}, "type": "object"}, "RestrictedExportConfig": {"description": "Restricted export config, used to configure restricted export on linked dataset.", "id": "RestrictedExportConfig", "properties": {"enabled": {"description": "Optional. If true, enable restricted export.", "type": "boolean"}, "restrictDirectTableAccess": {"description": "Output only. If true, restrict direct table access(read api/tabledata.list) on linked table.", "readOnly": true, "type": "boolean"}, "restrictQueryResult": {"description": "Optional. If true, restrict export of query result derived from restricted linked dataset table.", "type": "boolean"}}, "type": "object"}, "RestrictedExportPolicy": {"description": "Restricted export policy used to configure restricted export on linked dataset.", "id": "RestrictedExportPolicy", "properties": {"enabled": {"description": "Optional. If true, enable restricted export.", "type": "boolean"}, "restrictDirectTableAccess": {"description": "Optional. If true, restrict direct table access (read api/tabledata.list) on linked table.", "type": "boolean"}, "restrictQueryResult": {"description": "Optional. If true, restrict export of query result derived from restricted linked dataset table.", "type": "boolean"}}, "type": "object"}, "RetryPolicy": {"description": "A policy that specifies how Pub/Sub retries message delivery. Retry delay will be exponential based on provided minimum and maximum backoffs. https://en.wikipedia.org/wiki/Exponential_backoff. RetryPolicy will be triggered on NACKs or acknowledgement deadline exceeded events for a given message. Retry Policy is implemented on a best effort basis. At times, the delay between consecutive deliveries may not match the configuration. That is, delay can be more or less than configured backoff.", "id": "RetryPolicy", "properties": {"maximumBackoff": {"description": "Optional. The maximum delay between consecutive deliveries of a given message. Value should be between 0 and 600 seconds. Defaults to 600 seconds.", "format": "google-duration", "type": "string"}, "minimumBackoff": {"description": "Optional. The minimum delay between consecutive deliveries of a given message. Value should be between 0 and 600 seconds. Defaults to 10 seconds.", "format": "google-duration", "type": "string"}}, "type": "object"}, "RevokeSubscriptionRequest": {"description": "Message for revoking a subscription.", "id": "RevokeSubscriptionRequest", "properties": {"revokeCommercial": {"description": "Optional. If the subscription is commercial then this field must be set to true, otherwise a failure is thrown. This acts as a safety guard to avoid revoking commercial subscriptions accidentally.", "type": "boolean"}}, "type": "object"}, "RevokeSubscriptionResponse": {"description": "Message for response when you revoke a subscription. Empty for now.", "id": "RevokeSubscriptionResponse", "properties": {}, "type": "object"}, "SelectedResource": {"description": "Resource in this dataset that is selectively shared.", "id": "SelectedResource", "properties": {"routine": {"description": "Optional. Format: For routine: `projects/{projectId}/datasets/{datasetId}/routines/{routineId}` Example:\"projects/test_project/datasets/test_dataset/routines/test_routine\"", "type": "string"}, "table": {"description": "Optional. Format: For table: `projects/{projectId}/datasets/{datasetId}/tables/{tableId}` Example:\"projects/test_project/datasets/test_dataset/tables/test_table\"", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SharingEnvironmentConfig": {"description": "Sharing environment is a behavior model for sharing data within a data exchange. This option is configurable for a data exchange.", "id": "SharingEnvironmentConfig", "properties": {"dcrExchangeConfig": {"$ref": "DcrExchangeConfig", "description": "Data Clean Room (DCR), used for privacy-safe and secured data sharing."}, "defaultExchangeConfig": {"$ref": "DefaultExchangeConfig", "description": "Default Analytics Hub data exchange, used for secured data sharing."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "SubscribeDataExchangeRequest": {"description": "Message for subscribing to a Data Exchange.", "id": "SubscribeDataExchangeRequest", "properties": {"destination": {"description": "Required. The parent resource path of the Subscription. e.g. `projects/subscriberproject/locations/us`", "type": "string"}, "destinationDataset": {"$ref": "DestinationDataset", "description": "Optional. BigQuery destination dataset to create for the subscriber."}, "subscriberContact": {"description": "Email of the subscriber.", "type": "string"}, "subscription": {"description": "Required. Name of the subscription to create. e.g. `subscription1`", "type": "string"}}, "type": "object"}, "SubscribeDataExchangeResponse": {"description": "Message for response when you subscribe to a Data Exchange.", "id": "SubscribeDataExchangeResponse", "properties": {"subscription": {"$ref": "Subscription", "description": "Subscription object created from this subscribe action."}}, "type": "object"}, "SubscribeListingRequest": {"description": "Message for subscribing to a listing.", "id": "SubscribeListingRequest", "properties": {"destinationDataset": {"$ref": "DestinationDataset", "description": "Input only. BigQuery destination dataset to create for the subscriber."}, "destinationPubsubSubscription": {"$ref": "DestinationPubSubSubscription", "description": "Input only. Destination Pub/Sub subscription to create for the subscriber."}}, "type": "object"}, "SubscribeListingResponse": {"description": "Message for response when you subscribe to a listing.", "id": "SubscribeListingResponse", "properties": {"subscription": {"$ref": "Subscription", "description": "Subscription object created from this subscribe action."}}, "type": "object"}, "Subscription": {"description": "A subscription represents a subscribers' access to a particular set of published data. It contains references to associated listings, data exchanges, and linked datasets.", "id": "Subscription", "properties": {"commercialInfo": {"$ref": "GoogleCloudBigqueryAnalyticshubV1SubscriptionCommercialInfo", "description": "Output only. This is set if this is a commercial subscription i.e. if this subscription was created from subscribing to a commercial listing.", "readOnly": true}, "creationTime": {"description": "Output only. Timestamp when the subscription was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataExchange": {"description": "Output only. Resource name of the source Data Exchange. e.g. projects/123/locations/us/dataExchanges/456", "readOnly": true, "type": "string"}, "destinationDataset": {"$ref": "DestinationDataset", "description": "Optional. BigQuery destination dataset to create for the subscriber."}, "lastModifyTime": {"description": "Output only. Timestamp when the subscription was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}, "linkedDatasetMap": {"additionalProperties": {"$ref": "LinkedResource"}, "description": "Output only. Map of listing resource names to associated linked resource, e.g. projects/123/locations/us/dataExchanges/456/listings/789 -> projects/123/datasets/my_dataset For listing-level subscriptions, this is a map of size 1. Only contains values if state == STATE_ACTIVE.", "readOnly": true, "type": "object"}, "linkedResources": {"description": "Output only. Linked resources created in the subscription. Only contains values if state = STATE_ACTIVE.", "items": {"$ref": "LinkedResource"}, "readOnly": true, "type": "array"}, "listing": {"description": "Output only. Resource name of the source Listing. e.g. projects/123/locations/us/dataExchanges/456/listings/789", "readOnly": true, "type": "string"}, "logLinkedDatasetQueryUserEmail": {"description": "Output only. By default, false. If true, the Subscriber agreed to the email sharing mandate that is enabled for DataExchange/Listing.", "readOnly": true, "type": "boolean"}, "name": {"description": "Output only. The resource name of the subscription. e.g. `projects/myproject/locations/us/subscriptions/123`.", "readOnly": true, "type": "string"}, "organizationDisplayName": {"description": "Output only. Display name of the project of this subscription.", "readOnly": true, "type": "string"}, "organizationId": {"description": "Output only. Organization of the project this subscription belongs to.", "readOnly": true, "type": "string"}, "resourceType": {"description": "Output only. Listing shared asset type.", "enum": ["SHARED_RESOURCE_TYPE_UNSPECIFIED", "BIGQUERY_DATASET", "PUBSUB_TOPIC"], "enumDescriptions": ["Not specified.", "BigQuery Dataset Asset.", "Pub/Sub Topic Asset."], "readOnly": true, "type": "string"}, "state": {"description": "Output only. Current state of the subscription.", "enum": ["STATE_UNSPECIFIED", "STATE_ACTIVE", "STATE_STALE", "STATE_INACTIVE"], "enumDescriptions": ["Default value. This value is unused.", "This subscription is active and the data is accessible.", "The data referenced by this subscription is out of date and should be refreshed. This can happen when a data provider adds or removes datasets.", "This subscription has been cancelled or revoked and the data is no longer accessible."], "readOnly": true, "type": "string"}, "subscriberContact": {"description": "Output only. Email of the subscriber.", "readOnly": true, "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TextConfig": {"description": "Configuration for writing message data in text format. Message payloads will be written to files as raw text, separated by a newline.", "id": "TextConfig", "properties": {}, "type": "object"}}, "servicePath": "", "title": "Analytics Hub API", "version": "v1", "version_module": true}