{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://securitycenter.googleapis.com/", "batchPath": "batch", "canonicalName": "Security Command Center", "description": "Security Command Center API provides access to temporal views of assets and findings within an organization.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/security-command-center", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://securitycenter.me-central2.rep.googleapis.com/", "location": "me-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://securitycenter.us.rep.googleapis.com/", "location": "us"}, {"description": "Regional Endpoint", "endpointUrl": "https://securitycenter.eu.rep.googleapis.com/", "location": "eu"}], "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "securitycenter:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://securitycenter.mtls.googleapis.com/", "name": "securitycenter", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"methods": {"getOrganizationSettings": {"description": "Gets the settings for an organization.", "flatPath": "v1beta1/organizations/{organizationsId}/organizationSettings", "httpMethod": "GET", "id": "securitycenter.organizations.getOrganizationSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the organization to get organization settings for. Its format is \"organizations/[organization_id]/organizationSettings\".", "location": "path", "pattern": "^organizations/[^/]+/organizationSettings$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "OrganizationSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateOrganizationSettings": {"description": "Updates an organization's settings.", "flatPath": "v1beta1/organizations/{organizationsId}/organizationSettings", "httpMethod": "PATCH", "id": "securitycenter.organizations.updateOrganizationSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The relative resource name of the settings. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/organizationSettings\".", "location": "path", "pattern": "^organizations/[^/]+/organizationSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The FieldMask to use when updating the settings resource.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "OrganizationSettings"}, "response": {"$ref": "OrganizationSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"assets": {"methods": {"group": {"description": "Filters an organization's assets and groups them by their specified properties.", "flatPath": "v1beta1/organizations/{organizationsId}/assets:group", "httpMethod": "POST", "id": "securitycenter.organizations.assets.group", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the organization to groupBy. Its format is \"organizations/[organization_id]\".", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/assets:group", "request": {"$ref": "GroupAssetsRequest"}, "response": {"$ref": "GroupAssetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists an organization's assets.", "flatPath": "v1beta1/organizations/{organizationsId}/assets", "httpMethod": "GET", "id": "securitycenter.organizations.assets.list", "parameterOrder": ["parent"], "parameters": {"compareDuration": {"description": "When compare_duration is set, the ListAssetResult's \"state\" attribute is updated to indicate whether the asset was added, removed, or remained present during the compare_duration period of time that precedes the read_time. This is the time between (read_time - compare_duration) and read_time. The state value is derived based on the presence of the asset at the two points in time. Intermediate state changes between the two times don't affect the result. For example, the results aren't affected if the asset is removed and re-created again. Possible \"state\" values when compare_duration is specified: * \"ADDED\": indicates that the asset was not present before compare_duration, but present at read_time. * \"REMOVED\": indicates that the asset was present at the start of compare_duration, but not present at read_time. * \"ACTIVE\": indicates that the asset was present at both the start and the end of the time period defined by compare_duration and read_time. If compare_duration is not specified, then the only possible state is \"UNUSED\", which indicates that the asset is present at read_time.", "format": "google-duration", "location": "query", "type": "string"}, "fieldMask": {"description": "Optional. A field mask to specify the ListAssetsResult fields to be listed in the response. An empty field mask will list all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}, "filter": {"description": "Expression that defines the filter to apply across assets. The expression is a list of zero or more restrictions combined via logical operators `AND` and `OR`. Parentheses are not supported, and `OR` has higher precedence than `AND`. Restrictions have the form ` ` and may have a `-` character in front of them to indicate negation. The fields map to those defined in the Asset resource. Examples include: * name * security_center_properties.resource_name * resource_properties.a_property * security_marks.marks.marka The supported operators are: * `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching, for strings. The supported value types are: * string literals in quotes. * integer literals without quotes. * boolean literals `true` and `false` without quotes. For example, `resource_properties.size = 100` is a valid filter string.", "location": "query", "type": "string"}, "orderBy": {"description": "Expression that defines what fields and order to use for sorting. The string value should follow SQL syntax: comma separated list of fields. For example: \"name,resource_properties.a_property\". The default sorting order is ascending. To specify descending order for a field, a suffix \" desc\" should be appended to the field name. For example: \"name desc,resource_properties.a_property\". Redundant space characters in the syntax are insignificant. \"name desc,resource_properties.a_property\" and \" name desc , resource_properties.a_property \" are equivalent.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response. Default is 10, minimum is 1, maximum is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAssetsResponse`; indicates that this is a continuation of a prior `ListAssets` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the organization assets should belong to. Its format is \"organizations/[organization_id]\".", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}, "readTime": {"description": "Time used as a reference point when filtering assets. The filter is limited to assets existing at the supplied time and their values are those at that specific time. Absence of this field will default to the API's version of NOW.", "format": "google-datetime", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/assets", "response": {"$ref": "ListAssetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "runDiscovery": {"description": "Runs asset discovery. The discovery is tracked with a long-running operation. This API can only be called with limited frequency for an organization. If it is called too frequently the caller will receive a TOO_MANY_REQUESTS error.", "flatPath": "v1beta1/organizations/{organizationsId}/assets:runDiscovery", "httpMethod": "POST", "id": "securitycenter.organizations.assets.runDiscovery", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the organization to run asset discovery for. Its format is \"organizations/[organization_id]\".", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/assets:runDiscovery", "request": {"$ref": "RunAssetDiscoveryRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSecurityMarks": {"description": "Updates security marks.", "flatPath": "v1beta1/organizations/{organizationsId}/assets/{assetsId}/securityMarks", "httpMethod": "PATCH", "id": "securitycenter.organizations.assets.updateSecurityMarks", "parameterOrder": ["name"], "parameters": {"name": {"description": "The relative resource name of the SecurityMarks. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\".", "location": "path", "pattern": "^organizations/[^/]+/assets/[^/]+/securityMarks$", "required": true, "type": "string"}, "startTime": {"description": "The time at which the updated SecurityMarks take effect.", "format": "google-datetime", "location": "query", "type": "string"}, "updateMask": {"description": "The FieldMask to use when updating the security marks resource.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudSecuritycenterV1beta1SecurityMarks"}, "response": {"$ref": "GoogleCloudSecuritycenterV1beta1SecurityMarks"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1beta1/organizations/{organizationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "securitycenter.organizations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^organizations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1beta1/organizations/{organizationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "securitycenter.organizations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^organizations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/organizations/{organizationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "securitycenter.organizations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^organizations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/organizations/{organizationsId}/operations", "httpMethod": "GET", "id": "securitycenter.organizations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^organizations/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "sources": {"methods": {"create": {"description": "Creates a source.", "flatPath": "v1beta1/organizations/{organizationsId}/sources", "httpMethod": "POST", "id": "securitycenter.organizations.sources.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the new source's parent. Its format should be \"organizations/[organization_id]\".", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/sources", "request": {"$ref": "Source"}, "response": {"$ref": "Source"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a source.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}", "httpMethod": "GET", "id": "securitycenter.organizations.sources.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Relative resource name of the source. Its format is \"organizations/[organization_id]/source/[source_id]\".", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Source"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy on the specified Source.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}:getIamPolicy", "httpMethod": "POST", "id": "securitycenter.organizations.sources.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all sources belonging to an organization.", "flatPath": "v1beta1/organizations/{organizationsId}/sources", "httpMethod": "GET", "id": "securitycenter.organizations.sources.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of results to return in a single response. Default is 10, minimum is 1, maximum is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListSourcesResponse`; indicates that this is a continuation of a prior `ListSources` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the parent of sources to list. Its format should be \"organizations/[organization_id]\".", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/sources", "response": {"$ref": "ListSourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a source.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}", "httpMethod": "PATCH", "id": "securitycenter.organizations.sources.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The relative resource name of this source. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/sources/{source_id}\"", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The FieldMask to use when updating the source resource.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Source"}, "response": {"$ref": "Source"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified Source.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}:setIamPolicy", "httpMethod": "POST", "id": "securitycenter.organizations.sources.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": " Returns the permissions that a caller has on the specified source.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}:testIamPermissions", "httpMethod": "POST", "id": "securitycenter.organizations.sources.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"findings": {"methods": {"create": {"description": "Creates a finding. The corresponding source must exist for finding creation to succeed.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}/findings", "httpMethod": "POST", "id": "securitycenter.organizations.sources.findings.create", "parameterOrder": ["parent"], "parameters": {"findingId": {"description": "Required. Unique identifier provided by the client within the parent scope. It must be alphanumeric and less than or equal to 32 characters and greater than 0 characters in length.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the new finding's parent. Its format should be \"organizations/[organization_id]/sources/[source_id]\".", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/findings", "request": {"$ref": "GoogleCloudSecuritycenterV1beta1Finding"}, "response": {"$ref": "GoogleCloudSecuritycenterV1beta1Finding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "group": {"description": "Filters an organization or source's findings and groups them by their specified properties. To group across all sources provide a `-` as the source id. Example: /v1beta1/organizations/{organization_id}/sources/-/findings", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}/findings:group", "httpMethod": "POST", "id": "securitycenter.organizations.sources.findings.group", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the source to groupBy. Its format is \"organizations/[organization_id]/sources/[source_id]\". To groupBy across all sources provide a source_id of `-`. For example: organizations/{organization_id}/sources/-", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/findings:group", "request": {"$ref": "GroupFindingsRequest"}, "response": {"$ref": "GroupFindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists an organization or source's findings. To list across all sources provide a `-` as the source id. Example: /v1beta1/organizations/{organization_id}/sources/-/findings", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}/findings", "httpMethod": "GET", "id": "securitycenter.organizations.sources.findings.list", "parameterOrder": ["parent"], "parameters": {"fieldMask": {"description": "Optional. A field mask to specify the Finding fields to be listed in the response. An empty field mask will list all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}, "filter": {"description": "Expression that defines the filter to apply across findings. The expression is a list of one or more restrictions combined via logical operators `AND` and `OR`. Parentheses are not supported, and `OR` has higher precedence than `AND`. Restrictions have the form ` ` and may have a `-` character in front of them to indicate negation. Examples include: * name * source_properties.a_property * security_marks.marks.marka The supported operators are: * `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching, for strings. The supported value types are: * string literals in quotes. * integer literals without quotes. * boolean literals `true` and `false` without quotes. For example, `source_properties.size = 100` is a valid filter string.", "location": "query", "type": "string"}, "orderBy": {"description": "Expression that defines what fields and order to use for sorting. The string value should follow SQL syntax: comma separated list of fields. For example: \"name,resource_properties.a_property\". The default sorting order is ascending. To specify descending order for a field, a suffix \" desc\" should be appended to the field name. For example: \"name desc,source_properties.a_property\". Redundant space characters in the syntax are insignificant. \"name desc,source_properties.a_property\" and \" name desc , source_properties.a_property \" are equivalent.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response. Default is 10, minimum is 1, maximum is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListFindingsResponse`; indicates that this is a continuation of a prior `ListFindings` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the source the findings belong to. Its format is \"organizations/[organization_id]/sources/[source_id]\". To list across all sources provide a source_id of `-`. For example: organizations/{organization_id}/sources/-", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}, "readTime": {"description": "Time used as a reference point when filtering findings. The filter is limited to findings existing at the supplied time and their values are those at that specific time. Absence of this field will default to the API's version of NOW.", "format": "google-datetime", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/findings", "response": {"$ref": "ListFindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Creates or updates a finding. The corresponding source must exist for a finding creation to succeed.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}", "httpMethod": "PATCH", "id": "securitycenter.organizations.sources.findings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The relative resource name of this finding. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\"", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+/findings/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The FieldMask to use when updating the finding resource. This field should not be specified when creating a finding.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudSecuritycenterV1beta1Finding"}, "response": {"$ref": "GoogleCloudSecuritycenterV1beta1Finding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setState": {"description": "Updates the state of a finding.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}:setState", "httpMethod": "POST", "id": "securitycenter.organizations.sources.findings.setState", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The relative resource name of the finding. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/sources/{source_id}/finding/{finding_id}\".", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+/findings/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:setState", "request": {"$ref": "SetFindingStateRequest"}, "response": {"$ref": "GoogleCloudSecuritycenterV1beta1Finding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSecurityMarks": {"description": "Updates security marks.", "flatPath": "v1beta1/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}/securityMarks", "httpMethod": "PATCH", "id": "securitycenter.organizations.sources.findings.updateSecurityMarks", "parameterOrder": ["name"], "parameters": {"name": {"description": "The relative resource name of the SecurityMarks. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\".", "location": "path", "pattern": "^organizations/[^/]+/sources/[^/]+/findings/[^/]+/securityMarks$", "required": true, "type": "string"}, "startTime": {"description": "The time at which the updated SecurityMarks take effect.", "format": "google-datetime", "location": "query", "type": "string"}, "updateMask": {"description": "The FieldMask to use when updating the security marks resource.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudSecuritycenterV1beta1SecurityMarks"}, "response": {"$ref": "GoogleCloudSecuritycenterV1beta1SecurityMarks"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "********", "rootUrl": "https://securitycenter.googleapis.com/", "schemas": {"Access": {"description": "Represents an access event.", "id": "Access", "properties": {"callerIp": {"description": "Caller's IP address, such as \"*******\".", "type": "string"}, "callerIpGeo": {"$ref": "Geolocation", "description": "The caller IP's geolocation, which identifies where the call came from."}, "methodName": {"description": "The method that the service account called, e.g. \"SetIamPolicy\".", "type": "string"}, "principalEmail": {"description": "Associated email, such as \"<EMAIL>\". The email address of the authenticated user or a service account acting on behalf of a third party principal making the request. For third party identity callers, the `principal_subject` field is populated instead of this field. For privacy reasons, the principal email address is sometimes redacted. For more information, see [Caller identities in audit logs](https://cloud.google.com/logging/docs/audit#user-id).", "type": "string"}, "principalSubject": {"description": "A string that represents the principal_subject that is associated with the identity. Unlike `principal_email`, `principal_subject` supports principals that aren't associated with email addresses, such as third party principals. For most identities, the format is `principal://iam.googleapis.com/{identity pool name}/subject/{subject}`. Some GKE identities, such as GKE_WORKLOAD, FREEFORM, and GKE_HUB_WORKLOAD, still use the legacy format `serviceAccount:{identity pool name}[{subject}]`.", "type": "string"}, "serviceAccountDelegationInfo": {"description": "The identity delegation history of an authenticated service account that made the request. The `serviceAccountDelegationInfo[]` object contains information about the real authorities that try to access Google Cloud resources by delegating on a service account. When multiple authorities are present, they are guaranteed to be sorted based on the original ordering of the identity delegation events.", "items": {"$ref": "ServiceAccountDelegationInfo"}, "type": "array"}, "serviceAccountKeyName": {"description": "The name of the service account key that was used to create or exchange credentials when authenticating the service account that made the request. This is a scheme-less URI full resource name. For example: \"//iam.googleapis.com/projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}\". ", "type": "string"}, "serviceName": {"description": "This is the API service that the service account made a call to, e.g. \"iam.googleapis.com\"", "type": "string"}, "userAgent": {"description": "The caller's user agent string associated with the finding.", "type": "string"}, "userAgentFamily": {"description": "Type of user agent associated with the finding. For example, an operating system shell or an embedded or standalone application.", "type": "string"}, "userName": {"description": "A string that represents a username. The username provided depends on the type of the finding and is likely not an IAM principal. For example, this can be a system username if the finding is related to a virtual machine, or it can be an application login username.", "type": "string"}}, "type": "object"}, "AccessReview": {"description": "Conveys information about a Kubernetes access review (such as one returned by a [`kubectl auth can-i`](https://kubernetes.io/docs/reference/access-authn-authz/authorization/#checking-api-access) command) that was involved in a finding.", "id": "AccessReview", "properties": {"group": {"description": "The API group of the resource. \"*\" means all.", "type": "string"}, "name": {"description": "The name of the resource being requested. Empty means all.", "type": "string"}, "ns": {"description": "Namespace of the action being requested. Currently, there is no distinction between no namespace and all namespaces. Both are represented by \"\" (empty).", "type": "string"}, "resource": {"description": "The optional resource type requested. \"*\" means all.", "type": "string"}, "subresource": {"description": "The optional subresource type.", "type": "string"}, "verb": {"description": "A Kubernetes resource API verb, like get, list, watch, create, update, delete, proxy. \"*\" means all.", "type": "string"}, "version": {"description": "The API version of the resource. \"*\" means all.", "type": "string"}}, "type": "object"}, "AdaptiveProtection": {"description": "Information about [Google Cloud Armor Adaptive Protection](https://cloud.google.com/armor/docs/cloud-armor-overview#google-cloud-armor-adaptive-protection).", "id": "AdaptiveProtection", "properties": {"confidence": {"description": "A score of 0 means that there is low confidence that the detected event is an actual attack. A score of 1 means that there is high confidence that the detected event is an attack. See the [Adaptive Protection documentation](https://cloud.google.com/armor/docs/adaptive-protection-overview#configure-alert-tuning) for further explanation.", "format": "double", "type": "number"}}, "type": "object"}, "AffectedResources": {"description": "Details about resources affected by this finding.", "id": "AffectedResources", "properties": {"count": {"description": "The count of resources affected by the finding.", "format": "int64", "type": "string"}}, "type": "object"}, "Allowed": {"description": "Allowed IP rule.", "id": "Allowed", "properties": {"ipRules": {"description": "Optional. Optional list of allowed IP rules.", "items": {"$ref": "IpRule"}, "type": "array"}}, "type": "object"}, "Application": {"description": "Represents an application associated with a finding.", "id": "Application", "properties": {"baseUri": {"description": "The base URI that identifies the network location of the application in which the vulnerability was detected. For example, `http://example.com`.", "type": "string"}, "fullUri": {"description": "The full URI with payload that can be used to reproduce the vulnerability. For example, `http://example.com?p=aMmYgI6H`.", "type": "string"}}, "type": "object"}, "Asset": {"description": "Security Command Center representation of a Google Cloud resource. The Asset is a Security Command Center resource that captures information about a single Google Cloud resource. All modifications to an Asset are only within the context of Security Command Center and don't affect the referenced Google Cloud resource.", "id": "<PERSON><PERSON>", "properties": {"createTime": {"description": "The time at which the asset was created in Security Command Center.", "format": "google-datetime", "type": "string"}, "name": {"description": "The relative resource name of this asset. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/assets/{asset_id}\".", "type": "string"}, "resourceProperties": {"additionalProperties": {"type": "any"}, "description": "Resource managed properties. These properties are managed and defined by the Google Cloud resource and cannot be modified by the user.", "type": "object"}, "securityCenterProperties": {"$ref": "SecurityCenterProperties", "description": "Security Command Center managed properties. These properties are managed by Security Command Center and cannot be modified by the user."}, "securityMarks": {"$ref": "GoogleCloudSecuritycenterV1beta1SecurityMarks", "description": "User specified security marks. These marks are entirely managed by the user and come from the SecurityMarks resource that belongs to the asset."}, "updateTime": {"description": "The time at which the asset was last updated, added, or deleted in Security Command Center.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "AssetDiscoveryConfig": {"description": "The configuration used for Asset Discovery runs.", "id": "AssetDiscoveryConfig", "properties": {"inclusionMode": {"description": "The mode to use for filtering asset discovery.", "enum": ["INCLUSION_MODE_UNSPECIFIED", "INCLUDE_ONLY", "EXCLUDE"], "enumDescriptions": ["Unspecified. Setting the mode with this value will disable inclusion/exclusion filtering for Asset Discovery.", "Asset Discovery will capture only the resources within the projects specified. All other resources will be ignored.", "Asset Discovery will ignore all resources under the projects specified. All other resources will be retrieved."], "type": "string"}, "projectIds": {"description": "The project ids to use for filtering asset discovery.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Attack": {"description": "Information about DDoS attack volume and classification.", "id": "Attack", "properties": {"classification": {"description": "Type of attack, for example, 'SYN-flood', 'NTP-udp', or 'CHARGEN-udp'.", "type": "string"}, "volumeBps": {"deprecated": true, "description": "Total BPS (bytes per second) volume of attack. Deprecated - refer to volume_bps_long instead.", "format": "int32", "type": "integer"}, "volumeBpsLong": {"description": "Total BPS (bytes per second) volume of attack.", "format": "int64", "type": "string"}, "volumePps": {"deprecated": true, "description": "Total PPS (packets per second) volume of attack. Deprecated - refer to volume_pps_long instead.", "format": "int32", "type": "integer"}, "volumePpsLong": {"description": "Total PPS (packets per second) volume of attack.", "format": "int64", "type": "string"}}, "type": "object"}, "AttackExposure": {"description": "An attack exposure contains the results of an attack path simulation run.", "id": "AttackExposure", "properties": {"attackExposureResult": {"description": "The resource name of the attack path simulation result that contains the details regarding this attack exposure score. Example: `organizations/123/simulations/456/attackExposureResults/789`", "type": "string"}, "exposedHighValueResourcesCount": {"description": "The number of high value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "exposedLowValueResourcesCount": {"description": "The number of high value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "exposedMediumValueResourcesCount": {"description": "The number of medium value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "latestCalculationTime": {"description": "The most recent time the attack exposure was updated on this finding.", "format": "google-datetime", "type": "string"}, "score": {"description": "A number between 0 (inclusive) and infinity that represents how important this finding is to remediate. The higher the score, the more important it is to remediate.", "format": "double", "type": "number"}, "state": {"description": "What state this AttackExposure is in. This captures whether or not an attack exposure has been calculated or not.", "enum": ["STATE_UNSPECIFIED", "CALCULATED", "NOT_CALCULATED"], "enumDescriptions": ["The state is not specified.", "The attack exposure has been calculated.", "The attack exposure has not been calculated."], "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AwsAccount": {"description": "An AWS account that is a member of an organization.", "id": "AwsAccount", "properties": {"id": {"description": "The unique identifier (ID) of the account, containing exactly 12 digits.", "type": "string"}, "name": {"description": "The friendly name of this account.", "type": "string"}}, "type": "object"}, "AwsMetadata": {"description": "AWS metadata associated with the resource, only applicable if the finding's cloud provider is Amazon Web Services.", "id": "AwsMetadata", "properties": {"account": {"$ref": "AwsAccount", "description": "The AWS account associated with the resource."}, "organization": {"$ref": "AwsOrganization", "description": "The AWS organization associated with the resource."}, "organizationalUnits": {"description": "A list of AWS organizational units associated with the resource, ordered from lowest level (closest to the account) to highest level.", "items": {"$ref": "AwsOrganizationalUnit"}, "type": "array"}}, "type": "object"}, "AwsOrganization": {"description": "An organization is a collection of accounts that are centrally managed together using consolidated billing, organized hierarchically with organizational units (OUs), and controlled with policies.", "id": "AwsOrganization", "properties": {"id": {"description": "The unique identifier (ID) for the organization. The regex pattern for an organization ID string requires \"o-\" followed by from 10 to 32 lowercase letters or digits.", "type": "string"}}, "type": "object"}, "AwsOrganizationalUnit": {"description": "An Organizational Unit (OU) is a container of AWS accounts within a root of an organization. Policies that are attached to an OU apply to all accounts contained in that OU and in any child OUs.", "id": "AwsOrganizationalUnit", "properties": {"id": {"description": "The unique identifier (ID) associated with this OU. The regex pattern for an organizational unit ID string requires \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that contains the OU). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits. For example, \"ou-ab12-cd34ef56\".", "type": "string"}, "name": {"description": "The friendly name of the OU.", "type": "string"}}, "type": "object"}, "AzureManagementGroup": {"description": "Represents an Azure management group.", "id": "AzureManagementGroup", "properties": {"displayName": {"description": "The display name of the Azure management group.", "type": "string"}, "id": {"description": "The UUID of the Azure management group, for example, `*************-0000-0000-************`.", "type": "string"}}, "type": "object"}, "AzureMetadata": {"description": "Azure metadata associated with the resource, only applicable if the finding's cloud provider is Microsoft Azure.", "id": "AzureMetadata", "properties": {"managementGroups": {"description": "A list of Azure management groups associated with the resource, ordered from lowest level (closest to the subscription) to highest level.", "items": {"$ref": "AzureManagementGroup"}, "type": "array"}, "resourceGroup": {"$ref": "AzureResourceGroup", "description": "The Azure resource group associated with the resource."}, "subscription": {"$ref": "AzureSubscription", "description": "The Azure subscription associated with the resource."}, "tenant": {"$ref": "AzureTenant", "description": "The Azure Entra tenant associated with the resource."}}, "type": "object"}, "AzureResourceGroup": {"description": "Represents an Azure resource group.", "id": "AzureResourceGroup", "properties": {"id": {"description": "The ID of the Azure resource group.", "type": "string"}, "name": {"description": "The name of the Azure resource group. This is not a UUID.", "type": "string"}}, "type": "object"}, "AzureSubscription": {"description": "Represents an Azure subscription.", "id": "AzureSubscription", "properties": {"displayName": {"description": "The display name of the Azure subscription.", "type": "string"}, "id": {"description": "The UUID of the Azure subscription, for example, `291bba3f-e0a5-47bc-a099-3bdcb2a50a05`.", "type": "string"}}, "type": "object"}, "AzureTenant": {"description": "Represents a Microsoft Entra tenant.", "id": "AzureTenant", "properties": {"displayName": {"description": "The display name of the Azure tenant.", "type": "string"}, "id": {"description": "The ID of the Microsoft Entra tenant, for example, \"a11aaa11-aa11-1aa1-11aa-1aaa11a\".", "type": "string"}}, "type": "object"}, "BackupDisasterRecovery": {"description": "Information related to Google Cloud Backup and DR Service findings.", "id": "BackupDisasterRecovery", "properties": {"appliance": {"description": "The name of the Backup and DR appliance that captures, moves, and manages the lifecycle of backup data. For example, `backup-server-57137`.", "type": "string"}, "applications": {"description": "The names of Backup and DR applications. An application is a VM, database, or file system on a managed host monitored by a backup and recovery appliance. For example, `centos7-01-vol00`, `centos7-01-vol01`, `centos7-01-vol02`.", "items": {"type": "string"}, "type": "array"}, "backupCreateTime": {"description": "The timestamp at which the Backup and DR backup was created.", "format": "google-datetime", "type": "string"}, "backupTemplate": {"description": "The name of a Backup and DR template which comprises one or more backup policies. See the [Backup and DR documentation](https://cloud.google.com/backup-disaster-recovery/docs/concepts/backup-plan#temp) for more information. For example, `snap-ov`.", "type": "string"}, "backupType": {"description": "The backup type of the Backup and DR image. For example, `Snapshot`, `Remote Snapshot`, `OnVault`.", "type": "string"}, "host": {"description": "The name of a Backup and DR host, which is managed by the backup and recovery appliance and known to the management console. The host can be of type Generic (for example, Compute Engine, SQL Server, Oracle DB, SMB file system, etc.), vCenter, or an ESX server. See the [Backup and DR documentation on hosts](https://cloud.google.com/backup-disaster-recovery/docs/configuration/manage-hosts-and-their-applications) for more information. For example, `centos7-01`.", "type": "string"}, "policies": {"description": "The names of Backup and DR policies that are associated with a template and that define when to run a backup, how frequently to run a backup, and how long to retain the backup image. For example, `onvaults`.", "items": {"type": "string"}, "type": "array"}, "policyOptions": {"description": "The names of Backup and DR advanced policy options of a policy applying to an application. See the [Backup and DR documentation on policy options](https://cloud.google.com/backup-disaster-recovery/docs/create-plan/policy-settings). For example, `skipofflineappsincongrp, nounmap`.", "items": {"type": "string"}, "type": "array"}, "profile": {"description": "The name of the Backup and DR resource profile that specifies the storage media for backups of application and VM data. See the [Backup and DR documentation on profiles](https://cloud.google.com/backup-disaster-recovery/docs/concepts/backup-plan#profile). For example, `GCP`.", "type": "string"}, "storagePool": {"description": "The name of the Backup and DR storage pool that the backup and recovery appliance is storing data in. The storage pool could be of type Cloud, Primary, Snapshot, or OnVault. See the [Backup and DR documentation on storage pools](https://cloud.google.com/backup-disaster-recovery/docs/concepts/storage-pools). For example, `DiskPoolOne`.", "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "Chokepoint": {"description": "Contains details about a chokepoint, which is a resource or resource group where high-risk attack paths converge, based on [attack path simulations] (https://cloud.google.com/security-command-center/docs/attack-exposure-learn#attack_path_simulations).", "id": "Chokepoint", "properties": {"relatedFindings": {"description": "List of resource names of findings associated with this chokepoint. For example, organizations/123/sources/456/findings/789. This list will have at most 100 findings.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CloudArmor": {"description": "Fields related to Google Cloud Armor findings.", "id": "CloudArmor", "properties": {"adaptiveProtection": {"$ref": "AdaptiveProtection", "description": "Information about potential Layer 7 DDoS attacks identified by [Google Cloud Armor Adaptive Protection](https://cloud.google.com/armor/docs/adaptive-protection-overview)."}, "attack": {"$ref": "Attack", "description": "Information about DDoS attack volume and classification."}, "duration": {"description": "Duration of attack from the start until the current moment (updated every 5 minutes).", "format": "google-duration", "type": "string"}, "requests": {"$ref": "Requests", "description": "Information about incoming requests evaluated by [Google Cloud Armor security policies](https://cloud.google.com/armor/docs/security-policy-overview)."}, "securityPolicy": {"$ref": "SecurityPolicy", "description": "Information about the [Google Cloud Armor security policy](https://cloud.google.com/armor/docs/security-policy-overview) relevant to the finding."}, "threatVector": {"description": "Distinguish between volumetric & protocol DDoS attack and application layer attacks. For example, \"L3_4\" for Layer 3 and Layer 4 DDoS attacks, or \"L_7\" for Layer 7 DDoS attacks.", "type": "string"}}, "type": "object"}, "CloudDlpDataProfile": {"description": "The [data profile](https://cloud.google.com/dlp/docs/data-profiles) associated with the finding.", "id": "CloudDlpDataProfile", "properties": {"dataProfile": {"description": "Name of the data profile, for example, `projects/123/locations/europe/tableProfiles/8383929`.", "type": "string"}, "parentType": {"description": "The resource hierarchy level at which the data profile was generated.", "enum": ["PARENT_TYPE_UNSPECIFIED", "ORGANIZATION", "PROJECT"], "enumDescriptions": ["Unspecified parent type.", "Organization-level configurations.", "Project-level configurations."], "type": "string"}}, "type": "object"}, "CloudDlpInspection": {"description": "Details about the Cloud Data Loss Prevention (Cloud DLP) [inspection job](https://cloud.google.com/dlp/docs/concepts-job-triggers) that produced the finding.", "id": "CloudDlpInspection", "properties": {"fullScan": {"description": "Whether Cloud DLP scanned the complete resource or a sampled subset.", "type": "boolean"}, "infoType": {"description": "The type of information (or *[infoType](https://cloud.google.com/dlp/docs/infotypes-reference)*) found, for example, `EMAIL_ADDRESS` or `STREET_ADDRESS`.", "type": "string"}, "infoTypeCount": {"description": "The number of times Cloud DLP found this infoType within this job and resource.", "format": "int64", "type": "string"}, "inspectJob": {"description": "Name of the inspection job, for example, `projects/123/locations/europe/dlpJobs/i-8383929`.", "type": "string"}}, "type": "object"}, "CloudLoggingEntry": {"description": "Metadata taken from a [Cloud Logging LogEntry](https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry)", "id": "CloudLoggingEntry", "properties": {"insertId": {"description": "A unique identifier for the log entry.", "type": "string"}, "logId": {"description": "The type of the log (part of `log_name`. `log_name` is the resource name of the log to which this log entry belongs). For example: `cloudresourcemanager.googleapis.com/activity`. Note that this field is not URL-encoded, unlike the `LOG_ID` field in `LogEntry`.", "type": "string"}, "resourceContainer": {"description": "The organization, folder, or project of the monitored resource that produced this log entry.", "type": "string"}, "timestamp": {"description": "The time the event described by the log entry occurred.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Compliance": {"description": "Contains compliance information about a security standard indicating unmet recommendations.", "id": "Compliance", "properties": {"ids": {"description": "Policies within the standard or benchmark, for example, A.12.4.1", "items": {"type": "string"}, "type": "array"}, "standard": {"description": "Industry-wide compliance standards or benchmarks, such as CIS, PCI, and OWASP.", "type": "string"}, "version": {"description": "Version of the standard or benchmark, for example, 1.1", "type": "string"}}, "type": "object"}, "Connection": {"description": "Contains information about the IP connection associated with the finding.", "id": "Connection", "properties": {"destinationIp": {"description": "Destination IP address. Not present for sockets that are listening and not connected.", "type": "string"}, "destinationPort": {"description": "Destination port. Not present for sockets that are listening and not connected.", "format": "int32", "type": "integer"}, "protocol": {"description": "IANA Internet Protocol Number such as TCP(6) and UDP(17).", "enum": ["PROTOCOL_UNSPECIFIED", "ICMP", "TCP", "UDP", "GRE", "ESP"], "enumDescriptions": ["Unspecified protocol (not HOPOPT).", "Internet Control Message Protocol.", "Transmission Control Protocol.", "User Datagram Protocol.", "Generic Routing Encapsulation.", "Encap Security Payload."], "type": "string"}, "sourceIp": {"description": "Source IP address.", "type": "string"}, "sourcePort": {"description": "Source port.", "format": "int32", "type": "integer"}}, "type": "object"}, "Contact": {"description": "The email address of a contact.", "id": "Contact", "properties": {"email": {"description": "An email address. For example, \"`<EMAIL>`\".", "type": "string"}}, "type": "object"}, "ContactDetails": {"description": "Details about specific contacts", "id": "ContactDetails", "properties": {"contacts": {"description": "A list of contacts", "items": {"$ref": "Contact"}, "type": "array"}}, "type": "object"}, "Container": {"description": "Container associated with the finding.", "id": "Container", "properties": {"createTime": {"description": "The time that the container was created.", "format": "google-datetime", "type": "string"}, "imageId": {"description": "Optional container image ID, if provided by the container runtime. Uniquely identifies the container image launched using a container image digest.", "type": "string"}, "labels": {"description": "Container labels, as provided by the container runtime.", "items": {"$ref": "Label"}, "type": "array"}, "name": {"description": "Name of the container.", "type": "string"}, "uri": {"description": "Container image URI provided when configuring a pod or container. This string can identify a container image version using mutable tags.", "type": "string"}}, "type": "object"}, "Cve": {"description": "CVE stands for Common Vulnerabilities and Exposures. Information from the [CVE record](https://www.cve.org/ResourcesSupport/Glossary) that describes this vulnerability.", "id": "Cve", "properties": {"cvssv3": {"$ref": "Cvssv3", "description": "Describe Common Vulnerability Scoring System specified at https://www.first.org/cvss/v3.1/specification-document"}, "exploitReleaseDate": {"description": "Date the first publicly available exploit or PoC was released.", "format": "google-datetime", "type": "string"}, "exploitationActivity": {"description": "The exploitation activity of the vulnerability in the wild.", "enum": ["EXPLOITATION_ACTIVITY_UNSPECIFIED", "WIDE", "CONFIRMED", "AVAILABLE", "ANTICIPATED", "NO_KNOWN"], "enumDescriptions": ["Invalid or empty value.", "Exploitation has been reported or confirmed to widely occur.", "Limited reported or confirmed exploitation activities.", "Exploit is publicly available.", "No known exploitation activity, but has a high potential for exploitation.", "No known exploitation activity."], "type": "string"}, "firstExploitationDate": {"description": "Date of the earliest known exploitation.", "format": "google-datetime", "type": "string"}, "id": {"description": "The unique identifier for the vulnerability. e.g. CVE-2021-34527", "type": "string"}, "impact": {"description": "The potential impact of the vulnerability if it was to be exploited.", "enum": ["RISK_RATING_UNSPECIFIED", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "enumDescriptions": ["Invalid or empty value.", "Exploitation would have little to no security impact.", "Exploitation would enable attackers to perform activities, or could allow attackers to have a direct impact, but would require additional steps.", "Exploitation would enable attackers to have a notable direct impact without needing to overcome any major mitigating factors.", "Exploitation would fundamentally undermine the security of affected systems, enable actors to perform significant attacks with minimal effort, with little to no mitigating factors to overcome."], "type": "string"}, "observedInTheWild": {"description": "Whether or not the vulnerability has been observed in the wild.", "type": "boolean"}, "references": {"description": "Additional information about the CVE. e.g. https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527", "items": {"$ref": "Reference"}, "type": "array"}, "upstreamFixAvailable": {"description": "Whether upstream fix is available for the CVE.", "type": "boolean"}, "zeroDay": {"description": "Whether or not the vulnerability was zero day when the finding was published.", "type": "boolean"}}, "type": "object"}, "Cvssv3": {"description": "Common Vulnerability Scoring System version 3.", "id": "Cvssv3", "properties": {"attackComplexity": {"description": "This metric describes the conditions beyond the attacker's control that must exist in order to exploit the vulnerability.", "enum": ["ATTACK_COMPLEXITY_UNSPECIFIED", "ATTACK_COMPLEXITY_LOW", "ATTACK_COMPLEXITY_HIGH"], "enumDescriptions": ["Invalid value.", "Specialized access conditions or extenuating circumstances do not exist. An attacker can expect repeatable success when attacking the vulnerable component.", "A successful attack depends on conditions beyond the attacker's control. That is, a successful attack cannot be accomplished at will, but requires the attacker to invest in some measurable amount of effort in preparation or execution against the vulnerable component before a successful attack can be expected."], "type": "string"}, "attackVector": {"description": "Base Metrics Represents the intrinsic characteristics of a vulnerability that are constant over time and across user environments. This metric reflects the context by which vulnerability exploitation is possible.", "enum": ["ATTACK_VECTOR_UNSPECIFIED", "ATTACK_VECTOR_NETWORK", "ATTACK_VECTOR_ADJACENT", "ATTACK_VECTOR_LOCAL", "ATTACK_VECTOR_PHYSICAL"], "enumDescriptions": ["Invalid value.", "The vulnerable component is bound to the network stack and the set of possible attackers extends beyond the other options listed below, up to and including the entire Internet.", "The vulnerable component is bound to the network stack, but the attack is limited at the protocol level to a logically adjacent topology.", "The vulnerable component is not bound to the network stack and the attacker's path is via read/write/execute capabilities.", "The attack requires the attacker to physically touch or manipulate the vulnerable component."], "type": "string"}, "availabilityImpact": {"description": "This metric measures the impact to the availability of the impacted component resulting from a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "baseScore": {"description": "The base score is a function of the base metric scores.", "format": "double", "type": "number"}, "confidentialityImpact": {"description": "This metric measures the impact to the confidentiality of the information resources managed by a software component due to a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "integrityImpact": {"description": "This metric measures the impact to integrity of a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "privilegesRequired": {"description": "This metric describes the level of privileges an attacker must possess before successfully exploiting the vulnerability.", "enum": ["PRIVILEGES_REQUIRED_UNSPECIFIED", "PRIVILEGES_REQUIRED_NONE", "PRIVILEGES_REQUIRED_LOW", "PRIVILEGES_REQUIRED_HIGH"], "enumDescriptions": ["Invalid value.", "The attacker is unauthorized prior to attack, and therefore does not require any access to settings or files of the vulnerable system to carry out an attack.", "The attacker requires privileges that provide basic user capabilities that could normally affect only settings and files owned by a user. Alternatively, an attacker with Low privileges has the ability to access only non-sensitive resources.", "The attacker requires privileges that provide significant (e.g., administrative) control over the vulnerable component allowing access to component-wide settings and files."], "type": "string"}, "scope": {"description": "The Scope metric captures whether a vulnerability in one vulnerable component impacts resources in components beyond its security scope.", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_UNCHANGED", "SCOPE_CHANGED"], "enumDescriptions": ["Invalid value.", "An exploited vulnerability can only affect resources managed by the same security authority.", "An exploited vulnerability can affect resources beyond the security scope managed by the security authority of the vulnerable component."], "type": "string"}, "userInteraction": {"description": "This metric captures the requirement for a human user, other than the attacker, to participate in the successful compromise of the vulnerable component.", "enum": ["USER_INTERACTION_UNSPECIFIED", "USER_INTERACTION_NONE", "USER_INTERACTION_REQUIRED"], "enumDescriptions": ["Invalid value.", "The vulnerable system can be exploited without interaction from any user.", "Successful exploitation of this vulnerability requires a user to take some action before the vulnerability can be exploited."], "type": "string"}}, "type": "object"}, "Cwe": {"description": "CWE stands for Common Weakness Enumeration. Information about this weakness, as described by [CWE](https://cwe.mitre.org/).", "id": "Cwe", "properties": {"id": {"description": "The CWE identifier, e.g. CWE-94", "type": "string"}, "references": {"description": "Any reference to the details on the CWE, for example, https://cwe.mitre.org/data/definitions/94.html", "items": {"$ref": "Reference"}, "type": "array"}}, "type": "object"}, "DataAccessEvent": {"description": "Details about a data access attempt made by a principal not authorized under applicable data security policy.", "id": "DataAccessEvent", "properties": {"eventId": {"description": "Unique identifier for data access event.", "type": "string"}, "eventTime": {"description": "Timestamp of data access event.", "format": "google-datetime", "type": "string"}, "operation": {"description": "The operation performed by the principal to access the data.", "enum": ["OPERATION_UNSPECIFIED", "READ", "MOVE", "COPY"], "enumDescriptions": ["The operation is unspecified.", "Represents a read operation.", "Represents a move operation.", "Represents a copy operation."], "type": "string"}, "principalEmail": {"description": "The email address of the principal that accessed the data. The principal could be a user account, service account, Google group, or other.", "type": "string"}}, "type": "object"}, "DataFlowEvent": {"description": "Details about a data flow event, in which either the data is moved to or is accessed from a non-compliant geo-location, as defined in the applicable data security policy.", "id": "DataFlowEvent", "properties": {"eventId": {"description": "Unique identifier for data flow event.", "type": "string"}, "eventTime": {"description": "Timestamp of data flow event.", "format": "google-datetime", "type": "string"}, "operation": {"description": "The operation performed by the principal for the data flow event.", "enum": ["OPERATION_UNSPECIFIED", "READ", "MOVE", "COPY"], "enumDescriptions": ["The operation is unspecified.", "Represents a read operation.", "Represents a move operation.", "Represents a copy operation."], "type": "string"}, "principalEmail": {"description": "The email address of the principal that initiated the data flow event. The principal could be a user account, service account, Google group, or other.", "type": "string"}, "violatedLocation": {"description": "Non-compliant location of the principal or the data destination.", "type": "string"}}, "type": "object"}, "DataRetentionDeletionEvent": {"description": "Details about data retention deletion violations, in which the data is non-compliant based on their retention or deletion time, as defined in the applicable data security policy. The Data Retention Deletion (DRD) control is a control of the DSPM (Data Security Posture Management) suite that enables organizations to manage data retention and deletion policies in compliance with regulations, such as GDPR and CRPA. DRD supports two primary policy types: maximum storage length (max TTL) and minimum storage length (min TTL). Both are aimed at helping organizations meet regulatory and data management commitments.", "id": "DataRetentionDeletionEvent", "properties": {"dataObjectCount": {"description": "Number of objects that violated the policy for this resource. If the number is less than 1,000, then the value of this field is the exact number. If the number of objects that violated the policy is greater than or equal to 1,000, then the value of this field is 1000.", "format": "int64", "type": "string"}, "eventDetectionTime": {"description": "Timestamp indicating when the event was detected.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "Type of the DRD event.", "enum": ["EVENT_TYPE_UNSPECIFIED", "EVENT_TYPE_MAX_TTL_EXCEEDED"], "enumDescriptions": ["Unspecified event type.", "The maximum retention time has been exceeded."], "type": "string"}, "maxRetentionAllowed": {"description": "Maximum duration of retention allowed from the DRD control. This comes from the DRD control where users set a max TTL for their data. For example, suppose that a user sets the max TTL for a Cloud Storage bucket to 90 days. However, an object in that bucket is 100 days old. In this case, a DataRetentionDeletionEvent will be generated for that Cloud Storage bucket, and the max_retention_allowed is 90 days.", "format": "google-duration", "type": "string"}}, "type": "object"}, "Database": {"description": "Represents database access information, such as queries. A database may be a sub-resource of an instance (as in the case of Cloud SQL instances or Cloud Spanner instances), or the database instance itself. Some database resources might not have the [full resource name](https://google.aip.dev/122#full-resource-names) populated because these resource types, such as Cloud SQL databases, are not yet supported by Cloud Asset Inventory. In these cases only the display name is provided.", "id": "Database", "properties": {"displayName": {"description": "The human-readable name of the database that the user connected to.", "type": "string"}, "grantees": {"description": "The target usernames, roles, or groups of an SQL privilege grant, which is not an IAM policy change.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Some database resources may not have the [full resource name](https://google.aip.dev/122#full-resource-names) populated because these resource types are not yet supported by Cloud Asset Inventory (e.g. Cloud SQL databases). In these cases only the display name will be provided. The [full resource name](https://google.aip.dev/122#full-resource-names) of the database that the user connected to, if it is supported by Cloud Asset Inventory.", "type": "string"}, "query": {"description": "The SQL statement that is associated with the database access.", "type": "string"}, "userName": {"description": "The username used to connect to the database. The username might not be an IAM principal and does not have a set format.", "type": "string"}, "version": {"description": "The version of the database, for example, POSTGRES_14. See [the complete list](https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1/SqlDatabaseVersion).", "type": "string"}}, "type": "object"}, "Denied": {"description": "Denied IP rule.", "id": "Denied", "properties": {"ipRules": {"description": "Optional. Optional list of denied IP rules.", "items": {"$ref": "IpRule"}, "type": "array"}}, "type": "object"}, "Detection": {"description": "Memory hash detection contributing to the binary family match.", "id": "Detection", "properties": {"binary": {"description": "The name of the binary associated with the memory hash signature detection.", "type": "string"}, "percentPagesMatched": {"description": "The percentage of memory page hashes in the signature that were matched.", "format": "double", "type": "number"}}, "type": "object"}, "Disk": {"description": "Contains information about the disk associated with the finding.", "id": "Disk", "properties": {"name": {"description": "The name of the disk, for example, \"https://www.googleapis.com/compute/v1/projects/{project-id}/zones/{zone-id}/disks/{disk-id}\".", "type": "string"}}, "type": "object"}, "DiskPath": {"description": "Path of the file in terms of underlying disk/partition identifiers.", "id": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"partitionUuid": {"description": "UUID of the partition (format https://wiki.archlinux.org/title/persistent_block_device_naming#by-uuid)", "type": "string"}, "relativePath": {"description": "Relative path of the file in the partition as a JSON encoded string. Example: /home/<USER>/executable_file.sh", "type": "string"}}, "type": "object"}, "DynamicMuteRecord": {"description": "The record of a dynamic mute rule that matches the finding.", "id": "DynamicMuteRecord", "properties": {"matchTime": {"description": "When the dynamic mute rule first matched the finding.", "format": "google-datetime", "type": "string"}, "muteConfig": {"description": "The relative resource name of the mute rule, represented by a mute config, that created this record, for example `organizations/123/muteConfigs/mymuteconfig` or `organizations/123/locations/global/muteConfigs/mymuteconfig`.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EnvironmentVariable": {"description": "A name-value pair representing an environment variable used in an operating system process.", "id": "EnvironmentVariable", "properties": {"name": {"description": "Environment variable name as a JSON encoded string.", "type": "string"}, "val": {"description": "Environment variable value as a JSON encoded string.", "type": "string"}}, "type": "object"}, "ExfilResource": {"description": "Resource where data was exfiltrated from or exfiltrated to.", "id": "ExfilResource", "properties": {"components": {"description": "Subcomponents of the asset that was exfiltrated, like URIs used during exfiltration, table names, databases, and filenames. For example, multiple tables might have been exfiltrated from the same Cloud SQL instance, or multiple files might have been exfiltrated from the same Cloud Storage bucket.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "The resource's [full resource name](https://cloud.google.com/apis/design/resource_names#full_resource_name).", "type": "string"}}, "type": "object"}, "Exfiltration": {"description": "Exfiltration represents a data exfiltration attempt from one or more sources to one or more targets. The `sources` attribute lists the sources of the exfiltrated data. The `targets` attribute lists the destinations the data was copied to.", "id": "Exfiltration", "properties": {"sources": {"description": "If there are multiple sources, then the data is considered \"joined\" between them. For instance, BigQuery can join multiple tables, and each table would be considered a source.", "items": {"$ref": "ExfilResource"}, "type": "array"}, "targets": {"description": "If there are multiple targets, each target would get a complete copy of the \"joined\" source data.", "items": {"$ref": "ExfilResource"}, "type": "array"}, "totalExfiltratedBytes": {"description": "Total exfiltrated bytes processed for the entire job.", "format": "int64", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "File": {"description": "File information about the related binary/library used by an executable, or the script used by a script interpreter", "id": "File", "properties": {"contents": {"description": "Prefix of the file contents as a JSON-encoded string.", "type": "string"}, "diskPath": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "Path of the file in terms of underlying disk/partition identifiers."}, "hashedSize": {"description": "The length in bytes of the file prefix that was hashed. If hashed_size == size, any hashes reported represent the entire file.", "format": "int64", "type": "string"}, "partiallyHashed": {"description": "True when the hash covers only a prefix of the file.", "type": "boolean"}, "path": {"description": "Absolute path of the file as a JSON encoded string.", "type": "string"}, "sha256": {"description": "SHA256 hash of the first hashed_size bytes of the file encoded as a hex string. If hashed_size == size, sha256 represents the SHA256 hash of the entire file.", "type": "string"}, "size": {"description": "Size of the file in bytes.", "format": "int64", "type": "string"}}, "type": "object"}, "Finding": {"description": "Security Command Center finding. A finding is a record of assessment data like security, risk, health, or privacy, that is ingested into Security Command Center for presentation, notification, analysis, policy testing, and enforcement. For example, a cross-site scripting (XSS) vulnerability in an App Engine application is a finding.", "id": "Finding", "properties": {"access": {"$ref": "Access", "description": "Access details associated with the finding, such as more information on the caller, which method was accessed, and from where."}, "affectedResources": {"$ref": "AffectedResources", "description": "AffectedResources associated with the finding."}, "application": {"$ref": "Application", "description": "Represents an application associated with the finding."}, "attackExposure": {"$ref": "AttackExposure", "description": "The results of an attack path simulation relevant to this finding."}, "backupDisasterRecovery": {"$ref": "BackupDisasterRecovery", "description": "Fields related to Backup and DR findings."}, "canonicalName": {"description": "The canonical name of the finding. It's either \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\", \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}\" or \"projects/{project_number}/sources/{source_id}/findings/{finding_id}\", depending on the closest CRM ancestor of the resource associated with the finding.", "type": "string"}, "category": {"description": "The additional taxonomy group within findings from a given source. This field is immutable after creation time. Example: \"XSS_FLASH_INJECTION\"", "type": "string"}, "chokepoint": {"$ref": "Chokepoint", "description": "Contains details about a chokepoint, which is a resource or resource group where high-risk attack paths converge, based on [attack path simulations] (https://cloud.google.com/security-command-center/docs/attack-exposure-learn#attack_path_simulations). This field cannot be updated. Its value is ignored in all update requests."}, "cloudArmor": {"$ref": "CloudArmor", "description": "Fields related to Cloud Armor findings."}, "cloudDlpDataProfile": {"$ref": "CloudDlpDataProfile", "description": "Cloud DLP data profile that is associated with the finding."}, "cloudDlpInspection": {"$ref": "CloudDlpInspection", "description": "Cloud Data Loss Prevention (Cloud DLP) inspection results that are associated with the finding."}, "compliances": {"description": "Contains compliance information for security standards associated to the finding.", "items": {"$ref": "Compliance"}, "type": "array"}, "connections": {"description": "Contains information about the IP connection associated with the finding.", "items": {"$ref": "Connection"}, "type": "array"}, "contacts": {"additionalProperties": {"$ref": "ContactDetails"}, "description": "Output only. Map containing the points of contact for the given finding. The key represents the type of contact, while the value contains a list of all the contacts that pertain. Please refer to: https://cloud.google.com/resource-manager/docs/managing-notification-contacts#notification-categories { \"security\": { \"contacts\": [ { \"email\": \"<EMAIL>\" }, { \"email\": \"<EMAIL>\" } ] } }", "readOnly": true, "type": "object"}, "containers": {"description": "Containers associated with the finding. This field provides information for both Kubernetes and non-Kubernetes containers.", "items": {"$ref": "Container"}, "type": "array"}, "createTime": {"description": "The time at which the finding was created in Security Command Center.", "format": "google-datetime", "type": "string"}, "dataAccessEvents": {"description": "Data access events associated with the finding.", "items": {"$ref": "DataAccessEvent"}, "type": "array"}, "dataFlowEvents": {"description": "Data flow events associated with the finding.", "items": {"$ref": "DataFlowEvent"}, "type": "array"}, "dataRetentionDeletionEvents": {"description": "Data retention deletion events associated with the finding.", "items": {"$ref": "DataRetentionDeletionEvent"}, "type": "array"}, "database": {"$ref": "Database", "description": "Database associated with the finding."}, "description": {"description": "Contains more details about the finding.", "type": "string"}, "disk": {"$ref": "Disk", "description": "<PERSON><PERSON> associated with the finding."}, "eventTime": {"description": "The time the finding was first detected. If an existing finding is updated, then this is the time the update occurred. For example, if the finding represents an open firewall, this property captures the time the detector believes the firewall became open. The accuracy is determined by the detector. If the finding is later resolved, then this time reflects when the finding was resolved. This must not be set to a value greater than the current timestamp.", "format": "google-datetime", "type": "string"}, "exfiltration": {"$ref": "Exfiltration", "description": "Represents exfiltrations associated with the finding."}, "externalSystems": {"additionalProperties": {"$ref": "GoogleCloudSecuritycenterV1ExternalSystem"}, "description": "Output only. Third party SIEM/SOAR fields within SCC, contains external system information and external system finding fields.", "readOnly": true, "type": "object"}, "externalUri": {"description": "The URI that, if available, points to a web page outside of Security Command Center where additional information about the finding can be found. This field is guaranteed to be either empty or a well formed URL.", "type": "string"}, "files": {"description": "File associated with the finding.", "items": {"$ref": "File"}, "type": "array"}, "findingClass": {"description": "The class of the finding.", "enum": ["FINDING_CLASS_UNSPECIFIED", "THREAT", "VULNERABILITY", "MISCONFIGURATION", "OBSERVATION", "SCC_ERROR", "POSTURE_VIOLATION", "TOXIC_COMBINATION", "SENSITIVE_DATA_RISK", "CHOKEPOINT"], "enumDescriptions": ["Unspecified finding class.", "Describes unwanted or malicious activity.", "Describes a potential weakness in software that increases risk to Confidentiality & Integrity & Availability.", "Describes a potential weakness in cloud resource/asset configuration that increases risk.", "Describes a security observation that is for informational purposes.", "Describes an error that prevents some SCC functionality.", "Describes a potential security risk due to a change in the security posture.", "Describes a group of security issues that, when the issues occur together, represent a greater risk than when the issues occur independently. A group of such issues is referred to as a toxic combination.", "Describes a potential security risk to data assets that contain sensitive data.", "Describes a resource or resource group where high risk attack paths converge, based on attack path simulations (APS)."], "type": "string"}, "groupMemberships": {"description": "Contains details about groups of which this finding is a member. A group is a collection of findings that are related in some way. This field cannot be updated. Its value is ignored in all update requests.", "items": {"$ref": "GroupMembership"}, "type": "array"}, "iamBindings": {"description": "Represents IAM bindings associated with the finding.", "items": {"$ref": "<PERSON>am<PERSON><PERSON><PERSON>"}, "type": "array"}, "indicator": {"$ref": "Indicator", "description": "Represents what's commonly known as an *indicator of compromise* (IoC) in computer forensics. This is an artifact observed on a network or in an operating system that, with high confidence, indicates a computer intrusion. For more information, see [Indicator of compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise)."}, "ipRules": {"$ref": "IpRules", "description": "IP rules associated with the finding."}, "job": {"$ref": "Job", "description": "Job associated with the finding."}, "kernelRootkit": {"$ref": "KernelRootkit", "description": "Signature of the kernel rootkit."}, "kubernetes": {"$ref": "Kubernetes", "description": "Kubernetes resources associated with the finding."}, "loadBalancers": {"description": "The load balancers associated with the finding.", "items": {"$ref": "LoadBalancer"}, "type": "array"}, "logEntries": {"description": "Log entries that are relevant to the finding.", "items": {"$ref": "LogEntry"}, "type": "array"}, "mitreAttack": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "MITRE ATT&CK tactics and techniques related to this finding. See: https://attack.mitre.org"}, "moduleName": {"description": "Unique identifier of the module which generated the finding. Example: folders/598186756061/securityHealthAnalyticsSettings/customModules/56799441161885", "type": "string"}, "mute": {"description": "Indicates the mute state of a finding (either muted, unmuted or undefined). Unlike other attributes of a finding, a finding provider shouldn't set the value of mute.", "enum": ["MUTE_UNSPECIFIED", "MUTED", "UNMUTED", "UNDEFINED"], "enumDescriptions": ["Unspecified.", "Finding has been muted.", "Finding has been unmuted.", "Finding has never been muted/unmuted."], "type": "string"}, "muteInfo": {"$ref": "MuteInfo", "description": "Output only. The mute information regarding this finding.", "readOnly": true}, "muteInitiator": {"description": "Records additional information about the mute operation, for example, the [mute configuration](/security-command-center/docs/how-to-mute-findings) that muted the finding and the user who muted the finding.", "type": "string"}, "muteUpdateTime": {"description": "Output only. The most recent time this finding was muted or unmuted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "The [relative resource name](https://cloud.google.com/apis/design/resource_names#relative_resource_name) of the finding. Example: \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\", \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}\", \"projects/{project_id}/sources/{source_id}/findings/{finding_id}\".", "type": "string"}, "networks": {"description": "Represents the VPC networks that the resource is attached to.", "items": {"$ref": "Network"}, "type": "array"}, "nextSteps": {"description": "Steps to address the finding.", "type": "string"}, "notebook": {"$ref": "Notebook", "description": "Notebook associated with the finding."}, "orgPolicies": {"description": "Contains information about the org policies associated with the finding.", "items": {"$ref": "OrgPolicy"}, "type": "array"}, "parent": {"description": "The relative resource name of the source the finding belongs to. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name This field is immutable after creation time. For example: \"organizations/{organization_id}/sources/{source_id}\"", "type": "string"}, "parentDisplayName": {"description": "Output only. The human readable display name of the finding source such as \"Event Threat Detection\" or \"Security Health Analytics\".", "readOnly": true, "type": "string"}, "processes": {"description": "Represents operating system processes associated with the Finding.", "items": {"$ref": "Process"}, "type": "array"}, "resourceName": {"description": "For findings on Google Cloud resources, the full resource name of the Google Cloud resource this finding is for. See: https://cloud.google.com/apis/design/resource_names#full_resource_name When the finding is for a non-Google Cloud resource, the resourceName can be a customer or partner defined string. This field is immutable after creation time.", "type": "string"}, "securityMarks": {"$ref": "SecurityMarks", "description": "Output only. User specified security marks. These marks are entirely managed by the user and come from the SecurityMarks resource that belongs to the finding.", "readOnly": true}, "securityPosture": {"$ref": "SecurityPosture", "description": "The security posture associated with the finding."}, "severity": {"description": "The severity of the finding. This field is managed by the source that writes the finding.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["This value is used for findings when a source doesn't write a severity value.", "Vulnerability: A critical vulnerability is easily discoverable by an external actor, exploitable, and results in the direct ability to execute arbitrary code, exfiltrate data, and otherwise gain additional access and privileges to cloud resources and workloads. Examples include publicly accessible unprotected user data and public SSH access with weak or no passwords. Threat: Indicates a threat that is able to access, modify, or delete data or execute unauthorized code within existing resources.", "Vulnerability: A high risk vulnerability can be easily discovered and exploited in combination with other vulnerabilities in order to gain direct access and the ability to execute arbitrary code, exfiltrate data, and otherwise gain additional access and privileges to cloud resources and workloads. An example is a database with weak or no passwords that is only accessible internally. This database could easily be compromised by an actor that had access to the internal network. Threat: Indicates a threat that is able to create new computational resources in an environment but not able to access data or execute code in existing resources.", "Vulnerability: A medium risk vulnerability could be used by an actor to gain access to resources or privileges that enable them to eventually (through multiple steps or a complex exploit) gain access and the ability to execute arbitrary code or exfiltrate data. An example is a service account with access to more projects than it should have. If an actor gains access to the service account, they could potentially use that access to manipulate a project the service account was not intended to. Threat: Indicates a threat that is able to cause operational impact but may not access data or execute unauthorized code.", "Vulnerability: A low risk vulnerability hampers a security organization's ability to detect vulnerabilities or active threats in their deployment, or prevents the root cause investigation of security issues. An example is monitoring and logs being disabled for resource configurations and access. Threat: Indicates a threat that has obtained minimal access to an environment but is not able to access data, execute code, or create resources."], "type": "string"}, "sourceProperties": {"additionalProperties": {"type": "any"}, "description": "Source specific properties. These properties are managed by the source that writes the finding. The key names in the source_properties map must be between 1 and 255 characters, and must start with a letter and contain alphanumeric characters or underscores only.", "type": "object"}, "state": {"description": "The state of the finding.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "The finding requires attention and has not been addressed yet.", "The finding has been fixed, triaged as a non-issue or otherwise addressed and is no longer active."], "type": "string"}, "toxicCombination": {"$ref": "ToxicCombination", "description": "Contains details about a group of security issues that, when the issues occur together, represent a greater risk than when the issues occur independently. A group of such issues is referred to as a toxic combination. This field cannot be updated. Its value is ignored in all update requests."}, "vulnerability": {"$ref": "Vulnerability", "description": "Represents vulnerability-specific fields like CVE and CVSS scores. CVE stands for Common Vulnerabilities and Exposures (https://cve.mitre.org/about/)"}}, "type": "object"}, "Folder": {"description": "Message that contains the resource name and display name of a folder resource.", "id": "Folder", "properties": {"resourceFolder": {"description": "Full resource name of this folder. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourceFolderDisplayName": {"description": "The user defined display name for this folder.", "type": "string"}}, "type": "object"}, "GcpMetadata": {"description": "GCP metadata associated with the resource, only applicable if the finding's cloud provider is Google Cloud Platform.", "id": "GcpMetadata", "properties": {"folders": {"description": "Output only. Contains a Folder message for each folder in the assets ancestry. The first folder is the deepest nested folder, and the last folder is the folder directly under the Organization.", "items": {"$ref": "GoogleCloudSecuritycenterV2Folder"}, "readOnly": true, "type": "array"}, "organization": {"description": "The name of the organization that the resource belongs to.", "type": "string"}, "parent": {"description": "The full resource name of resource's parent.", "type": "string"}, "parentDisplayName": {"description": "The human readable name of resource's parent.", "type": "string"}, "project": {"description": "The full resource name of project that the resource belongs to.", "type": "string"}, "projectDisplayName": {"description": "The project ID that the resource belongs to.", "type": "string"}}, "type": "object"}, "Geolocation": {"description": "Represents a geographical location for a given access.", "id": "Geolocation", "properties": {"regionCode": {"description": "A CLDR.", "type": "string"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudSecuritycenterV1BigQueryExport": {"description": "Configures how to deliver Findings to BigQuery Instance.", "id": "GoogleCloudSecuritycenterV1BigQueryExport", "properties": {"createTime": {"description": "Output only. The time at which the BigQuery export was created. This field is set by the server and will be ignored if provided on export on creation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataset": {"description": "The dataset to write findings' updates to. Its format is \"projects/[project_id]/datasets/[bigquery_dataset_id]\". BigQuery Dataset unique ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_).", "type": "string"}, "description": {"description": "The description of the export (max of 1024 characters).", "type": "string"}, "filter": {"description": "Expression that defines the filter to apply across create/update events of findings. The expression is a list of zero or more restrictions combined via logical operators `AND` and `OR`. Parentheses are supported, and `OR` has higher precedence than `AND`. Restrictions have the form ` ` and may have a `-` character in front of them to indicate negation. The fields map to those defined in the corresponding resource. The supported operators are: * `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching, for strings. The supported value types are: * string literals in quotes. * integer literals without quotes. * boolean literals `true` and `false` without quotes.", "type": "string"}, "mostRecentEditor": {"description": "Output only. Email address of the user who last edited the BigQuery export. This field is set by the server and will be ignored if provided on export creation or update.", "readOnly": true, "type": "string"}, "name": {"description": "The relative resource name of this export. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name. Example format: \"organizations/{organization_id}/bigQueryExports/{export_id}\" Example format: \"folders/{folder_id}/bigQueryExports/{export_id}\" Example format: \"projects/{project_id}/bigQueryExports/{export_id}\" This field is provided in responses, and is ignored when provided in create requests.", "type": "string"}, "principal": {"description": "Output only. The service account that needs permission to create table and upload data to the BigQuery dataset.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the BigQuery export was updated. This field is set by the server and will be ignored if provided on export creation or update.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1Binding": {"description": "Represents a Kubernetes RoleBinding or ClusterRoleBinding.", "id": "GoogleCloudSecuritycenterV1Binding", "properties": {"name": {"description": "Name for the binding.", "type": "string"}, "ns": {"description": "Namespace for the binding.", "type": "string"}, "role": {"$ref": "Role", "description": "The Role or ClusterRole referenced by the binding."}, "subjects": {"description": "Represents one or more subjects that are bound to the role. Not always available for PATCH requests.", "items": {"$ref": "Subject"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV1BulkMuteFindingsResponse": {"description": "The response to a BulkMute request. Contains the LRO information.", "id": "GoogleCloudSecuritycenterV1BulkMuteFindingsResponse", "properties": {}, "type": "object"}, "GoogleCloudSecuritycenterV1CustomConfig": {"description": "Defines the properties in a custom module configuration for Security Health Analytics. Use the custom module configuration to create custom detectors that generate custom findings for resources that you specify.", "id": "GoogleCloudSecuritycenterV1CustomConfig", "properties": {"customOutput": {"$ref": "GoogleCloudSecuritycenterV1CustomOutputSpec", "description": "Custom output properties."}, "description": {"description": "Text that describes the vulnerability or misconfiguration that the custom module detects. This explanation is returned with each finding instance to help investigators understand the detected issue. The text must be enclosed in quotation marks.", "type": "string"}, "predicate": {"$ref": "Expr", "description": "The CEL expression to evaluate to produce findings. When the expression evaluates to true against a resource, a finding is generated."}, "recommendation": {"description": "An explanation of the recommended steps that security teams can take to resolve the detected issue. This explanation is returned with each finding generated by this module in the `nextSteps` property of the finding JSON.", "type": "string"}, "resourceSelector": {"$ref": "GoogleCloudSecuritycenterV1ResourceSelector", "description": "The resource types that the custom module operates on. Each custom module can specify up to 5 resource types."}, "severity": {"description": "The severity to assign to findings generated by the module.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["Unspecified severity.", "Critical severity.", "High severity.", "Medium severity.", "Low severity."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1CustomOutputSpec": {"description": "A set of optional name-value pairs that define custom source properties to return with each finding that is generated by the custom module. The custom source properties that are defined here are included in the finding JSON under `sourceProperties`.", "id": "GoogleCloudSecuritycenterV1CustomOutputSpec", "properties": {"properties": {"description": "A list of custom output properties to add to the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV1Property"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule": {"description": "An EffectiveSecurityHealthAnalyticsCustomModule is the representation of a Security Health Analytics custom module at a specified level of the resource hierarchy: organization, folder, or project. If a custom module is inherited from a parent organization or folder, the value of the `enablementState` property in EffectiveSecurityHealthAnalyticsCustomModule is set to the value that is effective in the parent, instead of `INHERITED`. For example, if the module is enabled in a parent organization or folder, the effective enablement_state for the module in all child folders or projects is also `enabled`. EffectiveSecurityHealthAnalyticsCustomModule is read-only.", "id": "GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule", "properties": {"cloudProvider": {"description": "The cloud provider of the custom module.", "enum": ["CLOUD_PROVIDER_UNSPECIFIED", "GOOGLE_CLOUD_PLATFORM", "AMAZON_WEB_SERVICES", "MICROSOFT_AZURE"], "enumDescriptions": ["Unspecified cloud provider.", "Google Cloud Platform.", "Amazon Web Services.", "Microsoft Azure."], "type": "string"}, "customConfig": {"$ref": "GoogleCloudSecuritycenterV1CustomConfig", "description": "Output only. The user-specified configuration for the module.", "readOnly": true}, "displayName": {"description": "Output only. The display name for the custom module. The name must be between 1 and 128 characters, start with a lowercase letter, and contain alphanumeric characters or underscores only.", "readOnly": true, "type": "string"}, "enablementState": {"description": "Output only. The effective state of enablement for the module at the given level of the hierarchy.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "ENABLED", "DISABLED"], "enumDescriptions": ["Unspecified enablement state.", "The module is enabled at the given level.", "The module is disabled at the given level."], "readOnly": true, "type": "string"}, "name": {"description": "Output only. The resource name of the custom module. Its format is \"organizations/{organization}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}\", or \"folders/{folder}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}\", or \"projects/{project}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}\"", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1ExternalSystem": {"description": "Representation of third party SIEM/SOAR fields within SCC.", "id": "GoogleCloudSecuritycenterV1ExternalSystem", "properties": {"assignees": {"description": "References primary/secondary etc assignees in the external system.", "items": {"type": "string"}, "type": "array"}, "caseCloseTime": {"description": "The time when the case was closed, as reported by the external system.", "format": "google-datetime", "type": "string"}, "caseCreateTime": {"description": "The time when the case was created, as reported by the external system.", "format": "google-datetime", "type": "string"}, "casePriority": {"description": "The priority of the finding's corresponding case in the external system.", "type": "string"}, "caseSla": {"description": "The SLA of the finding's corresponding case in the external system.", "format": "google-datetime", "type": "string"}, "caseUri": {"description": "The link to the finding's corresponding case in the external system.", "type": "string"}, "externalSystemUpdateTime": {"description": "The time when the case was last updated, as reported by the external system.", "format": "google-datetime", "type": "string"}, "externalUid": {"description": "The identifier that's used to track the finding's corresponding case in the external system.", "type": "string"}, "name": {"description": "Full resource name of the external system, for example: \"organizations/1234/sources/5678/findings/123456/externalSystems/jira\", \"folders/1234/sources/5678/findings/123456/externalSystems/jira\", \"projects/1234/sources/5678/findings/123456/externalSystems/jira\"", "type": "string"}, "status": {"description": "The most recent status of the finding's corresponding case, as reported by the external system.", "type": "string"}, "ticketInfo": {"$ref": "TicketInfo", "description": "Information about the ticket, if any, that is being used to track the resolution of the issue that is identified by this finding."}}, "type": "object"}, "GoogleCloudSecuritycenterV1MuteConfig": {"description": "A mute config is a Cloud SCC resource that contains the configuration to mute create/update events of findings.", "id": "GoogleCloudSecuritycenterV1MuteConfig", "properties": {"createTime": {"description": "Output only. The time at which the mute config was created. This field is set by the server and will be ignored if provided on config creation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A description of the mute config.", "type": "string"}, "displayName": {"deprecated": true, "description": "The human readable name to be displayed for the mute config.", "type": "string"}, "expiryTime": {"description": "Optional. The expiry of the mute config. Only applicable for dynamic configs. If the expiry is set, when the config expires, it is removed from all findings.", "format": "google-datetime", "type": "string"}, "filter": {"description": "Required. An expression that defines the filter to apply across create/update events of findings. While creating a filter string, be mindful of the scope in which the mute configuration is being created. E.g., If a filter contains project = X but is created under the project = Y scope, it might not match any findings. The following field and operator combinations are supported: * severity: `=`, `:` * category: `=`, `:` * resource.name: `=`, `:` * resource.project_name: `=`, `:` * resource.project_display_name: `=`, `:` * resource.folders.resource_folder: `=`, `:` * resource.parent_name: `=`, `:` * resource.parent_display_name: `=`, `:` * resource.type: `=`, `:` * finding_class: `=`, `:` * indicator.ip_addresses: `=`, `:` * indicator.domains: `=`, `:`", "type": "string"}, "mostRecentEditor": {"description": "Output only. Email address of the user who last edited the mute config. This field is set by the server and will be ignored if provided on config creation or update.", "readOnly": true, "type": "string"}, "name": {"description": "This field will be ignored if provided on config creation. Format `organizations/{organization}/muteConfigs/{mute_config}` `folders/{folder}/muteConfigs/{mute_config}` `projects/{project}/muteConfigs/{mute_config}` `organizations/{organization}/locations/global/muteConfigs/{mute_config}` `folders/{folder}/locations/global/muteConfigs/{mute_config}` `projects/{project}/locations/global/muteConfigs/{mute_config}`", "type": "string"}, "type": {"description": "Optional. The type of the mute config, which determines what type of mute state the config affects. The static mute state takes precedence over the dynamic mute state. Immutable after creation. STATIC by default if not set during creation.", "enum": ["MUTE_CONFIG_TYPE_UNSPECIFIED", "STATIC", "DYNAMIC"], "enumDescriptions": ["Unused.", "A static mute config, which sets the static mute state of future matching findings to muted. Once the static mute state has been set, finding or config modifications will not affect the state.", "A dynamic mute config, which is applied to existing and future matching findings, setting their dynamic mute state to \"muted\". If the config is updated or deleted, or a matching finding is updated, such that the finding doesn't match the config, the config will be removed from the finding, and the finding's dynamic mute state may become \"unmuted\" (unless other configs still match)."], "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the mute config was updated. This field is set by the server and will be ignored if provided on config creation or update.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1NotificationMessage": {"description": "Cloud SCC's Notification", "id": "GoogleCloudSecuritycenterV1NotificationMessage", "properties": {"finding": {"$ref": "Finding", "description": "If it's a Finding based notification config, this field will be populated."}, "notificationConfigName": {"description": "Name of the notification config that generated current notification.", "type": "string"}, "resource": {"$ref": "GoogleCloudSecuritycenterV1Resource", "description": "The Cloud resource tied to this notification's Finding."}}, "type": "object"}, "GoogleCloudSecuritycenterV1Property": {"description": "An individual name-value pair that defines a custom source property.", "id": "GoogleCloudSecuritycenterV1Property", "properties": {"name": {"description": "Name of the property for the custom output.", "type": "string"}, "valueExpression": {"$ref": "Expr", "description": "The CEL expression for the custom output. A resource property can be specified to return the value of the property or a text string enclosed in quotation marks."}}, "type": "object"}, "GoogleCloudSecuritycenterV1Resource": {"description": "Information related to the Google Cloud resource.", "id": "GoogleCloudSecuritycenterV1Resource", "properties": {"awsMetadata": {"$ref": "AwsMetadata", "description": "The AWS metadata associated with the finding."}, "azureMetadata": {"$ref": "AzureMetadata", "description": "The Azure metadata associated with the finding."}, "cloudProvider": {"description": "Indicates which cloud provider the resource resides in.", "enum": ["CLOUD_PROVIDER_UNSPECIFIED", "GOOGLE_CLOUD_PLATFORM", "AMAZON_WEB_SERVICES", "MICROSOFT_AZURE"], "enumDescriptions": ["The cloud provider is unspecified.", "The cloud provider is Google Cloud Platform.", "The cloud provider is Amazon Web Services.", "The cloud provider is Microsoft Azure."], "type": "string"}, "displayName": {"description": "The human readable name of the resource.", "type": "string"}, "folders": {"description": "Output only. Contains a Folder message for each folder in the assets ancestry. The first folder is the deepest nested folder, and the last folder is the folder directly under the Organization.", "items": {"$ref": "Folder"}, "readOnly": true, "type": "array"}, "location": {"description": "The region or location of the service (if applicable).", "type": "string"}, "name": {"description": "The full resource name of the resource. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "organization": {"description": "Indicates which organization or tenant in the cloud provider the finding applies to.", "type": "string"}, "parent": {"description": "The full resource name of resource's parent.", "type": "string"}, "parentDisplayName": {"description": "The human readable name of resource's parent.", "type": "string"}, "project": {"description": "The full resource name of project that the resource belongs to.", "type": "string"}, "projectDisplayName": {"description": "The project ID that the resource belongs to.", "type": "string"}, "resourcePath": {"$ref": "ResourcePath", "description": "Provides the path to the resource within the resource hierarchy."}, "resourcePathString": {"description": "A string representation of the resource path. For Google Cloud, it has the format of `organizations/{organization_id}/folders/{folder_id}/folders/{folder_id}/projects/{project_id}` where there can be any number of folders. For AWS, it has the format of `org/{organization_id}/ou/{organizational_unit_id}/ou/{organizational_unit_id}/account/{account_id}` where there can be any number of organizational units. For Azure, it has the format of `mg/{management_group_id}/mg/{management_group_id}/subscription/{subscription_id}/rg/{resource_group_name}` where there can be any number of management groups.", "type": "string"}, "service": {"description": "The parent service or product from which the resource is provided, for example, GKE or SNS.", "type": "string"}, "type": {"description": "The full resource type of the resource.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1ResourceSelector": {"description": "Resource for selecting resource type.", "id": "GoogleCloudSecuritycenterV1ResourceSelector", "properties": {"resourceTypes": {"description": "The resource types to run the detector on.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV1ResourceValueConfig": {"description": "A resource value configuration (RVC) is a mapping configuration of user's resources to resource values. Used in Attack path simulations.", "id": "GoogleCloudSecuritycenterV1ResourceValueConfig", "properties": {"cloudProvider": {"description": "Cloud provider this configuration applies to", "enum": ["CLOUD_PROVIDER_UNSPECIFIED", "GOOGLE_CLOUD_PLATFORM", "AMAZON_WEB_SERVICES", "MICROSOFT_AZURE"], "enumDescriptions": ["The cloud provider is unspecified.", "The cloud provider is Google Cloud Platform.", "The cloud provider is Amazon Web Services.", "The cloud provider is Microsoft Azure."], "type": "string"}, "createTime": {"description": "Output only. Timestamp this resource value configuration was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Description of the resource value configuration.", "type": "string"}, "name": {"description": "Name for the resource value configuration", "type": "string"}, "resourceLabelsSelector": {"additionalProperties": {"type": "string"}, "description": "List of resource labels to search for, evaluated with `AND`. For example, `\"resource_labels_selector\": {\"key\": \"value\", \"env\": \"prod\"}` will match resources with labels \"key\": \"value\" `AND` \"env\": \"prod\" https://cloud.google.com/resource-manager/docs/creating-managing-labels", "type": "object"}, "resourceType": {"description": "Apply resource_value only to resources that match resource_type. resource_type will be checked with `AND` of other resources. For example, \"storage.googleapis.com/Bucket\" with resource_value \"HIGH\" will apply \"HIGH\" value only to \"storage.googleapis.com/Bucket\" resources.", "type": "string"}, "resourceValue": {"description": "Required. Resource value level this expression represents", "enum": ["RESOURCE_VALUE_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "NONE"], "enumDescriptions": ["Unspecific value", "High resource value", "Medium resource value", "Low resource value", "No resource value, e.g. ignore these resources"], "type": "string"}, "scope": {"description": "Project or folder to scope this configuration to. For example, \"project/456\" would apply this configuration only to resources in \"project/456\" scope will be checked with `AND` of other resources.", "type": "string"}, "sensitiveDataProtectionMapping": {"$ref": "GoogleCloudSecuritycenterV1SensitiveDataProtectionMapping", "description": "A mapping of the sensitivity on Sensitive Data Protection finding to resource values. This mapping can only be used in combination with a resource_type that is related to BigQuery, e.g. \"bigquery.googleapis.com/Dataset\"."}, "tagValues": {"description": "Required. Tag values combined with `AND` to check against. For Google Cloud resources, they are tag value IDs in the form of \"tagValues/123\". Example: `[ \"tagValues/123\", \"tagValues/456\", \"tagValues/789\" ]` https://cloud.google.com/resource-manager/docs/tags/tags-creating-and-managing", "items": {"type": "string"}, "type": "array"}, "updateTime": {"description": "Output only. Timestamp this resource value configuration was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1RunAssetDiscoveryResponse": {"description": "Response of asset discovery run", "id": "GoogleCloudSecuritycenterV1RunAssetDiscoveryResponse", "properties": {"duration": {"description": "The duration between asset discovery run start and end", "format": "google-duration", "type": "string"}, "state": {"description": "The state of an asset discovery run.", "enum": ["STATE_UNSPECIFIED", "COMPLETED", "SUPERSEDED", "TERMINATED"], "enumDescriptions": ["Asset discovery run state was unspecified.", "Asset discovery run completed successfully.", "Asset discovery run was cancelled with tasks still pending, as another run for the same organization was started with a higher priority.", "Asset discovery run was killed and terminated."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule": {"description": "Represents an instance of a Security Health Analytics custom module, including its full module name, display name, enablement state, and last updated time. You can create a custom module at the organization, folder, or project level. Custom modules that you create at the organization or folder level are inherited by the child folders and projects.", "id": "GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule", "properties": {"ancestorModule": {"description": "Output only. If empty, indicates that the custom module was created in the organization, folder, or project in which you are viewing the custom module. Otherwise, `ancestor_module` specifies the organization or folder from which the custom module is inherited.", "readOnly": true, "type": "string"}, "cloudProvider": {"description": "The cloud provider of the custom module.", "enum": ["CLOUD_PROVIDER_UNSPECIFIED", "GOOGLE_CLOUD_PLATFORM", "AMAZON_WEB_SERVICES", "MICROSOFT_AZURE"], "enumDescriptions": ["Unspecified cloud provider.", "Google Cloud.", "Amazon Web Services (AWS).", "Microsoft Azure."], "type": "string"}, "customConfig": {"$ref": "GoogleCloudSecuritycenterV1CustomConfig", "description": "The user specified custom configuration for the module."}, "displayName": {"description": "The display name of the Security Health Analytics custom module. This display name becomes the finding category for all findings that are returned by this custom module. The display name must be between 1 and 128 characters, start with a lowercase letter, and contain alphanumeric characters or underscores only.", "type": "string"}, "enablementState": {"description": "The enablement state of the custom module.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "ENABLED", "DISABLED", "INHERITED"], "enumDescriptions": ["Unspecified enablement state.", "The module is enabled at the given CRM resource.", "The module is disabled at the given CRM resource.", "State is inherited from an ancestor module. The module will either be effectively ENABLED or DISABLED based on its closest non-inherited ancestor module in the CRM hierarchy."], "type": "string"}, "lastEditor": {"description": "Output only. The editor that last updated the custom module.", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. The resource name of the custom module. Its format is \"organizations/{organization}/securityHealthAnalyticsSettings/customModules/{customModule}\", or \"folders/{folder}/securityHealthAnalyticsSettings/customModules/{customModule}\", or \"projects/{project}/securityHealthAnalyticsSettings/customModules/{customModule}\" The id {customModule} is server-generated and is not user settable. It will be a numeric id containing 1-20 digits.", "type": "string"}, "updateTime": {"description": "Output only. The time at which the custom module was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1SensitiveDataProtectionMapping": {"description": "Resource value mapping for Sensitive Data Protection findings. If any of these mappings have a resource value that is not unspecified, the resource_value field will be ignored when reading this configuration.", "id": "GoogleCloudSecuritycenterV1SensitiveDataProtectionMapping", "properties": {"highSensitivityMapping": {"description": "Resource value mapping for high-sensitivity Sensitive Data Protection findings", "enum": ["RESOURCE_VALUE_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "NONE"], "enumDescriptions": ["Unspecific value", "High resource value", "Medium resource value", "Low resource value", "No resource value, e.g. ignore these resources"], "type": "string"}, "mediumSensitivityMapping": {"description": "Resource value mapping for medium-sensitivity Sensitive Data Protection findings", "enum": ["RESOURCE_VALUE_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "NONE"], "enumDescriptions": ["Unspecific value", "High resource value", "Medium resource value", "Low resource value", "No resource value, e.g. ignore these resources"], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1beta1Finding": {"description": "Security Command Center finding. A finding is a record of assessment data (security, risk, health or privacy) ingested into Security Command Center for presentation, notification, analysis, policy testing, and enforcement. For example, an XSS vulnerability in an App Engine application is a finding.", "id": "GoogleCloudSecuritycenterV1beta1Finding", "properties": {"category": {"description": "The additional taxonomy group within findings from a given source. This field is immutable after creation time. Example: \"XSS_FLASH_INJECTION\"", "type": "string"}, "createTime": {"description": "The time at which the finding was created in Security Command Center.", "format": "google-datetime", "type": "string"}, "eventTime": {"description": "The time at which the event took place, or when an update to the finding occurred. For example, if the finding represents an open firewall it would capture the time the detector believes the firewall became open. The accuracy is determined by the detector. If the finding were to be resolved afterward, this time would reflect when the finding was resolved.", "format": "google-datetime", "type": "string"}, "externalUri": {"description": "The URI that, if available, points to a web page outside of Security Command Center where additional information about the finding can be found. This field is guaranteed to be either empty or a well formed URL.", "type": "string"}, "name": {"description": "The relative resource name of this finding. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\"", "type": "string"}, "parent": {"description": "Immutable. The relative resource name of the source the finding belongs to. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name This field is immutable after creation time. For example: \"organizations/{organization_id}/sources/{source_id}\"", "type": "string"}, "resourceName": {"description": "For findings on Google Cloud resources, the full resource name of the Google Cloud resource this finding is for. See: https://cloud.google.com/apis/design/resource_names#full_resource_name When the finding is for a non-Google Cloud resource, the resourceName can be a customer or partner defined string. This field is immutable after creation time.", "type": "string"}, "securityMarks": {"$ref": "GoogleCloudSecuritycenterV1beta1SecurityMarks", "description": "Output only. User specified security marks. These marks are entirely managed by the user and come from the SecurityMarks resource that belongs to the finding.", "readOnly": true}, "sourceProperties": {"additionalProperties": {"type": "any"}, "description": "Source specific properties. These properties are managed by the source that writes the finding. The key names in the source_properties map must be between 1 and 255 characters, and must start with a letter and contain alphanumeric characters or underscores only.", "type": "object"}, "state": {"description": "The state of the finding.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "The finding requires attention and has not been addressed yet.", "The finding has been fixed, triaged as a non-issue or otherwise addressed and is no longer active."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1beta1RunAssetDiscoveryResponse": {"description": "Response of asset discovery run", "id": "GoogleCloudSecuritycenterV1beta1RunAssetDiscoveryResponse", "properties": {"duration": {"description": "The duration between asset discovery run start and end", "format": "google-duration", "type": "string"}, "state": {"description": "The state of an asset discovery run.", "enum": ["STATE_UNSPECIFIED", "COMPLETED", "SUPERSEDED", "TERMINATED"], "enumDescriptions": ["Asset discovery run state was unspecified.", "Asset discovery run completed successfully.", "Asset discovery run was cancelled with tasks still pending, as another run for the same organization was started with a higher priority.", "Asset discovery run was killed and terminated."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1beta1SecurityMarks": {"description": "User specified security marks that are attached to the parent Security Command Center resource. Security marks are scoped within a Security Command Center organization -- they can be modified and viewed by all users who have proper permissions on the organization.", "id": "GoogleCloudSecuritycenterV1beta1SecurityMarks", "properties": {"marks": {"additionalProperties": {"type": "string"}, "description": "Mutable user specified security marks belonging to the parent resource. Constraints are as follows: * Keys and values are treated as case insensitive * Keys must be between 1 - 256 characters (inclusive) * Keys must be letters, numbers, underscores, or dashes * Values have leading and trailing whitespace trimmed, remaining characters must be between 1 - 4096 characters (inclusive)", "type": "object"}, "name": {"description": "The relative resource name of the SecurityMarks. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\".", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1Finding": {"description": "Security Command Center finding. A finding is a record of assessment data (security, risk, health or privacy) ingested into Security Command Center for presentation, notification, analysis, policy testing, and enforcement. For example, an XSS vulnerability in an App Engine application is a finding.", "id": "GoogleCloudSecuritycenterV1p1beta1Finding", "properties": {"canonicalName": {"description": "The canonical name of the finding. It's either \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\", \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}\" or \"projects/{project_number}/sources/{source_id}/findings/{finding_id}\", depending on the closest CRM ancestor of the resource associated with the finding.", "type": "string"}, "category": {"description": "The additional taxonomy group within findings from a given source. This field is immutable after creation time. Example: \"XSS_FLASH_INJECTION\"", "type": "string"}, "createTime": {"description": "The time at which the finding was created in Security Command Center.", "format": "google-datetime", "type": "string"}, "eventTime": {"description": "The time at which the event took place, or when an update to the finding occurred. For example, if the finding represents an open firewall it would capture the time the detector believes the firewall became open. The accuracy is determined by the detector. If the finding were to be resolved afterward, this time would reflect when the finding was resolved. Must not be set to a value greater than the current timestamp.", "format": "google-datetime", "type": "string"}, "externalUri": {"description": "The URI that, if available, points to a web page outside of Security Command Center where additional information about the finding can be found. This field is guaranteed to be either empty or a well formed URL.", "type": "string"}, "name": {"description": "The relative resource name of this finding. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\"", "type": "string"}, "parent": {"description": "The relative resource name of the source the finding belongs to. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name This field is immutable after creation time. For example: \"organizations/{organization_id}/sources/{source_id}\"", "type": "string"}, "resourceName": {"description": "For findings on Google Cloud resources, the full resource name of the Google Cloud resource this finding is for. See: https://cloud.google.com/apis/design/resource_names#full_resource_name When the finding is for a non-Google Cloud resource, the resourceName can be a customer or partner defined string. This field is immutable after creation time.", "type": "string"}, "securityMarks": {"$ref": "GoogleCloudSecuritycenterV1p1beta1SecurityMarks", "description": "Output only. User specified security marks. These marks are entirely managed by the user and come from the SecurityMarks resource that belongs to the finding.", "readOnly": true}, "severity": {"description": "The severity of the finding. This field is managed by the source that writes the finding.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["No severity specified. The default value.", "Critical severity.", "High severity.", "Medium severity.", "Low severity."], "type": "string"}, "sourceProperties": {"additionalProperties": {"type": "any"}, "description": "Source specific properties. These properties are managed by the source that writes the finding. The key names in the source_properties map must be between 1 and 255 characters, and must start with a letter and contain alphanumeric characters or underscores only.", "type": "object"}, "state": {"description": "The state of the finding.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "The finding requires attention and has not been addressed yet.", "The finding has been fixed, triaged as a non-issue or otherwise addressed and is no longer active."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1Folder": {"description": "Message that contains the resource name and display name of a folder resource.", "id": "GoogleCloudSecuritycenterV1p1beta1Folder", "properties": {"resourceFolder": {"description": "Full resource name of this folder. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourceFolderDisplayName": {"description": "The user defined display name for this folder.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1NotificationMessage": {"description": "Security Command Center's Notification", "id": "GoogleCloudSecuritycenterV1p1beta1NotificationMessage", "properties": {"finding": {"$ref": "GoogleCloudSecuritycenterV1p1beta1Finding", "description": "If it's a Finding based notification config, this field will be populated."}, "notificationConfigName": {"description": "Name of the notification config that generated current notification.", "type": "string"}, "resource": {"$ref": "GoogleCloudSecuritycenterV1p1beta1Resource", "description": "The Cloud resource tied to the notification."}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1Resource": {"description": "Information related to the Google Cloud resource.", "id": "GoogleCloudSecuritycenterV1p1beta1Resource", "properties": {"folders": {"description": "Output only. Contains a Folder message for each folder in the assets ancestry. The first folder is the deepest nested folder, and the last folder is the folder directly under the Organization.", "items": {"$ref": "GoogleCloudSecuritycenterV1p1beta1Folder"}, "readOnly": true, "type": "array"}, "name": {"description": "The full resource name of the resource. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "parent": {"description": "The full resource name of resource's parent.", "type": "string"}, "parentDisplayName": {"description": "The human readable name of resource's parent.", "type": "string"}, "project": {"description": "The full resource name of project that the resource belongs to.", "type": "string"}, "projectDisplayName": {"description": "The project id that the resource belongs to.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1RunAssetDiscoveryResponse": {"description": "Response of asset discovery run", "id": "GoogleCloudSecuritycenterV1p1beta1RunAssetDiscoveryResponse", "properties": {"duration": {"description": "The duration between asset discovery run start and end", "format": "google-duration", "type": "string"}, "state": {"description": "The state of an asset discovery run.", "enum": ["STATE_UNSPECIFIED", "COMPLETED", "SUPERSEDED", "TERMINATED"], "enumDescriptions": ["Asset discovery run state was unspecified.", "Asset discovery run completed successfully.", "Asset discovery run was cancelled with tasks still pending, as another run for the same organization was started with a higher priority.", "Asset discovery run was killed and terminated."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1SecurityMarks": {"description": "User specified security marks that are attached to the parent Security Command Center resource. Security marks are scoped within a Security Command Center organization -- they can be modified and viewed by all users who have proper permissions on the organization.", "id": "GoogleCloudSecuritycenterV1p1beta1SecurityMarks", "properties": {"canonicalName": {"description": "The canonical name of the marks. Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"folders/{folder_id}/assets/{asset_id}/securityMarks\" \"projects/{project_number}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\" \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}/securityMarks\" \"projects/{project_number}/sources/{source_id}/findings/{finding_id}/securityMarks\"", "type": "string"}, "marks": {"additionalProperties": {"type": "string"}, "description": "Mutable user specified security marks belonging to the parent resource. Constraints are as follows: * Keys and values are treated as case insensitive * Keys must be between 1 - 256 characters (inclusive) * Keys must be letters, numbers, underscores, or dashes * Values have leading and trailing whitespace trimmed, remaining characters must be between 1 - 4096 characters (inclusive)", "type": "object"}, "name": {"description": "The relative resource name of the SecurityMarks. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\".", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Access": {"description": "Represents an access event.", "id": "GoogleCloudSecuritycenterV2Access", "properties": {"callerIp": {"description": "Caller's IP address, such as \"*******\".", "type": "string"}, "callerIpGeo": {"$ref": "GoogleCloudSecuritycenterV2Geolocation", "description": "The caller IP's geolocation, which identifies where the call came from."}, "methodName": {"description": "The method that the service account called, e.g. \"SetIamPolicy\".", "type": "string"}, "principalEmail": {"description": "Associated email, such as \"<EMAIL>\". The email address of the authenticated user or a service account acting on behalf of a third party principal making the request. For third party identity callers, the `principal_subject` field is populated instead of this field. For privacy reasons, the principal email address is sometimes redacted. For more information, see [Caller identities in audit logs](https://cloud.google.com/logging/docs/audit#user-id).", "type": "string"}, "principalSubject": {"description": "A string that represents the principal_subject that is associated with the identity. Unlike `principal_email`, `principal_subject` supports principals that aren't associated with email addresses, such as third party principals. For most identities, the format is `principal://iam.googleapis.com/{identity pool name}/subject/{subject}`. Some GKE identities, such as GKE_WORKLOAD, FREEFORM, and GKE_HUB_WORKLOAD, still use the legacy format `serviceAccount:{identity pool name}[{subject}]`.", "type": "string"}, "serviceAccountDelegationInfo": {"description": "The identity delegation history of an authenticated service account that made the request. The `serviceAccountDelegationInfo[]` object contains information about the real authorities that try to access Google Cloud resources by delegating on a service account. When multiple authorities are present, they are guaranteed to be sorted based on the original ordering of the identity delegation events.", "items": {"$ref": "GoogleCloudSecuritycenterV2ServiceAccountDelegationInfo"}, "type": "array"}, "serviceAccountKeyName": {"description": "The name of the service account key that was used to create or exchange credentials when authenticating the service account that made the request. This is a scheme-less URI full resource name. For example: \"//iam.googleapis.com/projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}\". ", "type": "string"}, "serviceName": {"description": "This is the API service that the service account made a call to, e.g. \"iam.googleapis.com\"", "type": "string"}, "userAgent": {"description": "The caller's user agent string associated with the finding.", "type": "string"}, "userAgentFamily": {"description": "Type of user agent associated with the finding. For example, an operating system shell or an embedded or standalone application.", "type": "string"}, "userName": {"description": "A string that represents a username. The username provided depends on the type of the finding and is likely not an IAM principal. For example, this can be a system username if the finding is related to a virtual machine, or it can be an application login username.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AccessReview": {"description": "Conveys information about a Kubernetes access review (such as one returned by a [`kubectl auth can-i`](https://kubernetes.io/docs/reference/access-authn-authz/authorization/#checking-api-access) command) that was involved in a finding.", "id": "GoogleCloudSecuritycenterV2AccessReview", "properties": {"group": {"description": "The API group of the resource. \"*\" means all.", "type": "string"}, "name": {"description": "The name of the resource being requested. Empty means all.", "type": "string"}, "ns": {"description": "Namespace of the action being requested. Currently, there is no distinction between no namespace and all namespaces. Both are represented by \"\" (empty).", "type": "string"}, "resource": {"description": "The optional resource type requested. \"*\" means all.", "type": "string"}, "subresource": {"description": "The optional subresource type.", "type": "string"}, "verb": {"description": "A Kubernetes resource API verb, like get, list, watch, create, update, delete, proxy. \"*\" means all.", "type": "string"}, "version": {"description": "The API version of the resource. \"*\" means all.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AdaptiveProtection": {"description": "Information about [Google Cloud Armor Adaptive Protection](https://cloud.google.com/armor/docs/cloud-armor-overview#google-cloud-armor-adaptive-protection).", "id": "GoogleCloudSecuritycenterV2AdaptiveProtection", "properties": {"confidence": {"description": "A score of 0 means that there is low confidence that the detected event is an actual attack. A score of 1 means that there is high confidence that the detected event is an attack. See the [Adaptive Protection documentation](https://cloud.google.com/armor/docs/adaptive-protection-overview#configure-alert-tuning) for further explanation.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AffectedResources": {"description": "Details about resources affected by this finding.", "id": "GoogleCloudSecuritycenterV2AffectedResources", "properties": {"count": {"description": "The count of resources affected by the finding.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Allowed": {"description": "Allowed IP rule.", "id": "GoogleCloudSecuritycenterV2Allowed", "properties": {"ipRules": {"description": "Optional. Optional list of allowed IP rules.", "items": {"$ref": "GoogleCloudSecuritycenterV2IpRule"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Application": {"description": "Represents an application associated with a finding.", "id": "GoogleCloudSecuritycenterV2Application", "properties": {"baseUri": {"description": "The base URI that identifies the network location of the application in which the vulnerability was detected. For example, `http://example.com`.", "type": "string"}, "fullUri": {"description": "The full URI with payload that could be used to reproduce the vulnerability. For example, `http://example.com?p=aMmYgI6H`.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Attack": {"description": "Information about DDoS attack volume and classification.", "id": "GoogleCloudSecuritycenterV2Attack", "properties": {"classification": {"description": "Type of attack, for example, 'SYN-flood', 'NTP-udp', or 'CHARGEN-udp'.", "type": "string"}, "volumeBps": {"deprecated": true, "description": "Total BPS (bytes per second) volume of attack. Deprecated - refer to volume_bps_long instead.", "format": "int32", "type": "integer"}, "volumeBpsLong": {"description": "Total BPS (bytes per second) volume of attack.", "format": "int64", "type": "string"}, "volumePps": {"deprecated": true, "description": "Total PPS (packets per second) volume of attack. Deprecated - refer to volume_pps_long instead.", "format": "int32", "type": "integer"}, "volumePpsLong": {"description": "Total PPS (packets per second) volume of attack.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AttackExposure": {"description": "An attack exposure contains the results of an attack path simulation run.", "id": "GoogleCloudSecuritycenterV2AttackExposure", "properties": {"attackExposureResult": {"description": "The resource name of the attack path simulation result that contains the details regarding this attack exposure score. Example: `organizations/123/simulations/456/attackExposureResults/789`", "type": "string"}, "exposedHighValueResourcesCount": {"description": "The number of high value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "exposedLowValueResourcesCount": {"description": "The number of high value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "exposedMediumValueResourcesCount": {"description": "The number of medium value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "latestCalculationTime": {"description": "The most recent time the attack exposure was updated on this finding.", "format": "google-datetime", "type": "string"}, "score": {"description": "A number between 0 (inclusive) and infinity that represents how important this finding is to remediate. The higher the score, the more important it is to remediate.", "format": "double", "type": "number"}, "state": {"description": "Output only. What state this AttackExposure is in. This captures whether or not an attack exposure has been calculated or not.", "enum": ["STATE_UNSPECIFIED", "CALCULATED", "NOT_CALCULATED"], "enumDescriptions": ["The state is not specified.", "The attack exposure has been calculated.", "The attack exposure has not been calculated."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AwsAccount": {"description": "An AWS account that is a member of an organization.", "id": "GoogleCloudSecuritycenterV2AwsAccount", "properties": {"id": {"description": "The unique identifier (ID) of the account, containing exactly 12 digits.", "type": "string"}, "name": {"description": "The friendly name of this account.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AwsMetadata": {"description": "AWS metadata associated with the resource, only applicable if the finding's cloud provider is Amazon Web Services.", "id": "GoogleCloudSecuritycenterV2AwsMetadata", "properties": {"account": {"$ref": "GoogleCloudSecuritycenterV2AwsAccount", "description": "The AWS account associated with the resource."}, "organization": {"$ref": "GoogleCloudSecuritycenterV2AwsOrganization", "description": "The AWS organization associated with the resource."}, "organizationalUnits": {"description": "A list of AWS organizational units associated with the resource, ordered from lowest level (closest to the account) to highest level.", "items": {"$ref": "GoogleCloudSecuritycenterV2AwsOrganizationalUnit"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AwsOrganization": {"description": "An organization is a collection of accounts that are centrally managed together using consolidated billing, organized hierarchically with organizational units (OUs), and controlled with policies.", "id": "GoogleCloudSecuritycenterV2AwsOrganization", "properties": {"id": {"description": "The unique identifier (ID) for the organization. The regex pattern for an organization ID string requires \"o-\" followed by from 10 to 32 lowercase letters or digits.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AwsOrganizationalUnit": {"description": "An Organizational Unit (OU) is a container of AWS accounts within a root of an organization. Policies that are attached to an OU apply to all accounts contained in that OU and in any child OUs.", "id": "GoogleCloudSecuritycenterV2AwsOrganizationalUnit", "properties": {"id": {"description": "The unique identifier (ID) associated with this OU. The regex pattern for an organizational unit ID string requires \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that contains the OU). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits. For example, \"ou-ab12-cd34ef56\".", "type": "string"}, "name": {"description": "The friendly name of the OU.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AzureManagementGroup": {"description": "Represents an Azure management group.", "id": "GoogleCloudSecuritycenterV2AzureManagementGroup", "properties": {"displayName": {"description": "The display name of the Azure management group.", "type": "string"}, "id": {"description": "The UUID of the Azure management group, for example, `*************-0000-0000-************`.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AzureMetadata": {"description": "Azure metadata associated with the resource, only applicable if the finding's cloud provider is Microsoft Azure.", "id": "GoogleCloudSecuritycenterV2AzureMetadata", "properties": {"managementGroups": {"description": "A list of Azure management groups associated with the resource, ordered from lowest level (closest to the subscription) to highest level.", "items": {"$ref": "GoogleCloudSecuritycenterV2AzureManagementGroup"}, "type": "array"}, "resourceGroup": {"$ref": "GoogleCloudSecuritycenterV2AzureResourceGroup", "description": "The Azure resource group associated with the resource."}, "subscription": {"$ref": "GoogleCloudSecuritycenterV2AzureSubscription", "description": "The Azure subscription associated with the resource."}, "tenant": {"$ref": "GoogleCloudSecuritycenterV2AzureTenant", "description": "The Azure Entra tenant associated with the resource."}}, "type": "object"}, "GoogleCloudSecuritycenterV2AzureResourceGroup": {"description": "Represents an Azure resource group.", "id": "GoogleCloudSecuritycenterV2AzureResourceGroup", "properties": {"id": {"description": "The ID of the Azure resource group.", "type": "string"}, "name": {"description": "The name of the Azure resource group. This is not a UUID.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AzureSubscription": {"description": "Represents an Azure subscription.", "id": "GoogleCloudSecuritycenterV2AzureSubscription", "properties": {"displayName": {"description": "The display name of the Azure subscription.", "type": "string"}, "id": {"description": "The UUID of the Azure subscription, for example, `291bba3f-e0a5-47bc-a099-3bdcb2a50a05`.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2AzureTenant": {"description": "Represents a Microsoft Entra tenant.", "id": "GoogleCloudSecuritycenterV2AzureTenant", "properties": {"displayName": {"description": "The display name of the Azure tenant.", "type": "string"}, "id": {"description": "The ID of the Microsoft Entra tenant, for example, \"a11aaa11-aa11-1aa1-11aa-1aaa11a\".", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2BackupDisasterRecovery": {"description": "Information related to Google Cloud Backup and DR Service findings.", "id": "GoogleCloudSecuritycenterV2BackupDisasterRecovery", "properties": {"appliance": {"description": "The name of the Backup and DR appliance that captures, moves, and manages the lifecycle of backup data. For example, `backup-server-57137`.", "type": "string"}, "applications": {"description": "The names of Backup and DR applications. An application is a VM, database, or file system on a managed host monitored by a backup and recovery appliance. For example, `centos7-01-vol00`, `centos7-01-vol01`, `centos7-01-vol02`.", "items": {"type": "string"}, "type": "array"}, "backupCreateTime": {"description": "The timestamp at which the Backup and DR backup was created.", "format": "google-datetime", "type": "string"}, "backupTemplate": {"description": "The name of a Backup and DR template which comprises one or more backup policies. See the [Backup and DR documentation](https://cloud.google.com/backup-disaster-recovery/docs/concepts/backup-plan#temp) for more information. For example, `snap-ov`.", "type": "string"}, "backupType": {"description": "The backup type of the Backup and DR image. For example, `Snapshot`, `Remote Snapshot`, `OnVault`.", "type": "string"}, "host": {"description": "The name of a Backup and DR host, which is managed by the backup and recovery appliance and known to the management console. The host can be of type Generic (for example, Compute Engine, SQL Server, Oracle DB, SMB file system, etc.), vCenter, or an ESX server. See the [Backup and DR documentation on hosts](https://cloud.google.com/backup-disaster-recovery/docs/configuration/manage-hosts-and-their-applications) for more information. For example, `centos7-01`.", "type": "string"}, "policies": {"description": "The names of Backup and DR policies that are associated with a template and that define when to run a backup, how frequently to run a backup, and how long to retain the backup image. For example, `onvaults`.", "items": {"type": "string"}, "type": "array"}, "policyOptions": {"description": "The names of Backup and DR advanced policy options of a policy applying to an application. See the [Backup and DR documentation on policy options](https://cloud.google.com/backup-disaster-recovery/docs/create-plan/policy-settings). For example, `skipofflineappsincongrp, nounmap`.", "items": {"type": "string"}, "type": "array"}, "profile": {"description": "The name of the Backup and DR resource profile that specifies the storage media for backups of application and VM data. See the [Backup and DR documentation on profiles](https://cloud.google.com/backup-disaster-recovery/docs/concepts/backup-plan#profile). For example, `GCP`.", "type": "string"}, "storagePool": {"description": "The name of the Backup and DR storage pool that the backup and recovery appliance is storing data in. The storage pool could be of type Cloud, Primary, Snapshot, or OnVault. See the [Backup and DR documentation on storage pools](https://cloud.google.com/backup-disaster-recovery/docs/concepts/storage-pools). For example, `DiskPoolOne`.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2BigQueryExport": {"description": "Configures how to deliver Findings to BigQuery Instance.", "id": "GoogleCloudSecuritycenterV2BigQueryExport", "properties": {"createTime": {"description": "Output only. The time at which the BigQuery export was created. This field is set by the server and will be ignored if provided on export on creation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataset": {"description": "The dataset to write findings' updates to. Its format is \"projects/[project_id]/datasets/[bigquery_dataset_id]\". BigQuery dataset unique ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_).", "type": "string"}, "description": {"description": "The description of the export (max of 1024 characters).", "type": "string"}, "filter": {"description": "Expression that defines the filter to apply across create/update events of findings. The expression is a list of zero or more restrictions combined via logical operators `AND` and `OR`. Parentheses are supported, and `OR` has higher precedence than `AND`. Restrictions have the form ` ` and may have a `-` character in front of them to indicate negation. The fields map to those defined in the corresponding resource. The supported operators are: * `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching, for strings. The supported value types are: * string literals in quotes. * integer literals without quotes. * boolean literals `true` and `false` without quotes.", "type": "string"}, "mostRecentEditor": {"description": "Output only. Email address of the user who last edited the BigQuery export. This field is set by the server and will be ignored if provided on export creation or update.", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The relative resource name of this export. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name. The following list shows some examples: + `organizations/{organization_id}/locations/{location_id}/bigQueryExports/{export_id}` + `folders/{folder_id}/locations/{location_id}/bigQueryExports/{export_id}` + `projects/{project_id}/locations/{location_id}/bigQueryExports/{export_id}` This field is provided in responses, and is ignored when provided in create requests.", "type": "string"}, "principal": {"description": "Output only. The service account that needs permission to create table and upload data to the BigQuery dataset.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the BigQuery export was updated. This field is set by the server and will be ignored if provided on export creation or update.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Binding": {"description": "Represents a Kubernetes RoleBinding or ClusterRoleBinding.", "id": "GoogleCloudSecuritycenterV2Binding", "properties": {"name": {"description": "Name for the binding.", "type": "string"}, "ns": {"description": "Namespace for the binding.", "type": "string"}, "role": {"$ref": "GoogleCloudSecuritycenterV2Role", "description": "The Role or ClusterRole referenced by the binding."}, "subjects": {"description": "Represents one or more subjects that are bound to the role. Not always available for PATCH requests.", "items": {"$ref": "GoogleCloudSecuritycenterV2Subject"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2BulkMuteFindingsResponse": {"description": "The response to a BulkMute request. Contains the LRO information.", "id": "GoogleCloudSecuritycenterV2BulkMuteFindingsResponse", "properties": {}, "type": "object"}, "GoogleCloudSecuritycenterV2Chokepoint": {"description": "Contains details about a chokepoint, which is a resource or resource group where high-risk attack paths converge, based on [attack path simulations] (https://cloud.google.com/security-command-center/docs/attack-exposure-learn#attack_path_simulations).", "id": "GoogleCloudSecuritycenterV2Chokepoint", "properties": {"relatedFindings": {"description": "List of resource names of findings associated with this chokepoint. For example, organizations/123/sources/456/findings/789. This list will have at most 100 findings.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2CloudArmor": {"description": "Fields related to Google Cloud Armor findings.", "id": "GoogleCloudSecuritycenterV2CloudArmor", "properties": {"adaptiveProtection": {"$ref": "GoogleCloudSecuritycenterV2AdaptiveProtection", "description": "Information about potential Layer 7 DDoS attacks identified by [Google Cloud Armor Adaptive Protection](https://cloud.google.com/armor/docs/adaptive-protection-overview)."}, "attack": {"$ref": "GoogleCloudSecuritycenterV2Attack", "description": "Information about DDoS attack volume and classification."}, "duration": {"description": "Duration of attack from the start until the current moment (updated every 5 minutes).", "format": "google-duration", "type": "string"}, "requests": {"$ref": "GoogleCloudSecuritycenterV2Requests", "description": "Information about incoming requests evaluated by [Google Cloud Armor security policies](https://cloud.google.com/armor/docs/security-policy-overview)."}, "securityPolicy": {"$ref": "GoogleCloudSecuritycenterV2SecurityPolicy", "description": "Information about the [Google Cloud Armor security policy](https://cloud.google.com/armor/docs/security-policy-overview) relevant to the finding."}, "threatVector": {"description": "Distinguish between volumetric & protocol DDoS attack and application layer attacks. For example, \"L3_4\" for Layer 3 and Layer 4 DDoS attacks, or \"L_7\" for Layer 7 DDoS attacks.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2CloudDlpDataProfile": {"description": "The [data profile](https://cloud.google.com/dlp/docs/data-profiles) associated with the finding.", "id": "GoogleCloudSecuritycenterV2CloudDlpDataProfile", "properties": {"dataProfile": {"description": "Name of the data profile, for example, `projects/123/locations/europe/tableProfiles/8383929`.", "type": "string"}, "parentType": {"description": "The resource hierarchy level at which the data profile was generated.", "enum": ["PARENT_TYPE_UNSPECIFIED", "ORGANIZATION", "PROJECT"], "enumDescriptions": ["Unspecified parent type.", "Organization-level configurations.", "Project-level configurations."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2CloudDlpInspection": {"description": "Details about the Cloud Data Loss Prevention (Cloud DLP) [inspection job](https://cloud.google.com/dlp/docs/concepts-job-triggers) that produced the finding.", "id": "GoogleCloudSecuritycenterV2CloudDlpInspection", "properties": {"fullScan": {"description": "Whether Cloud DLP scanned the complete resource or a sampled subset.", "type": "boolean"}, "infoType": {"description": "The type of information (or *[infoType](https://cloud.google.com/dlp/docs/infotypes-reference)*) found, for example, `EMAIL_ADDRESS` or `STREET_ADDRESS`.", "type": "string"}, "infoTypeCount": {"description": "The number of times Cloud DLP found this infoType within this job and resource.", "format": "int64", "type": "string"}, "inspectJob": {"description": "Name of the inspection job, for example, `projects/123/locations/europe/dlpJobs/i-8383929`.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2CloudLoggingEntry": {"description": "Metadata taken from a [Cloud Logging LogEntry](https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry)", "id": "GoogleCloudSecuritycenterV2CloudLoggingEntry", "properties": {"insertId": {"description": "A unique identifier for the log entry.", "type": "string"}, "logId": {"description": "The type of the log (part of `log_name`. `log_name` is the resource name of the log to which this log entry belongs). For example: `cloudresourcemanager.googleapis.com/activity` Note that this field is not URL-encoded, unlike in `LogEntry`.", "type": "string"}, "resourceContainer": {"description": "The organization, folder, or project of the monitored resource that produced this log entry.", "type": "string"}, "timestamp": {"description": "The time the event described by the log entry occurred.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Compliance": {"description": "Contains compliance information about a security standard indicating unmet recommendations.", "id": "GoogleCloudSecuritycenterV2Compliance", "properties": {"ids": {"description": "Policies within the standard or benchmark, for example, A.12.4.1", "items": {"type": "string"}, "type": "array"}, "standard": {"description": "Industry-wide compliance standards or benchmarks, such as CIS, PCI, and OWASP.", "type": "string"}, "version": {"description": "Version of the standard or benchmark, for example, 1.1", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Connection": {"description": "Contains information about the IP connection associated with the finding.", "id": "GoogleCloudSecuritycenterV2Connection", "properties": {"destinationIp": {"description": "Destination IP address. Not present for sockets that are listening and not connected.", "type": "string"}, "destinationPort": {"description": "Destination port. Not present for sockets that are listening and not connected.", "format": "int32", "type": "integer"}, "protocol": {"description": "IANA Internet Protocol Number such as TCP(6) and UDP(17).", "enum": ["PROTOCOL_UNSPECIFIED", "ICMP", "TCP", "UDP", "GRE", "ESP"], "enumDescriptions": ["Unspecified protocol (not HOPOPT).", "Internet Control Message Protocol.", "Transmission Control Protocol.", "User Datagram Protocol.", "Generic Routing Encapsulation.", "Encap Security Payload."], "type": "string"}, "sourceIp": {"description": "Source IP address.", "type": "string"}, "sourcePort": {"description": "Source port.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Contact": {"description": "The email address of a contact.", "id": "GoogleCloudSecuritycenterV2Contact", "properties": {"email": {"description": "An email address. For example, \"`<EMAIL>`\".", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2ContactDetails": {"description": "Details about specific contacts", "id": "GoogleCloudSecuritycenterV2ContactDetails", "properties": {"contacts": {"description": "A list of contacts", "items": {"$ref": "GoogleCloudSecuritycenterV2Contact"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Container": {"description": "Container associated with the finding.", "id": "GoogleCloudSecuritycenterV2Container", "properties": {"createTime": {"description": "The time that the container was created.", "format": "google-datetime", "type": "string"}, "imageId": {"description": "Optional container image ID, if provided by the container runtime. Uniquely identifies the container image launched using a container image digest.", "type": "string"}, "labels": {"description": "Container labels, as provided by the container runtime.", "items": {"$ref": "GoogleCloudSecuritycenterV2Label"}, "type": "array"}, "name": {"description": "Name of the container.", "type": "string"}, "uri": {"description": "Container image URI provided when configuring a pod or container. This string can identify a container image version using mutable tags.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Cve": {"description": "CVE stands for Common Vulnerabilities and Exposures. Information from the [CVE record](https://www.cve.org/ResourcesSupport/Glossary) that describes this vulnerability.", "id": "GoogleCloudSecuritycenterV2Cve", "properties": {"cvssv3": {"$ref": "GoogleCloudSecuritycenterV2Cvssv3", "description": "Describe Common Vulnerability Scoring System specified at https://www.first.org/cvss/v3.1/specification-document"}, "exploitReleaseDate": {"description": "Date the first publicly available exploit or PoC was released.", "format": "google-datetime", "type": "string"}, "exploitationActivity": {"description": "The exploitation activity of the vulnerability in the wild.", "enum": ["EXPLOITATION_ACTIVITY_UNSPECIFIED", "WIDE", "CONFIRMED", "AVAILABLE", "ANTICIPATED", "NO_KNOWN"], "enumDescriptions": ["Invalid or empty value.", "Exploitation has been reported or confirmed to widely occur.", "Limited reported or confirmed exploitation activities.", "Exploit is publicly available.", "No known exploitation activity, but has a high potential for exploitation.", "No known exploitation activity."], "type": "string"}, "firstExploitationDate": {"description": "Date of the earliest known exploitation.", "format": "google-datetime", "type": "string"}, "id": {"description": "The unique identifier for the vulnerability. e.g. CVE-2021-34527", "type": "string"}, "impact": {"description": "The potential impact of the vulnerability if it was to be exploited.", "enum": ["RISK_RATING_UNSPECIFIED", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "enumDescriptions": ["Invalid or empty value.", "Exploitation would have little to no security impact.", "Exploitation would enable attackers to perform activities, or could allow attackers to have a direct impact, but would require additional steps.", "Exploitation would enable attackers to have a notable direct impact without needing to overcome any major mitigating factors.", "Exploitation would fundamentally undermine the security of affected systems, enable actors to perform significant attacks with minimal effort, with little to no mitigating factors to overcome."], "type": "string"}, "observedInTheWild": {"description": "Whether or not the vulnerability has been observed in the wild.", "type": "boolean"}, "references": {"description": "Additional information about the CVE. e.g. https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527", "items": {"$ref": "GoogleCloudSecuritycenterV2Reference"}, "type": "array"}, "upstreamFixAvailable": {"description": "Whether upstream fix is available for the CVE.", "type": "boolean"}, "zeroDay": {"description": "Whether or not the vulnerability was zero day when the finding was published.", "type": "boolean"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Cvssv3": {"description": "Common Vulnerability Scoring System version 3.", "id": "GoogleCloudSecuritycenterV2Cvssv3", "properties": {"attackComplexity": {"description": "This metric describes the conditions beyond the attacker's control that must exist in order to exploit the vulnerability.", "enum": ["ATTACK_COMPLEXITY_UNSPECIFIED", "ATTACK_COMPLEXITY_LOW", "ATTACK_COMPLEXITY_HIGH"], "enumDescriptions": ["Invalid value.", "Specialized access conditions or extenuating circumstances do not exist. An attacker can expect repeatable success when attacking the vulnerable component.", "A successful attack depends on conditions beyond the attacker's control. That is, a successful attack cannot be accomplished at will, but requires the attacker to invest in some measurable amount of effort in preparation or execution against the vulnerable component before a successful attack can be expected."], "type": "string"}, "attackVector": {"description": "Base Metrics Represents the intrinsic characteristics of a vulnerability that are constant over time and across user environments. This metric reflects the context by which vulnerability exploitation is possible.", "enum": ["ATTACK_VECTOR_UNSPECIFIED", "ATTACK_VECTOR_NETWORK", "ATTACK_VECTOR_ADJACENT", "ATTACK_VECTOR_LOCAL", "ATTACK_VECTOR_PHYSICAL"], "enumDescriptions": ["Invalid value.", "The vulnerable component is bound to the network stack and the set of possible attackers extends beyond the other options listed below, up to and including the entire Internet.", "The vulnerable component is bound to the network stack, but the attack is limited at the protocol level to a logically adjacent topology.", "The vulnerable component is not bound to the network stack and the attacker's path is via read/write/execute capabilities.", "The attack requires the attacker to physically touch or manipulate the vulnerable component."], "type": "string"}, "availabilityImpact": {"description": "This metric measures the impact to the availability of the impacted component resulting from a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "baseScore": {"description": "The base score is a function of the base metric scores.", "format": "double", "type": "number"}, "confidentialityImpact": {"description": "This metric measures the impact to the confidentiality of the information resources managed by a software component due to a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "integrityImpact": {"description": "This metric measures the impact to integrity of a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "privilegesRequired": {"description": "This metric describes the level of privileges an attacker must possess before successfully exploiting the vulnerability.", "enum": ["PRIVILEGES_REQUIRED_UNSPECIFIED", "PRIVILEGES_REQUIRED_NONE", "PRIVILEGES_REQUIRED_LOW", "PRIVILEGES_REQUIRED_HIGH"], "enumDescriptions": ["Invalid value.", "The attacker is unauthorized prior to attack, and therefore does not require any access to settings or files of the vulnerable system to carry out an attack.", "The attacker requires privileges that provide basic user capabilities that could normally affect only settings and files owned by a user. Alternatively, an attacker with Low privileges has the ability to access only non-sensitive resources.", "The attacker requires privileges that provide significant (e.g., administrative) control over the vulnerable component allowing access to component-wide settings and files."], "type": "string"}, "scope": {"description": "The Scope metric captures whether a vulnerability in one vulnerable component impacts resources in components beyond its security scope.", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_UNCHANGED", "SCOPE_CHANGED"], "enumDescriptions": ["Invalid value.", "An exploited vulnerability can only affect resources managed by the same security authority.", "An exploited vulnerability can affect resources beyond the security scope managed by the security authority of the vulnerable component."], "type": "string"}, "userInteraction": {"description": "This metric captures the requirement for a human user, other than the attacker, to participate in the successful compromise of the vulnerable component.", "enum": ["USER_INTERACTION_UNSPECIFIED", "USER_INTERACTION_NONE", "USER_INTERACTION_REQUIRED"], "enumDescriptions": ["Invalid value.", "The vulnerable system can be exploited without interaction from any user.", "Successful exploitation of this vulnerability requires a user to take some action before the vulnerability can be exploited."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Cwe": {"description": "CWE stands for Common Weakness Enumeration. Information about this weakness, as described by [CWE](https://cwe.mitre.org/).", "id": "GoogleCloudSecuritycenterV2Cwe", "properties": {"id": {"description": "The CWE identifier, e.g. CWE-94", "type": "string"}, "references": {"description": "Any reference to the details on the CWE, for example, https://cwe.mitre.org/data/definitions/94.html", "items": {"$ref": "GoogleCloudSecuritycenterV2Reference"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2DataAccessEvent": {"description": "Details about a data access attempt made by a principal not authorized under applicable data security policy.", "id": "GoogleCloudSecuritycenterV2DataAccessEvent", "properties": {"eventId": {"description": "Unique identifier for data access event.", "type": "string"}, "eventTime": {"description": "Timestamp of data access event.", "format": "google-datetime", "type": "string"}, "operation": {"description": "The operation performed by the principal to access the data.", "enum": ["OPERATION_UNSPECIFIED", "READ", "MOVE", "COPY"], "enumDescriptions": ["The operation is unspecified.", "Represents a read operation.", "Represents a move operation.", "Represents a copy operation."], "type": "string"}, "principalEmail": {"description": "The email address of the principal that accessed the data. The principal could be a user account, service account, Google group, or other.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2DataFlowEvent": {"description": "Details about a data flow event, in which either the data is moved to or is accessed from a non-compliant geo-location, as defined in the applicable data security policy.", "id": "GoogleCloudSecuritycenterV2DataFlowEvent", "properties": {"eventId": {"description": "Unique identifier for data flow event.", "type": "string"}, "eventTime": {"description": "Timestamp of data flow event.", "format": "google-datetime", "type": "string"}, "operation": {"description": "The operation performed by the principal for the data flow event.", "enum": ["OPERATION_UNSPECIFIED", "READ", "MOVE", "COPY"], "enumDescriptions": ["The operation is unspecified.", "Represents a read operation.", "Represents a move operation.", "Represents a copy operation."], "type": "string"}, "principalEmail": {"description": "The email address of the principal that initiated the data flow event. The principal could be a user account, service account, Google group, or other.", "type": "string"}, "violatedLocation": {"description": "Non-compliant location of the principal or the data destination.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2DataRetentionDeletionEvent": {"description": "Details about data retention deletion violations, in which the data is non-compliant based on their retention or deletion time, as defined in the applicable data security policy. The Data Retention Deletion (DRD) control is a control of the DSPM (Data Security Posture Management) suite that enables organizations to manage data retention and deletion policies in compliance with regulations, such as GDPR and CRPA. DRD supports two primary policy types: maximum storage length (max TTL) and minimum storage length (min TTL). Both are aimed at helping organizations meet regulatory and data management commitments.", "id": "GoogleCloudSecuritycenterV2DataRetentionDeletionEvent", "properties": {"dataObjectCount": {"description": "Number of objects that violated the policy for this resource. If the number is less than 1,000, then the value of this field is the exact number. If the number of objects that violated the policy is greater than or equal to 1,000, then the value of this field is 1000.", "format": "int64", "type": "string"}, "eventDetectionTime": {"description": "Timestamp indicating when the event was detected.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "Type of the DRD event.", "enum": ["EVENT_TYPE_UNSPECIFIED", "EVENT_TYPE_MAX_TTL_EXCEEDED"], "enumDescriptions": ["Unspecified event type.", "The maximum retention time has been exceeded."], "type": "string"}, "maxRetentionAllowed": {"description": "Maximum duration of retention allowed from the DRD control. This comes from the DRD control where users set a max TTL for their data. For example, suppose that a user sets the max TTL for a Cloud Storage bucket to 90 days. However, an object in that bucket is 100 days old. In this case, a DataRetentionDeletionEvent will be generated for that Cloud Storage bucket, and the max_retention_allowed is 90 days.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Database": {"description": "Represents database access information, such as queries. A database may be a sub-resource of an instance (as in the case of Cloud SQL instances or Cloud Spanner instances), or the database instance itself. Some database resources might not have the [full resource name](https://google.aip.dev/122#full-resource-names) populated because these resource types, such as Cloud SQL databases, are not yet supported by Cloud Asset Inventory. In these cases only the display name is provided.", "id": "GoogleCloudSecuritycenterV2Database", "properties": {"displayName": {"description": "The human-readable name of the database that the user connected to.", "type": "string"}, "grantees": {"description": "The target usernames, roles, or groups of an SQL privilege grant, which is not an IAM policy change.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Some database resources may not have the [full resource name](https://google.aip.dev/122#full-resource-names) populated because these resource types are not yet supported by Cloud Asset Inventory (e.g. Cloud SQL databases). In these cases only the display name will be provided. The [full resource name](https://google.aip.dev/122#full-resource-names) of the database that the user connected to, if it is supported by Cloud Asset Inventory.", "type": "string"}, "query": {"description": "The SQL statement that is associated with the database access.", "type": "string"}, "userName": {"description": "The username used to connect to the database. The username might not be an IAM principal and does not have a set format.", "type": "string"}, "version": {"description": "The version of the database, for example, POSTGRES_14. See [the complete list](https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1/SqlDatabaseVersion).", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Denied": {"description": "Denied IP rule.", "id": "GoogleCloudSecuritycenterV2Denied", "properties": {"ipRules": {"description": "Optional. Optional list of denied IP rules.", "items": {"$ref": "GoogleCloudSecuritycenterV2IpRule"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Detection": {"description": "Memory hash detection contributing to the binary family match.", "id": "GoogleCloudSecuritycenterV2Detection", "properties": {"binary": {"description": "The name of the binary associated with the memory hash signature detection.", "type": "string"}, "percentPagesMatched": {"description": "The percentage of memory page hashes in the signature that were matched.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Disk": {"description": "Contains information about the disk associated with the finding.", "id": "GoogleCloudSecuritycenterV2Disk", "properties": {"name": {"description": "The name of the disk, for example, \"https://www.googleapis.com/compute/v1/projects/{project-id}/zones/{zone-id}/disks/{disk-id}\".", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2DiskPath": {"description": "Path of the file in terms of underlying disk/partition identifiers.", "id": "GoogleCloudSecuritycenterV2DiskPath", "properties": {"partitionUuid": {"description": "UUID of the partition (format https://wiki.archlinux.org/title/persistent_block_device_naming#by-uuid)", "type": "string"}, "relativePath": {"description": "Relative path of the file in the partition as a JSON encoded string. Example: /home/<USER>/executable_file.sh", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2DynamicMuteRecord": {"description": "The record of a dynamic mute rule that matches the finding.", "id": "GoogleCloudSecuritycenterV2DynamicMuteRecord", "properties": {"matchTime": {"description": "When the dynamic mute rule first matched the finding.", "format": "google-datetime", "type": "string"}, "muteConfig": {"description": "The relative resource name of the mute rule, represented by a mute config, that created this record, for example `organizations/123/muteConfigs/mymuteconfig` or `organizations/123/locations/global/muteConfigs/mymuteconfig`.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2EnvironmentVariable": {"description": "A name-value pair representing an environment variable used in an operating system process.", "id": "GoogleCloudSecuritycenterV2EnvironmentVariable", "properties": {"name": {"description": "Environment variable name as a JSON encoded string.", "type": "string"}, "val": {"description": "Environment variable value as a JSON encoded string.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2ExfilResource": {"description": "Resource where data was exfiltrated from or exfiltrated to.", "id": "GoogleCloudSecuritycenterV2ExfilResource", "properties": {"components": {"description": "Subcomponents of the asset that was exfiltrated, like URIs used during exfiltration, table names, databases, and filenames. For example, multiple tables might have been exfiltrated from the same Cloud SQL instance, or multiple files might have been exfiltrated from the same Cloud Storage bucket.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "The resource's [full resource name](https://cloud.google.com/apis/design/resource_names#full_resource_name).", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Exfiltration": {"description": "Exfiltration represents a data exfiltration attempt from one or more sources to one or more targets. The `sources` attribute lists the sources of the exfiltrated data. The `targets` attribute lists the destinations the data was copied to.", "id": "GoogleCloudSecuritycenterV2Exfiltration", "properties": {"sources": {"description": "If there are multiple sources, then the data is considered \"joined\" between them. For instance, BigQuery can join multiple tables, and each table would be considered a source.", "items": {"$ref": "GoogleCloudSecuritycenterV2ExfilResource"}, "type": "array"}, "targets": {"description": "If there are multiple targets, each target would get a complete copy of the \"joined\" source data.", "items": {"$ref": "GoogleCloudSecuritycenterV2ExfilResource"}, "type": "array"}, "totalExfiltratedBytes": {"description": "Total exfiltrated bytes processed for the entire job.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2ExternalSystem": {"description": "Representation of third party SIEM/SOAR fields within SCC.", "id": "GoogleCloudSecuritycenterV2ExternalSystem", "properties": {"assignees": {"description": "References primary/secondary etc assignees in the external system.", "items": {"type": "string"}, "type": "array"}, "caseCloseTime": {"description": "The time when the case was closed, as reported by the external system.", "format": "google-datetime", "type": "string"}, "caseCreateTime": {"description": "The time when the case was created, as reported by the external system.", "format": "google-datetime", "type": "string"}, "casePriority": {"description": "The priority of the finding's corresponding case in the external system.", "type": "string"}, "caseSla": {"description": "The SLA of the finding's corresponding case in the external system.", "format": "google-datetime", "type": "string"}, "caseUri": {"description": "The link to the finding's corresponding case in the external system.", "type": "string"}, "externalSystemUpdateTime": {"description": "The time when the case was last updated, as reported by the external system.", "format": "google-datetime", "type": "string"}, "externalUid": {"description": "The identifier that's used to track the finding's corresponding case in the external system.", "type": "string"}, "name": {"description": "Full resource name of the external system. The following list shows some examples: + `organizations/1234/sources/5678/findings/123456/externalSystems/jira` + `organizations/1234/sources/5678/locations/us/findings/123456/externalSystems/jira` + `folders/1234/sources/5678/findings/123456/externalSystems/jira` + `folders/1234/sources/5678/locations/us/findings/123456/externalSystems/jira` + `projects/1234/sources/5678/findings/123456/externalSystems/jira` + `projects/1234/sources/5678/locations/us/findings/123456/externalSystems/jira`", "type": "string"}, "status": {"description": "The most recent status of the finding's corresponding case, as reported by the external system.", "type": "string"}, "ticketInfo": {"$ref": "GoogleCloudSecuritycenterV2TicketInfo", "description": "Information about the ticket, if any, that is being used to track the resolution of the issue that is identified by this finding."}}, "type": "object"}, "GoogleCloudSecuritycenterV2File": {"description": "File information about the related binary/library used by an executable, or the script used by a script interpreter", "id": "GoogleCloudSecuritycenterV2File", "properties": {"contents": {"description": "Prefix of the file contents as a JSON-encoded string.", "type": "string"}, "diskPath": {"$ref": "GoogleCloudSecuritycenterV2DiskPath", "description": "Path of the file in terms of underlying disk/partition identifiers."}, "hashedSize": {"description": "The length in bytes of the file prefix that was hashed. If hashed_size == size, any hashes reported represent the entire file.", "format": "int64", "type": "string"}, "partiallyHashed": {"description": "True when the hash covers only a prefix of the file.", "type": "boolean"}, "path": {"description": "Absolute path of the file as a JSON encoded string.", "type": "string"}, "sha256": {"description": "SHA256 hash of the first hashed_size bytes of the file encoded as a hex string. If hashed_size == size, sha256 represents the SHA256 hash of the entire file.", "type": "string"}, "size": {"description": "Size of the file in bytes.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Finding": {"description": "Security Command Center finding. A finding is a record of assessment data like security, risk, health, or privacy, that is ingested into Security Command Center for presentation, notification, analysis, policy testing, and enforcement. For example, a cross-site scripting (XSS) vulnerability in an App Engine application is a finding.", "id": "GoogleCloudSecuritycenterV2Finding", "properties": {"access": {"$ref": "GoogleCloudSecuritycenterV2Access", "description": "Access details associated with the finding, such as more information on the caller, which method was accessed, and from where."}, "affectedResources": {"$ref": "GoogleCloudSecuritycenterV2AffectedResources", "description": "AffectedResources associated with the finding."}, "application": {"$ref": "GoogleCloudSecuritycenterV2Application", "description": "Represents an application associated with the finding."}, "attackExposure": {"$ref": "GoogleCloudSecuritycenterV2AttackExposure", "description": "The results of an attack path simulation relevant to this finding."}, "backupDisasterRecovery": {"$ref": "GoogleCloudSecuritycenterV2BackupDisasterRecovery", "description": "Fields related to Backup and DR findings."}, "canonicalName": {"description": "Output only. The canonical name of the finding. The following list shows some examples: + `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}` + `organizations/{organization_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}` + `folders/{folder_id}/sources/{source_id}/findings/{finding_id}` + `folders/{folder_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}` + `projects/{project_id}/sources/{source_id}/findings/{finding_id}` + `projects/{project_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}` The prefix is the closest CRM ancestor of the resource associated with the finding.", "readOnly": true, "type": "string"}, "category": {"description": "Immutable. The additional taxonomy group within findings from a given source. Example: \"XSS_FLASH_INJECTION\"", "type": "string"}, "chokepoint": {"$ref": "GoogleCloudSecuritycenterV2Chokepoint", "description": "Contains details about a chokepoint, which is a resource or resource group where high-risk attack paths converge, based on [attack path simulations] (https://cloud.google.com/security-command-center/docs/attack-exposure-learn#attack_path_simulations). This field cannot be updated. Its value is ignored in all update requests."}, "cloudArmor": {"$ref": "GoogleCloudSecuritycenterV2CloudArmor", "description": "Fields related to Cloud Armor findings."}, "cloudDlpDataProfile": {"$ref": "GoogleCloudSecuritycenterV2CloudDlpDataProfile", "description": "Cloud DLP data profile that is associated with the finding."}, "cloudDlpInspection": {"$ref": "GoogleCloudSecuritycenterV2CloudDlpInspection", "description": "Cloud Data Loss Prevention (Cloud DLP) inspection results that are associated with the finding."}, "compliances": {"description": "Contains compliance information for security standards associated to the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2Compliance"}, "type": "array"}, "connections": {"description": "Contains information about the IP connection associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2Connection"}, "type": "array"}, "contacts": {"additionalProperties": {"$ref": "GoogleCloudSecuritycenterV2ContactDetails"}, "description": "Output only. Map containing the points of contact for the given finding. The key represents the type of contact, while the value contains a list of all the contacts that pertain. Please refer to: https://cloud.google.com/resource-manager/docs/managing-notification-contacts#notification-categories { \"security\": { \"contacts\": [ { \"email\": \"<EMAIL>\" }, { \"email\": \"<EMAIL>\" } ] } }", "readOnly": true, "type": "object"}, "containers": {"description": "Containers associated with the finding. This field provides information for both Kubernetes and non-Kubernetes containers.", "items": {"$ref": "GoogleCloudSecuritycenterV2Container"}, "type": "array"}, "createTime": {"description": "Output only. The time at which the finding was created in Security Command Center.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataAccessEvents": {"description": "Data access events associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2DataAccessEvent"}, "type": "array"}, "dataFlowEvents": {"description": "Data flow events associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2DataFlowEvent"}, "type": "array"}, "dataRetentionDeletionEvents": {"description": "Data retention deletion events associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2DataRetentionDeletionEvent"}, "type": "array"}, "database": {"$ref": "GoogleCloudSecuritycenterV2Database", "description": "Database associated with the finding."}, "description": {"description": "Contains more details about the finding.", "type": "string"}, "disk": {"$ref": "GoogleCloudSecuritycenterV2Disk", "description": "<PERSON><PERSON> associated with the finding."}, "eventTime": {"description": "The time the finding was first detected. If an existing finding is updated, then this is the time the update occurred. For example, if the finding represents an open firewall, this property captures the time the detector believes the firewall became open. The accuracy is determined by the detector. If the finding is later resolved, then this time reflects when the finding was resolved. This must not be set to a value greater than the current timestamp.", "format": "google-datetime", "type": "string"}, "exfiltration": {"$ref": "GoogleCloudSecuritycenterV2Exfiltration", "description": "Represents exfiltrations associated with the finding."}, "externalSystems": {"additionalProperties": {"$ref": "GoogleCloudSecuritycenterV2ExternalSystem"}, "description": "Output only. Third party SIEM/SOAR fields within SCC, contains external system information and external system finding fields.", "readOnly": true, "type": "object"}, "externalUri": {"description": "The URI that, if available, points to a web page outside of Security Command Center where additional information about the finding can be found. This field is guaranteed to be either empty or a well formed URL.", "type": "string"}, "files": {"description": "File associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2File"}, "type": "array"}, "findingClass": {"description": "The class of the finding.", "enum": ["FINDING_CLASS_UNSPECIFIED", "THREAT", "VULNERABILITY", "MISCONFIGURATION", "OBSERVATION", "SCC_ERROR", "POSTURE_VIOLATION", "TOXIC_COMBINATION", "SENSITIVE_DATA_RISK", "CHOKEPOINT"], "enumDescriptions": ["Unspecified finding class.", "Describes unwanted or malicious activity.", "Describes a potential weakness in software that increases risk to Confidentiality & Integrity & Availability.", "Describes a potential weakness in cloud resource/asset configuration that increases risk.", "Describes a security observation that is for informational purposes.", "Describes an error that prevents some SCC functionality.", "Describes a potential security risk due to a change in the security posture.", "Describes a combination of security issues that represent a more severe security problem when taken together.", "Describes a potential security risk to data assets that contain sensitive data.", "Describes a resource or resource group where high risk attack paths converge, based on attack path simulations (APS)."], "type": "string"}, "groupMemberships": {"description": "Contains details about groups of which this finding is a member. A group is a collection of findings that are related in some way. This field cannot be updated. Its value is ignored in all update requests.", "items": {"$ref": "GoogleCloudSecuritycenterV2GroupMembership"}, "type": "array"}, "iamBindings": {"description": "Represents IAM bindings associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2IamBinding"}, "type": "array"}, "indicator": {"$ref": "GoogleCloudSecuritycenterV2Indicator", "description": "Represents what's commonly known as an *indicator of compromise* (IoC) in computer forensics. This is an artifact observed on a network or in an operating system that, with high confidence, indicates a computer intrusion. For more information, see [Indicator of compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise)."}, "ipRules": {"$ref": "GoogleCloudSecuritycenterV2IpRules", "description": "IP rules associated with the finding."}, "job": {"$ref": "GoogleCloudSecuritycenterV2Job", "description": "Job associated with the finding."}, "kernelRootkit": {"$ref": "GoogleCloudSecuritycenterV2KernelRootkit", "description": "Signature of the kernel rootkit."}, "kubernetes": {"$ref": "GoogleCloudSecuritycenterV2Kubernetes", "description": "Kubernetes resources associated with the finding."}, "loadBalancers": {"description": "The load balancers associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2LoadBalancer"}, "type": "array"}, "logEntries": {"description": "Log entries that are relevant to the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2LogEntry"}, "type": "array"}, "mitreAttack": {"$ref": "GoogleCloudSecuritycenterV2MitreAttack", "description": "MITRE ATT&CK tactics and techniques related to this finding. See: https://attack.mitre.org"}, "moduleName": {"description": "Unique identifier of the module which generated the finding. Example: folders/598186756061/securityHealthAnalyticsSettings/customModules/56799441161885", "type": "string"}, "mute": {"description": "Indicates the mute state of a finding (either muted, unmuted or undefined). Unlike other attributes of a finding, a finding provider shouldn't set the value of mute.", "enum": ["MUTE_UNSPECIFIED", "MUTED", "UNMUTED", "UNDEFINED"], "enumDescriptions": ["Unspecified.", "Finding has been muted.", "Finding has been unmuted.", "Finding has never been muted/unmuted."], "type": "string"}, "muteInfo": {"$ref": "GoogleCloudSecuritycenterV2MuteInfo", "description": "Output only. The mute information regarding this finding.", "readOnly": true}, "muteInitiator": {"description": "Records additional information about the mute operation, for example, the [mute configuration](https://cloud.google.com/security-command-center/docs/how-to-mute-findings) that muted the finding and the user who muted the finding.", "type": "string"}, "muteUpdateTime": {"description": "Output only. The most recent time this finding was muted or unmuted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "The [relative resource name](https://cloud.google.com/apis/design/resource_names#relative_resource_name) of the finding. The following list shows some examples: + `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}` + `organizations/{organization_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}` + `folders/{folder_id}/sources/{source_id}/findings/{finding_id}` + `folders/{folder_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}` + `projects/{project_id}/sources/{source_id}/findings/{finding_id}` + `projects/{project_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}`", "type": "string"}, "networks": {"description": "Represents the VPC networks that the resource is attached to.", "items": {"$ref": "GoogleCloudSecuritycenterV2Network"}, "type": "array"}, "nextSteps": {"description": "Steps to address the finding.", "type": "string"}, "notebook": {"$ref": "GoogleCloudSecuritycenterV2Notebook", "description": "Notebook associated with the finding."}, "orgPolicies": {"description": "Contains information about the org policies associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2OrgPolicy"}, "type": "array"}, "parent": {"description": "The relative resource name of the source and location the finding belongs to. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name This field is immutable after creation time. The following list shows some examples: + `organizations/{organization_id}/sources/{source_id}` + `folders/{folders_id}/sources/{source_id}` + `projects/{projects_id}/sources/{source_id}` + `organizations/{organization_id}/sources/{source_id}/locations/{location_id}` + `folders/{folders_id}/sources/{source_id}/locations/{location_id}` + `projects/{projects_id}/sources/{source_id}/locations/{location_id}`", "type": "string"}, "parentDisplayName": {"description": "Output only. The human readable display name of the finding source such as \"Event Threat Detection\" or \"Security Health Analytics\".", "readOnly": true, "type": "string"}, "processes": {"description": "Represents operating system processes associated with the Finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2Process"}, "type": "array"}, "resourceName": {"description": "Immutable. For findings on Google Cloud resources, the full resource name of the Google Cloud resource this finding is for. See: https://cloud.google.com/apis/design/resource_names#full_resource_name When the finding is for a non-Google Cloud resource, the resourceName can be a customer or partner defined string.", "type": "string"}, "securityMarks": {"$ref": "GoogleCloudSecuritycenterV2SecurityMarks", "description": "Output only. User specified security marks. These marks are entirely managed by the user and come from the SecurityMarks resource that belongs to the finding.", "readOnly": true}, "securityPosture": {"$ref": "GoogleCloudSecuritycenterV2SecurityPosture", "description": "The security posture associated with the finding."}, "severity": {"description": "The severity of the finding. This field is managed by the source that writes the finding.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["This value is used for findings when a source doesn't write a severity value.", "Vulnerability: A critical vulnerability is easily discoverable by an external actor, exploitable, and results in the direct ability to execute arbitrary code, exfiltrate data, and otherwise gain additional access and privileges to cloud resources and workloads. Examples include publicly accessible unprotected user data and public SSH access with weak or no passwords. Threat: Indicates a threat that is able to access, modify, or delete data or execute unauthorized code within existing resources.", "Vulnerability: A high risk vulnerability can be easily discovered and exploited in combination with other vulnerabilities in order to gain direct access and the ability to execute arbitrary code, exfiltrate data, and otherwise gain additional access and privileges to cloud resources and workloads. An example is a database with weak or no passwords that is only accessible internally. This database could easily be compromised by an actor that had access to the internal network. Threat: Indicates a threat that is able to create new computational resources in an environment but not able to access data or execute code in existing resources.", "Vulnerability: A medium risk vulnerability could be used by an actor to gain access to resources or privileges that enable them to eventually (through multiple steps or a complex exploit) gain access and the ability to execute arbitrary code or exfiltrate data. An example is a service account with access to more projects than it should have. If an actor gains access to the service account, they could potentially use that access to manipulate a project the service account was not intended to. Threat: Indicates a threat that is able to cause operational impact but may not access data or execute unauthorized code.", "Vulnerability: A low risk vulnerability hampers a security organization's ability to detect vulnerabilities or active threats in their deployment, or prevents the root cause investigation of security issues. An example is monitoring and logs being disabled for resource configurations and access. Threat: Indicates a threat that has obtained minimal access to an environment but is not able to access data, execute code, or create resources."], "type": "string"}, "sourceProperties": {"additionalProperties": {"type": "any"}, "description": "Source specific properties. These properties are managed by the source that writes the finding. The key names in the source_properties map must be between 1 and 255 characters, and must start with a letter and contain alphanumeric characters or underscores only.", "type": "object"}, "state": {"description": "Output only. The state of the finding.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "The finding requires attention and has not been addressed yet.", "The finding has been fixed, triaged as a non-issue or otherwise addressed and is no longer active."], "readOnly": true, "type": "string"}, "toxicCombination": {"$ref": "GoogleCloudSecuritycenterV2ToxicCombination", "description": "Contains details about a group of security issues that, when the issues occur together, represent a greater risk than when the issues occur independently. A group of such issues is referred to as a toxic combination. This field cannot be updated. Its value is ignored in all update requests."}, "vulnerability": {"$ref": "GoogleCloudSecuritycenterV2Vulnerability", "description": "Represents vulnerability-specific fields like CVE and CVSS scores. CVE stands for Common Vulnerabilities and Exposures (https://cve.mitre.org/about/)"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Folder": {"description": "Message that contains the resource name and display name of a folder resource.", "id": "GoogleCloudSecuritycenterV2Folder", "properties": {"resourceFolder": {"description": "Full resource name of this folder. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourceFolderDisplayName": {"description": "The user defined display name for this folder.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Geolocation": {"description": "Represents a geographical location for a given access.", "id": "GoogleCloudSecuritycenterV2Geolocation", "properties": {"regionCode": {"description": "A CLDR.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2GroupMembership": {"description": "Contains details about groups of which this finding is a member. A group is a collection of findings that are related in some way.", "id": "GoogleCloudSecuritycenterV2GroupMembership", "properties": {"groupId": {"description": "ID of the group.", "type": "string"}, "groupType": {"description": "Type of group.", "enum": ["GROUP_TYPE_UNSPECIFIED", "GROUP_TYPE_TOXIC_COMBINATION", "GROUP_TYPE_CHOKEPOINT"], "enumDescriptions": ["Default value.", "Group represents a toxic combination.", "Group represents a chokepoint."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IamBinding": {"description": "Represents a particular IAM binding, which captures a member's role addition, removal, or state.", "id": "GoogleCloudSecuritycenterV2IamBinding", "properties": {"action": {"description": "The action that was performed on a Binding.", "enum": ["ACTION_UNSPECIFIED", "ADD", "REMOVE"], "enumDescriptions": ["Unspecified.", "Addition of a Binding.", "Removal of a Binding."], "type": "string"}, "member": {"description": "A single identity requesting access for a Cloud Platform resource, for example, \"<EMAIL>\".", "type": "string"}, "role": {"description": "Role that is assigned to \"members\". For example, \"roles/viewer\", \"roles/editor\", or \"roles/owner\".", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Indicator": {"description": "Represents what's commonly known as an _indicator of compromise_ (IoC) in computer forensics. This is an artifact observed on a network or in an operating system that, with high confidence, indicates a computer intrusion. For more information, see [Indicator of compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).", "id": "GoogleCloudSecuritycenterV2Indicator", "properties": {"domains": {"description": "List of domains associated to the Finding.", "items": {"type": "string"}, "type": "array"}, "ipAddresses": {"description": "The list of IP addresses that are associated with the finding.", "items": {"type": "string"}, "type": "array"}, "signatures": {"description": "The list of matched signatures indicating that the given process is present in the environment.", "items": {"$ref": "GoogleCloudSecuritycenterV2ProcessSignature"}, "type": "array"}, "uris": {"description": "The list of URIs associated to the Findings.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IpRule": {"description": "IP rule information.", "id": "GoogleCloudSecuritycenterV2IpRule", "properties": {"portRanges": {"description": "Optional. An optional list of ports to which this rule applies. This field is only applicable for the UDP or (S)TCP protocols. Each entry must be either an integer or a range including a min and max port number.", "items": {"$ref": "GoogleCloudSecuritycenterV2PortRange"}, "type": "array"}, "protocol": {"description": "The IP protocol this rule applies to. This value can either be one of the following well known protocol strings (TCP, UDP, ICMP, ESP, AH, IPIP, SCTP) or a string representation of the integer value.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IpRules": {"description": "IP rules associated with the finding.", "id": "GoogleCloudSecuritycenterV2IpRules", "properties": {"allowed": {"$ref": "GoogleCloudSecuritycenterV2Allowed", "description": "Tuple with allowed rules."}, "denied": {"$ref": "GoogleCloudSecuritycenterV2Denied", "description": "<PERSON><PERSON> with denied rules."}, "destinationIpRanges": {"description": "If destination IP ranges are specified, the firewall rule applies only to traffic that has a destination IP address in these ranges. These ranges must be expressed in CIDR format. Only supports IPv4.", "items": {"type": "string"}, "type": "array"}, "direction": {"description": "The direction that the rule is applicable to, one of ingress or egress.", "enum": ["DIRECTION_UNSPECIFIED", "INGRESS", "EGRESS"], "enumDescriptions": ["Unspecified direction value.", "Ingress direction value.", "Egress direction value."], "type": "string"}, "exposedServices": {"description": "Name of the network protocol service, such as FTP, that is exposed by the open port. Follows the naming convention available at: https://www.iana.org/assignments/service-names-port-numbers/service-names-port-numbers.xhtml.", "items": {"type": "string"}, "type": "array"}, "sourceIpRanges": {"description": "If source IP ranges are specified, the firewall rule applies only to traffic that has a source IP address in these ranges. These ranges must be expressed in CIDR format. Only supports IPv4.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Issue": {"description": "Security Command Center Issue.", "id": "GoogleCloudSecuritycenterV2Issue", "properties": {"createTime": {"description": "Output only. The time the issue was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "The description of the issue in Markdown format.", "type": "string"}, "detection": {"description": "The finding category or rule name that generated the issue.", "type": "string"}, "domains": {"description": "The domains of the issue.", "items": {"$ref": "GoogleCloudSecuritycenterV2IssueDomain"}, "type": "array"}, "exposureScore": {"description": "The exposure score of the issue.", "format": "double", "type": "number"}, "issueType": {"description": "The type of the issue.", "enum": ["ISSUE_TYPE_UNSPECIFIED", "CHOKEPOINT", "TOXIC_COMBINATION", "INSIGHT"], "enumDescriptions": ["Unspecified issue type.", "Chokepoint issue type.", "Toxic combination issue type.", "Insight issue type."], "type": "string"}, "lastObservationTime": {"description": "The time the issue was last observed.", "format": "google-datetime", "type": "string"}, "mute": {"$ref": "GoogleCloudSecuritycenterV2IssueMute", "description": "The mute information of the issue."}, "name": {"description": "Identifier. The name of the issue. Format: organizations/{organization}/locations/{location}/issues/{issue}", "type": "string"}, "primaryResource": {"$ref": "GoogleCloudSecuritycenterV2IssueResource", "description": "The primary resource associated with the issue."}, "relatedFindings": {"description": "The findings related to the issue.", "items": {"$ref": "GoogleCloudSecuritycenterV2IssueFinding"}, "type": "array"}, "remediations": {"description": "Approaches to remediate the issue in Markdown format.", "items": {"type": "string"}, "type": "array"}, "secondaryResources": {"description": "Additional resources associated with the issue.", "items": {"$ref": "GoogleCloudSecuritycenterV2IssueResource"}, "type": "array"}, "securityContexts": {"description": "The security context of the issue.", "items": {"$ref": "GoogleCloudSecuritycenterV2IssueSecurityContext"}, "type": "array"}, "severity": {"description": "The severity of the issue.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["Unspecified severity.", "Critical severity.", "High severity.", "Medium severity.", "Low severity."], "type": "string"}, "state": {"description": "Output only. The state of the issue.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "Active state.", "Inactive state."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time the issue was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueDomain": {"description": "The domains of an issue.", "id": "GoogleCloudSecuritycenterV2IssueDomain", "properties": {"domainCategory": {"description": "The domain category of the issue.", "enum": ["DOMAIN_CATEGORY_UNSPECIFIED", "AI", "CODE", "CONTAINER", "DATA", "IDENTITY_AND_ACCESS", "VULNERABILITY"], "enumDescriptions": ["Unspecified domain category.", "Issues in the AI domain.", "Issues in the code domain.", "Issues in the container domain.", "Issues in the data domain.", "Issues in the identity and access domain.", "Issues in the vulnerability domain."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueFinding": {"description": "Finding related to an issue.", "id": "GoogleCloudSecuritycenterV2IssueFinding", "properties": {"cve": {"$ref": "GoogleCloudSecuritycenterV2IssueFindingCve", "description": "The CVE of the finding."}, "name": {"description": "The name of the finding.", "type": "string"}, "securityBulletin": {"$ref": "GoogleCloudSecuritycenterV2IssueFindingSecurityBulletin", "description": "The security bulletin of the finding."}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueFindingCve": {"description": "The CVE of the finding.", "id": "GoogleCloudSecuritycenterV2IssueFindingCve", "properties": {"name": {"description": "The CVE name.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueFindingSecurityBulletin": {"description": "The security bulletin of the finding.", "id": "GoogleCloudSecuritycenterV2IssueFindingSecurityBulletin", "properties": {"name": {"description": "The security bulletin name.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueMute": {"description": "The mute information of the issue.", "id": "GoogleCloudSecuritycenterV2IssueMute", "properties": {"muteInitiator": {"description": "The email address of the user who last changed the mute state of the issue.", "type": "string"}, "muteReason": {"description": "The user-provided reason for muting the issue.", "type": "string"}, "muteState": {"description": "Output only. The mute state of the issue.", "enum": ["MUTE_STATE_UNSPECIFIED", "NOT_MUTED", "MUTED"], "enumDescriptions": ["Unspecified mute state.", "Not muted.", "Muted."], "readOnly": true, "type": "string"}, "muteUpdateTime": {"description": "The time the issue was muted.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueResource": {"description": "A resource associated with the an issue.", "id": "GoogleCloudSecuritycenterV2IssueResource", "properties": {"awsMetadata": {"$ref": "GoogleCloudSecuritycenterV2IssueResourceAwsMetadata", "description": "The AWS metadata of the resource associated with the issue. Only populated for AWS resources."}, "azureMetadata": {"$ref": "GoogleCloudSecuritycenterV2IssueResourceAzureMetadata", "description": "The Azure metadata of the resource associated with the issue. Only populated for Azure resources."}, "cloudProvider": {"description": "The cloud provider of the resource associated with the issue.", "enum": ["CLOUD_PROVIDER_UNSPECIFIED", "GOOGLE_CLOUD", "AMAZON_WEB_SERVICES", "MICROSOFT_AZURE"], "enumDescriptions": ["Unspecified cloud provider.", "Google Cloud.", "Amazon Web Services.", "Microsoft Azure."], "type": "string"}, "displayName": {"description": "The resource-type specific display name of the resource associated with the issue.", "type": "string"}, "googleCloudMetadata": {"$ref": "GoogleCloudSecuritycenterV2IssueResourceGoogleCloudMetadata", "description": "The Google Cloud metadata of the resource associated with the issue. Only populated for Google Cloud resources."}, "name": {"description": "The full resource name of the resource associated with the issue.", "type": "string"}, "type": {"description": "The type of the resource associated with the issue.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueResourceAwsMetadata": {"description": "The AWS metadata of a resource associated with an issue.", "id": "GoogleCloudSecuritycenterV2IssueResourceAwsMetadata", "properties": {"account": {"$ref": "GoogleCloudSecuritycenterV2IssueResourceAwsMetadataAwsAccount", "description": "The AWS account of the resource associated with the issue."}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueResourceAwsMetadataAwsAccount": {"description": "The AWS account of the resource associated with the issue.", "id": "GoogleCloudSecuritycenterV2IssueResourceAwsMetadataAwsAccount", "properties": {"id": {"description": "The AWS account ID of the resource associated with the issue.", "type": "string"}, "name": {"description": "The AWS account name of the resource associated with the issue.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueResourceAzureMetadata": {"description": "The Azure metadata of a resource associated with an issue.", "id": "GoogleCloudSecuritycenterV2IssueResourceAzureMetadata", "properties": {"subscription": {"$ref": "GoogleCloudSecuritycenterV2IssueResourceAzureMetadataAzureSubscription", "description": "The Azure subscription of the resource associated with the issue."}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueResourceAzureMetadataAzureSubscription": {"description": "The Azure subscription of the resource associated with the issue.", "id": "GoogleCloudSecuritycenterV2IssueResourceAzureMetadataAzureSubscription", "properties": {"displayName": {"description": "The Azure subscription display name of the resource associated with the issue.", "type": "string"}, "id": {"description": "The Azure subscription ID of the resource associated with the issue.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueResourceGoogleCloudMetadata": {"description": "Google Cloud metadata of a resource associated with an issue.", "id": "GoogleCloudSecuritycenterV2IssueResourceGoogleCloudMetadata", "properties": {"projectId": {"description": "The project ID that the resource associated with the issue belongs to.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueSecurityContext": {"description": "Security context associated with an issue.", "id": "GoogleCloudSecuritycenterV2IssueSecurityContext", "properties": {"aggregatedCount": {"$ref": "GoogleCloudSecuritycenterV2IssueSecurityContextAggregatedCount", "description": "The aggregated count of the security context."}, "context": {"$ref": "GoogleCloudSecuritycenterV2IssueSecurityContextContext", "description": "The context of the security context."}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueSecurityContextAggregatedCount": {"description": "Aggregated count of a security context.", "id": "GoogleCloudSecuritycenterV2IssueSecurityContextAggregatedCount", "properties": {"key": {"description": "Aggregation key.", "type": "string"}, "value": {"description": "Aggregation value.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudSecuritycenterV2IssueSecurityContextContext": {"description": "Context of a security context.", "id": "GoogleCloudSecuritycenterV2IssueSecurityContextContext", "properties": {"type": {"description": "Context type.", "type": "string"}, "values": {"description": "Context values.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Job": {"description": "Describes a job", "id": "GoogleCloudSecuritycenterV2Job", "properties": {"errorCode": {"description": "Optional. If the job did not complete successfully, this field describes why.", "format": "int32", "type": "integer"}, "location": {"description": "Optional. Gives the location where the job ran, such as `US` or `europe-west1`", "type": "string"}, "name": {"description": "The fully-qualified name for a job. e.g. `projects//jobs/`", "type": "string"}, "state": {"description": "Output only. State of the job, such as `RUNNING` or `PENDING`.", "enum": ["JOB_STATE_UNSPECIFIED", "PENDING", "RUNNING", "SUCCEEDED", "FAILED"], "enumDescriptions": ["Unspecified represents an unknown state and should not be used.", "Job is scheduled and pending for run", "Job in progress", "Job has completed with success", "Job has completed but with failure"], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2KernelRootkit": {"description": "Kernel mode rootkit signatures.", "id": "GoogleCloudSecuritycenterV2KernelRootkit", "properties": {"name": {"description": "Rootkit name, when available.", "type": "string"}, "unexpectedCodeModification": {"description": "True if unexpected modifications of kernel code memory are present.", "type": "boolean"}, "unexpectedFtraceHandler": {"description": "True if `ftrace` points are present with callbacks pointing to regions that are not in the expected kernel or module code range.", "type": "boolean"}, "unexpectedInterruptHandler": {"description": "True if interrupt handlers that are are not in the expected kernel or module code regions are present.", "type": "boolean"}, "unexpectedKernelCodePages": {"description": "True if kernel code pages that are not in the expected kernel or module code regions are present.", "type": "boolean"}, "unexpectedKprobeHandler": {"description": "True if `kprobe` points are present with callbacks pointing to regions that are not in the expected kernel or module code range.", "type": "boolean"}, "unexpectedProcessesInRunqueue": {"description": "True if unexpected processes in the scheduler run queue are present. Such processes are in the run queue, but not in the process task list.", "type": "boolean"}, "unexpectedReadOnlyDataModification": {"description": "True if unexpected modifications of kernel read-only data memory are present.", "type": "boolean"}, "unexpectedSystemCallHandler": {"description": "True if system call handlers that are are not in the expected kernel or module code regions are present.", "type": "boolean"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Kubernetes": {"description": "Kubernetes-related attributes.", "id": "GoogleCloudSecuritycenterV2Kubernetes", "properties": {"accessReviews": {"description": "Provides information on any Kubernetes access reviews (privilege checks) relevant to the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2AccessReview"}, "type": "array"}, "bindings": {"description": "Provides Kubernetes role binding information for findings that involve [RoleBindings or ClusterRoleBindings](https://cloud.google.com/kubernetes-engine/docs/how-to/role-based-access-control).", "items": {"$ref": "GoogleCloudSecuritycenterV2Binding"}, "type": "array"}, "nodePools": {"description": "GKE [node pools](https://cloud.google.com/kubernetes-engine/docs/concepts/node-pools) associated with the finding. This field contains node pool information for each node, when it is available.", "items": {"$ref": "GoogleCloudSecuritycenterV2NodePool"}, "type": "array"}, "nodes": {"description": "Provides Kubernetes [node](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture#nodes) information.", "items": {"$ref": "GoogleCloudSecuritycenterV2Node"}, "type": "array"}, "objects": {"description": "Kubernetes objects related to the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2Object"}, "type": "array"}, "pods": {"description": "Kubernetes [Pods](https://cloud.google.com/kubernetes-engine/docs/concepts/pod) associated with the finding. This field contains Pod records for each container that is owned by a Pod.", "items": {"$ref": "GoogleCloudSecuritycenterV2Pod"}, "type": "array"}, "roles": {"description": "Provides Kubernetes role information for findings that involve [Roles or ClusterRoles](https://cloud.google.com/kubernetes-engine/docs/how-to/role-based-access-control).", "items": {"$ref": "GoogleCloudSecuritycenterV2Role"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Label": {"description": "Represents a generic name-value label. A label has separate name and value fields to support filtering with the `contains()` function. For more information, see [Filtering on array-type fields](https://cloud.google.com/security-command-center/docs/how-to-api-list-findings#array-contains-filtering).", "id": "GoogleCloudSecuritycenterV2Label", "properties": {"name": {"description": "Name of the label.", "type": "string"}, "value": {"description": "Value that corresponds to the label's name.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2LoadBalancer": {"description": "Contains information related to the load balancer associated with the finding.", "id": "GoogleCloudSecuritycenterV2LoadBalancer", "properties": {"name": {"description": "The name of the load balancer associated with the finding.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2LogEntry": {"description": "An individual entry in a log.", "id": "GoogleCloudSecuritycenterV2LogEntry", "properties": {"cloudLoggingEntry": {"$ref": "GoogleCloudSecuritycenterV2CloudLoggingEntry", "description": "An individual entry in a log stored in Cloud Logging."}}, "type": "object"}, "GoogleCloudSecuritycenterV2MemoryHashSignature": {"description": "A signature corresponding to memory page hashes.", "id": "GoogleCloudSecuritycenterV2MemoryHashSignature", "properties": {"binaryFamily": {"description": "The binary family.", "type": "string"}, "detections": {"description": "The list of memory hash detections contributing to the binary family match.", "items": {"$ref": "GoogleCloudSecuritycenterV2Detection"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2MitreAttack": {"description": "MITRE ATT&CK tactics and techniques related to this finding. See: https://attack.mitre.org", "id": "GoogleCloudSecuritycenterV2MitreAttack", "properties": {"additionalTactics": {"description": "Additional MITRE ATT&CK tactics related to this finding, if any.", "items": {"enum": ["TACTIC_UNSPECIFIED", "RECONNAISSANCE", "RESOURCE_DEVELOPMENT", "INITIAL_ACCESS", "EXECUTION", "PERSISTENCE", "PRIVILEGE_ESCALATION", "DEFENSE_EVASION", "CREDENTIAL_ACCESS", "DISCOVERY", "LATERAL_MOVEMENT", "COLLECTION", "COMMAND_AND_CONTROL", "EXFILTRATION", "IMPACT"], "enumDescriptions": ["Unspecified value.", "TA0043", "TA0042", "TA0001", "TA0002", "TA0003", "TA0004", "TA0005", "TA0006", "TA0007", "TA0008", "TA0009", "TA0011", "TA0010", "TA0040"], "type": "string"}, "type": "array"}, "additionalTechniques": {"description": "Additional MITRE ATT&CK techniques related to this finding, if any, along with any of their respective parent techniques.", "items": {"enum": ["TECHNIQUE_UNSPECIFIED", "DATA_OBFUSCATION", "DATA_OBFUSCATION_STEGANOGRAPHY", "AUTOMATED_EXFILTRATION", "OBFUSCATED_FILES_OR_INFO", "STEGANOGRAPHY", "COMPILE_AFTER_DELIVERY", "COMMAND_OBFUSCATION", "MASQUERADING", "MATCH_LEGITIMATE_NAME_OR_LOCATION", "BOOT_OR_LOGON_INITIALIZATION_SCRIPTS", "STARTUP_ITEMS", "NETWORK_SERVICE_DISCOVERY", "SCHEDULED_TASK_JOB", "CONTAINER_ORCHESTRATION_JOB", "PROCESS_INJECTION", "PROCESS_DISCOVERY", "COMMAND_AND_SCRIPTING_INTERPRETER", "UNIX_SHELL", "PYTHON", "EXPLOITATION_FOR_PRIVILEGE_ESCALATION", "PERMISSION_GROUPS_DISCOVERY", "CLOUD_GROUPS", "INDICATOR_REMOVAL_FILE_DELETION", "APPLICATION_LAYER_PROTOCOL", "DNS", "SOFTWARE_DEPLOYMENT_TOOLS", "VALID_ACCOUNTS", "DEFAULT_ACCOUNTS", "LOCAL_ACCOUNTS", "CLOUD_ACCOUNTS", "PROXY", "EXTERNAL_PROXY", "MULTI_HOP_PROXY", "ACCOUNT_MANIPULATION", "ADDITIONAL_CLOUD_CREDENTIALS", "ADDITIONAL_CLOUD_ROLES", "SSH_AUTHORIZED_KEYS", "ADDITIONAL_CONTAINER_CLUSTER_ROLES", "MULTI_STAGE_CHANNELS", "INGRESS_TOOL_TRANSFER", "NATIVE_API", "BRUTE_FORCE", "AUTOMATED_COLLECTION", "SHARED_MODULES", "DATA_ENCODING", "STANDARD_ENCODING", "ACCESS_TOKEN_MANIPULATION", "TOKEN_IMPERSONATION_OR_THEFT", "CREATE_ACCOUNT", "LOCAL_ACCOUNT", "DEOBFUSCATE_DECODE_FILES_OR_INFO", "EXPLOIT_PUBLIC_FACING_APPLICATION", "USER_EXECUTION", "DOMAIN_POLICY_MODIFICATION", "DATA_DESTRUCTION", "SERVICE_STOP", "INHIBIT_SYSTEM_RECOVERY", "FIRMWARE_CORRUPTION", "RESOURCE_HIJACKING", "NETWORK_DENIAL_OF_SERVICE", "CLOUD_SERVICE_DISCOVERY", "STEAL_APPLICATION_ACCESS_TOKEN", "ACCOUNT_ACCESS_REMOVAL", "TRANSFER_DATA_TO_CLOUD_ACCOUNT", "STEAL_WEB_SESSION_COOKIE", "CREATE_OR_MODIFY_SYSTEM_PROCESS", "EVENT_TRIGGERED_EXECUTION", "BOOT_OR_LOGON_AUTOSTART_EXECUTION", "KERNEL_MODULES_AND_EXTENSIONS", "ABUSE_ELEVATION_CONTROL_MECHANISM", "UNSECURED_CREDENTIALS", "BASH_HISTORY", "PRIVATE_KEYS", "COMPROMISE_HOST_SOFTWARE_BINARY", "CREDENTIALS_FROM_PASSWORD_STORES", "MODIFY_AUTHENTICATION_PROCESS", "IMPAIR_DEFENSES", "DISABLE_OR_MODIFY_TOOLS", "HIDE_ARTIFACTS", "HIDDEN_FILES_AND_DIRECTORIES", "HIDDEN_USERS", "EXFILTRATION_OVER_WEB_SERVICE", "EXFILTRATION_TO_CLOUD_STORAGE", "DYNAMIC_RESOLUTION", "LATERAL_TOOL_TRANSFER", "MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE", "CREATE_SNAPSHOT", "CLOUD_INFRASTRUCTURE_DISCOVERY", "DEVELOP_CAPABILITIES", "DEVELOP_CAPABILITIES_MALWARE", "OBTAIN_CAPABILITIES", "OBTAIN_CAPABILITIES_MALWARE", "ACTIVE_SCANNING", "SCANNING_IP_BLOCKS", "STAGE_CAPABILITIES", "CONTAINER_ADMINISTRATION_COMMAND", "DEPLOY_CONTAINER", "ESCAPE_TO_HOST", "CONTAINER_AND_RESOURCE_DISCOVERY", "REFLECTIVE_CODE_LOADING", "STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES"], "enumDescriptions": ["Unspecified value.", "T1001", "T1001.002", "T1020", "T1027", "T1027.003", "T1027.004", "T1027.010", "T1036", "T1036.005", "T1037", "T1037.005", "T1046", "T1053", "T1053.007", "T1055", "T1057", "T1059", "T1059.004", "T1059.006", "T1068", "T1069", "T1069.003", "T1070.004", "T1071", "T1071.004", "T1072", "T1078", "T1078.001", "T1078.003", "T1078.004", "T1090", "T1090.002", "T1090.003", "T1098", "T1098.001", "T1098.003", "T1098.004", "T1098.006", "T1104", "T1105", "T1106", "T1110", "T1119", "T1129", "T1132", "T1132.001", "T1134", "T1134.001", "T1136", "T1136.001", "T1140", "T1190", "T1204", "T1484", "T1485", "T1489", "T1490", "T1495", "T1496", "T1498", "T1526", "T1528", "T1531", "T1537", "T1539", "T1543", "T1546", "T1547", "T1547.006", "T1548", "T1552", "T1552.003", "T1552.004", "T1554", "T1555", "T1556", "T1562", "T1562.001", "T1564", "T1564.001", "T1564.002", "T1567", "T1567.002", "T1568", "T1570", "T1578", "T1578.001", "T1580", "T1587", "T1587.001", "T1588", "T1588.001", "T1595", "T1595.001", "T1608", "T1609", "T1610", "T1611", "T1613", "T1620", "T1649"], "type": "string"}, "type": "array"}, "primaryTactic": {"description": "The MITRE ATT&CK tactic most closely represented by this finding, if any.", "enum": ["TACTIC_UNSPECIFIED", "RECONNAISSANCE", "RESOURCE_DEVELOPMENT", "INITIAL_ACCESS", "EXECUTION", "PERSISTENCE", "PRIVILEGE_ESCALATION", "DEFENSE_EVASION", "CREDENTIAL_ACCESS", "DISCOVERY", "LATERAL_MOVEMENT", "COLLECTION", "COMMAND_AND_CONTROL", "EXFILTRATION", "IMPACT"], "enumDescriptions": ["Unspecified value.", "TA0043", "TA0042", "TA0001", "TA0002", "TA0003", "TA0004", "TA0005", "TA0006", "TA0007", "TA0008", "TA0009", "TA0011", "TA0010", "TA0040"], "type": "string"}, "primaryTechniques": {"description": "The MITRE ATT&CK technique most closely represented by this finding, if any. primary_techniques is a repeated field because there are multiple levels of MITRE ATT&CK techniques. If the technique most closely represented by this finding is a sub-technique (e.g. `SCANNING_IP_BLOCKS`), both the sub-technique and its parent technique(s) will be listed (e.g. `SCANNING_IP_BLOCKS`, `ACTIVE_SCANNING`).", "items": {"enum": ["TECHNIQUE_UNSPECIFIED", "DATA_OBFUSCATION", "DATA_OBFUSCATION_STEGANOGRAPHY", "AUTOMATED_EXFILTRATION", "OBFUSCATED_FILES_OR_INFO", "STEGANOGRAPHY", "COMPILE_AFTER_DELIVERY", "COMMAND_OBFUSCATION", "MASQUERADING", "MATCH_LEGITIMATE_NAME_OR_LOCATION", "BOOT_OR_LOGON_INITIALIZATION_SCRIPTS", "STARTUP_ITEMS", "NETWORK_SERVICE_DISCOVERY", "SCHEDULED_TASK_JOB", "CONTAINER_ORCHESTRATION_JOB", "PROCESS_INJECTION", "PROCESS_DISCOVERY", "COMMAND_AND_SCRIPTING_INTERPRETER", "UNIX_SHELL", "PYTHON", "EXPLOITATION_FOR_PRIVILEGE_ESCALATION", "PERMISSION_GROUPS_DISCOVERY", "CLOUD_GROUPS", "INDICATOR_REMOVAL_FILE_DELETION", "APPLICATION_LAYER_PROTOCOL", "DNS", "SOFTWARE_DEPLOYMENT_TOOLS", "VALID_ACCOUNTS", "DEFAULT_ACCOUNTS", "LOCAL_ACCOUNTS", "CLOUD_ACCOUNTS", "PROXY", "EXTERNAL_PROXY", "MULTI_HOP_PROXY", "ACCOUNT_MANIPULATION", "ADDITIONAL_CLOUD_CREDENTIALS", "ADDITIONAL_CLOUD_ROLES", "SSH_AUTHORIZED_KEYS", "ADDITIONAL_CONTAINER_CLUSTER_ROLES", "MULTI_STAGE_CHANNELS", "INGRESS_TOOL_TRANSFER", "NATIVE_API", "BRUTE_FORCE", "AUTOMATED_COLLECTION", "SHARED_MODULES", "DATA_ENCODING", "STANDARD_ENCODING", "ACCESS_TOKEN_MANIPULATION", "TOKEN_IMPERSONATION_OR_THEFT", "CREATE_ACCOUNT", "LOCAL_ACCOUNT", "DEOBFUSCATE_DECODE_FILES_OR_INFO", "EXPLOIT_PUBLIC_FACING_APPLICATION", "USER_EXECUTION", "DOMAIN_POLICY_MODIFICATION", "DATA_DESTRUCTION", "SERVICE_STOP", "INHIBIT_SYSTEM_RECOVERY", "FIRMWARE_CORRUPTION", "RESOURCE_HIJACKING", "NETWORK_DENIAL_OF_SERVICE", "CLOUD_SERVICE_DISCOVERY", "STEAL_APPLICATION_ACCESS_TOKEN", "ACCOUNT_ACCESS_REMOVAL", "TRANSFER_DATA_TO_CLOUD_ACCOUNT", "STEAL_WEB_SESSION_COOKIE", "CREATE_OR_MODIFY_SYSTEM_PROCESS", "EVENT_TRIGGERED_EXECUTION", "BOOT_OR_LOGON_AUTOSTART_EXECUTION", "KERNEL_MODULES_AND_EXTENSIONS", "ABUSE_ELEVATION_CONTROL_MECHANISM", "UNSECURED_CREDENTIALS", "BASH_HISTORY", "PRIVATE_KEYS", "COMPROMISE_HOST_SOFTWARE_BINARY", "CREDENTIALS_FROM_PASSWORD_STORES", "MODIFY_AUTHENTICATION_PROCESS", "IMPAIR_DEFENSES", "DISABLE_OR_MODIFY_TOOLS", "HIDE_ARTIFACTS", "HIDDEN_FILES_AND_DIRECTORIES", "HIDDEN_USERS", "EXFILTRATION_OVER_WEB_SERVICE", "EXFILTRATION_TO_CLOUD_STORAGE", "DYNAMIC_RESOLUTION", "LATERAL_TOOL_TRANSFER", "MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE", "CREATE_SNAPSHOT", "CLOUD_INFRASTRUCTURE_DISCOVERY", "DEVELOP_CAPABILITIES", "DEVELOP_CAPABILITIES_MALWARE", "OBTAIN_CAPABILITIES", "OBTAIN_CAPABILITIES_MALWARE", "ACTIVE_SCANNING", "SCANNING_IP_BLOCKS", "STAGE_CAPABILITIES", "CONTAINER_ADMINISTRATION_COMMAND", "DEPLOY_CONTAINER", "ESCAPE_TO_HOST", "CONTAINER_AND_RESOURCE_DISCOVERY", "REFLECTIVE_CODE_LOADING", "STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES"], "enumDescriptions": ["Unspecified value.", "T1001", "T1001.002", "T1020", "T1027", "T1027.003", "T1027.004", "T1027.010", "T1036", "T1036.005", "T1037", "T1037.005", "T1046", "T1053", "T1053.007", "T1055", "T1057", "T1059", "T1059.004", "T1059.006", "T1068", "T1069", "T1069.003", "T1070.004", "T1071", "T1071.004", "T1072", "T1078", "T1078.001", "T1078.003", "T1078.004", "T1090", "T1090.002", "T1090.003", "T1098", "T1098.001", "T1098.003", "T1098.004", "T1098.006", "T1104", "T1105", "T1106", "T1110", "T1119", "T1129", "T1132", "T1132.001", "T1134", "T1134.001", "T1136", "T1136.001", "T1140", "T1190", "T1204", "T1484", "T1485", "T1489", "T1490", "T1495", "T1496", "T1498", "T1526", "T1528", "T1531", "T1537", "T1539", "T1543", "T1546", "T1547", "T1547.006", "T1548", "T1552", "T1552.003", "T1552.004", "T1554", "T1555", "T1556", "T1562", "T1562.001", "T1564", "T1564.001", "T1564.002", "T1567", "T1567.002", "T1568", "T1570", "T1578", "T1578.001", "T1580", "T1587", "T1587.001", "T1588", "T1588.001", "T1595", "T1595.001", "T1608", "T1609", "T1610", "T1611", "T1613", "T1620", "T1649"], "type": "string"}, "type": "array"}, "version": {"description": "The MITRE ATT&CK version referenced by the above fields. E.g. \"8\".", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2MuteConfig": {"description": "A mute config is a Cloud SCC resource that contains the configuration to mute create/update events of findings.", "id": "GoogleCloudSecuritycenterV2MuteConfig", "properties": {"createTime": {"description": "Output only. The time at which the mute config was created. This field is set by the server and will be ignored if provided on config creation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A description of the mute config.", "type": "string"}, "expiryTime": {"description": "Optional. The expiry of the mute config. Only applicable for dynamic configs. If the expiry is set, when the config expires, it is removed from all findings.", "format": "google-datetime", "type": "string"}, "filter": {"description": "Required. An expression that defines the filter to apply across create/update events of findings. While creating a filter string, be mindful of the scope in which the mute configuration is being created. E.g., If a filter contains project = X but is created under the project = Y scope, it might not match any findings. The following field and operator combinations are supported: * severity: `=`, `:` * category: `=`, `:` * resource.name: `=`, `:` * resource.project_name: `=`, `:` * resource.project_display_name: `=`, `:` * resource.folders.resource_folder: `=`, `:` * resource.parent_name: `=`, `:` * resource.parent_display_name: `=`, `:` * resource.type: `=`, `:` * finding_class: `=`, `:` * indicator.ip_addresses: `=`, `:` * indicator.domains: `=`, `:`", "type": "string"}, "mostRecentEditor": {"description": "Output only. Email address of the user who last edited the mute config. This field is set by the server and will be ignored if provided on config creation or update.", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. This field will be ignored if provided on config creation. The following list shows some examples of the format: + `organizations/{organization}/muteConfigs/{mute_config}` + `organizations/{organization}locations/{location}//muteConfigs/{mute_config}` + `folders/{folder}/muteConfigs/{mute_config}` + `folders/{folder}/locations/{location}/muteConfigs/{mute_config}` + `projects/{project}/muteConfigs/{mute_config}` + `projects/{project}/locations/{location}/muteConfigs/{mute_config}`", "type": "string"}, "type": {"description": "Required. The type of the mute config, which determines what type of mute state the config affects. Immutable after creation.", "enum": ["MUTE_CONFIG_TYPE_UNSPECIFIED", "STATIC", "DYNAMIC"], "enumDescriptions": ["Unused.", "A static mute config, which sets the static mute state of future matching findings to muted. Once the static mute state has been set, finding or config modifications will not affect the state.", "A dynamic mute config, which is applied to existing and future matching findings, setting their dynamic mute state to \"muted\". If the config is updated or deleted, or a matching finding is updated, such that the finding doesn't match the config, the config will be removed from the finding, and the finding's dynamic mute state may become \"unmuted\" (unless other configs still match)."], "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the mute config was updated. This field is set by the server and will be ignored if provided on config creation or update.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2MuteInfo": {"description": "Mute information about the finding, including whether the finding has a static mute or any matching dynamic mute rules.", "id": "GoogleCloudSecuritycenterV2MuteInfo", "properties": {"dynamicMuteRecords": {"description": "The list of dynamic mute rules that currently match the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2DynamicMuteRecord"}, "type": "array"}, "staticMute": {"$ref": "GoogleCloudSecuritycenterV2StaticMute", "description": "If set, the static mute applied to this finding. Static mutes override dynamic mutes. If unset, there is no static mute."}}, "type": "object"}, "GoogleCloudSecuritycenterV2Network": {"description": "Contains information about a VPC network associated with the finding.", "id": "GoogleCloudSecuritycenterV2Network", "properties": {"name": {"description": "The name of the VPC network resource, for example, `//compute.googleapis.com/projects/my-project/global/networks/my-network`.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Node": {"description": "Kubernetes nodes associated with the finding.", "id": "GoogleCloudSecuritycenterV2Node", "properties": {"name": {"description": "[Full resource name](https://google.aip.dev/122#full-resource-names) of the Compute Engine VM running the cluster node.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2NodePool": {"description": "Provides GKE node pool information.", "id": "GoogleCloudSecuritycenterV2NodePool", "properties": {"name": {"description": "Kubernetes node pool name.", "type": "string"}, "nodes": {"description": "Nodes associated with the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV2Node"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Notebook": {"description": "Represents a Jupyter notebook IPYNB file, such as a [Colab Enterprise notebook](https://cloud.google.com/colab/docs/introduction) file, that is associated with a finding.", "id": "GoogleCloudSecuritycenterV2Notebook", "properties": {"lastAuthor": {"description": "The user ID of the latest author to modify the notebook.", "type": "string"}, "name": {"description": "The name of the notebook.", "type": "string"}, "notebookUpdateTime": {"description": "The most recent time the notebook was updated.", "format": "google-datetime", "type": "string"}, "service": {"description": "The source notebook service, for example, \"Colab Enterprise\".", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2NotificationMessage": {"description": "Cloud SCC's Notification", "id": "GoogleCloudSecuritycenterV2NotificationMessage", "properties": {"finding": {"$ref": "GoogleCloudSecuritycenterV2Finding", "description": "If it's a Finding based notification config, this field will be populated."}, "notificationConfigName": {"description": "Name of the notification config that generated current notification.", "type": "string"}, "resource": {"$ref": "GoogleCloudSecuritycenterV2Resource", "description": "The Cloud resource tied to this notification's Finding."}}, "type": "object"}, "GoogleCloudSecuritycenterV2Object": {"description": "Kubernetes object related to the finding, uniquely identified by GKNN. Used if the object Kind is not one of Pod, Node, NodePool, Binding, or AccessReview.", "id": "GoogleCloudSecuritycenterV2Object", "properties": {"containers": {"description": "Pod containers associated with this finding, if any.", "items": {"$ref": "GoogleCloudSecuritycenterV2Container"}, "type": "array"}, "group": {"description": "Kubernetes object group, such as \"policy.k8s.io/v1\".", "type": "string"}, "kind": {"description": "Kubernetes object kind, such as \"Namespace\".", "type": "string"}, "name": {"description": "Kubernetes object name. For details see https://kubernetes.io/docs/concepts/overview/working-with-objects/names/.", "type": "string"}, "ns": {"description": "Kubernetes object namespace. Must be a valid DNS label. Named \"ns\" to avoid collision with C++ namespace keyword. For details see https://kubernetes.io/docs/tasks/administer-cluster/namespaces/.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2OrgPolicy": {"description": "Contains information about the org policies associated with the finding.", "id": "GoogleCloudSecuritycenterV2OrgPolicy", "properties": {"name": {"description": "Identifier. The resource name of the org policy. Example: \"organizations/{organization_id}/policies/{constraint_name}\"", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Package": {"description": "Package is a generic definition of a package.", "id": "GoogleCloudSecuritycenterV2Package", "properties": {"cpeUri": {"description": "The CPE URI where the vulnerability was detected.", "type": "string"}, "packageName": {"description": "The name of the package where the vulnerability was detected.", "type": "string"}, "packageType": {"description": "Type of package, for example, os, maven, or go.", "type": "string"}, "packageVersion": {"description": "The version of the package.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Pod": {"description": "A Kubernetes Pod.", "id": "GoogleCloudSecuritycenterV2Pod", "properties": {"containers": {"description": "Pod containers associated with this finding, if any.", "items": {"$ref": "GoogleCloudSecuritycenterV2Container"}, "type": "array"}, "labels": {"description": "Pod labels. For Kubernetes containers, these are applied to the container.", "items": {"$ref": "GoogleCloudSecuritycenterV2Label"}, "type": "array"}, "name": {"description": "Kubernetes Pod name.", "type": "string"}, "ns": {"description": "Kubernetes Pod namespace.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2PolicyDriftDetails": {"description": "The policy field that violates the deployed posture and its expected and detected values.", "id": "GoogleCloudSecuritycenterV2PolicyDriftDetails", "properties": {"detectedValue": {"description": "The detected value that violates the deployed posture, for example, `false` or `allowed_values={\"projects/22831892\"}`.", "type": "string"}, "expectedValue": {"description": "The value of this field that was configured in a posture, for example, `true` or `allowed_values={\"projects/29831892\"}`.", "type": "string"}, "field": {"description": "The name of the updated field, for example constraint.implementation.policy_rules[0].enforce", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2PortRange": {"description": "A port range which is inclusive of the min and max values. Values are between 0 and 2^16-1. The max can be equal / must be not smaller than the min value. If min and max are equal this indicates that it is a single port.", "id": "GoogleCloudSecuritycenterV2PortRange", "properties": {"max": {"description": "Maximum port value.", "format": "int64", "type": "string"}, "min": {"description": "Minimum port value.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Process": {"description": "Represents an operating system process.", "id": "GoogleCloudSecuritycenterV2Process", "properties": {"args": {"description": "Process arguments as JSON encoded strings.", "items": {"type": "string"}, "type": "array"}, "argumentsTruncated": {"description": "True if `args` is incomplete.", "type": "boolean"}, "binary": {"$ref": "GoogleCloudSecuritycenterV2File", "description": "File information for the process executable."}, "envVariables": {"description": "Process environment variables.", "items": {"$ref": "GoogleCloudSecuritycenterV2EnvironmentVariable"}, "type": "array"}, "envVariablesTruncated": {"description": "True if `env_variables` is incomplete.", "type": "boolean"}, "libraries": {"description": "File information for libraries loaded by the process.", "items": {"$ref": "GoogleCloudSecuritycenterV2File"}, "type": "array"}, "name": {"description": "The process name, as displayed in utilities like `top` and `ps`. This name can be accessed through `/proc/[pid]/comm` and changed with `prctl(PR_SET_NAME)`.", "type": "string"}, "parentPid": {"description": "The parent process ID.", "format": "int64", "type": "string"}, "pid": {"description": "The process ID.", "format": "int64", "type": "string"}, "script": {"$ref": "GoogleCloudSecuritycenterV2File", "description": "When the process represents the invocation of a script, `binary` provides information about the interpreter, while `script` provides information about the script file provided to the interpreter."}}, "type": "object"}, "GoogleCloudSecuritycenterV2ProcessSignature": {"description": "Indicates what signature matched this process.", "id": "GoogleCloudSecuritycenterV2ProcessSignature", "properties": {"memoryHashSignature": {"$ref": "GoogleCloudSecuritycenterV2MemoryHashSignature", "description": "Signature indicating that a binary family was matched."}, "signatureType": {"description": "Describes the type of resource associated with the signature.", "enum": ["SIGNATURE_TYPE_UNSPECIFIED", "SIGNATURE_TYPE_PROCESS", "SIGNATURE_TYPE_FILE"], "enumDescriptions": ["The default signature type.", "Used for signatures concerning processes.", "Used for signatures concerning disks."], "type": "string"}, "yaraRuleSignature": {"$ref": "GoogleCloudSecuritycenterV2YaraRuleSignature", "description": "Signature indicating that a YARA rule was matched."}}, "type": "object"}, "GoogleCloudSecuritycenterV2Reference": {"description": "Additional Links", "id": "GoogleCloudSecuritycenterV2Reference", "properties": {"source": {"description": "Source of the reference e.g. NVD", "type": "string"}, "uri": {"description": "Uri for the mentioned source e.g. https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Requests": {"description": "Information about the requests relevant to the finding.", "id": "GoogleCloudSecuritycenterV2Requests", "properties": {"longTermAllowed": {"description": "Allowed RPS (requests per second) over the long term.", "format": "int32", "type": "integer"}, "longTermDenied": {"description": "Denied RPS (requests per second) over the long term.", "format": "int32", "type": "integer"}, "ratio": {"description": "For 'Increasing deny ratio', the ratio is the denied traffic divided by the allowed traffic. For 'Allowed traffic spike', the ratio is the allowed traffic in the short term divided by allowed traffic in the long term.", "format": "double", "type": "number"}, "shortTermAllowed": {"description": "Allowed RPS (requests per second) in the short term.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Resource": {"description": "Information related to the Google Cloud resource.", "id": "GoogleCloudSecuritycenterV2Resource", "properties": {"awsMetadata": {"$ref": "GoogleCloudSecuritycenterV2AwsMetadata", "description": "The AWS metadata associated with the finding."}, "azureMetadata": {"$ref": "GoogleCloudSecuritycenterV2AzureMetadata", "description": "The Azure metadata associated with the finding."}, "cloudProvider": {"description": "Indicates which cloud provider the finding is from.", "enum": ["CLOUD_PROVIDER_UNSPECIFIED", "GOOGLE_CLOUD_PLATFORM", "AMAZON_WEB_SERVICES", "MICROSOFT_AZURE"], "enumDescriptions": ["The cloud provider is unspecified.", "The cloud provider is Google Cloud Platform.", "The cloud provider is Amazon Web Services.", "The cloud provider is Microsoft Azure."], "type": "string"}, "displayName": {"description": "The human readable name of the resource.", "type": "string"}, "gcpMetadata": {"$ref": "GcpMetadata", "description": "The GCP metadata associated with the finding."}, "location": {"description": "The region or location of the service (if applicable).", "type": "string"}, "name": {"description": "The full resource name of the resource. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourcePath": {"$ref": "GoogleCloudSecuritycenterV2ResourcePath", "description": "Provides the path to the resource within the resource hierarchy."}, "resourcePathString": {"description": "A string representation of the resource path. For Google Cloud, it has the format of `organizations/{organization_id}/folders/{folder_id}/folders/{folder_id}/projects/{project_id}` where there can be any number of folders. For AWS, it has the format of `org/{organization_id}/ou/{organizational_unit_id}/ou/{organizational_unit_id}/account/{account_id}` where there can be any number of organizational units. For Azure, it has the format of `mg/{management_group_id}/mg/{management_group_id}/subscription/{subscription_id}/rg/{resource_group_name}` where there can be any number of management groups.", "type": "string"}, "service": {"description": "The service or resource provider associated with the resource.", "type": "string"}, "type": {"description": "The full resource type of the resource.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2ResourcePath": {"description": "Represents the path of resources leading up to the resource this finding is about.", "id": "GoogleCloudSecuritycenterV2ResourcePath", "properties": {"nodes": {"description": "The list of nodes that make the up resource path, ordered from lowest level to highest level.", "items": {"$ref": "GoogleCloudSecuritycenterV2ResourcePathNode"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2ResourcePathNode": {"description": "A node within the resource path. Each node represents a resource within the resource hierarchy.", "id": "GoogleCloudSecuritycenterV2ResourcePathNode", "properties": {"displayName": {"description": "The display name of the resource this node represents.", "type": "string"}, "id": {"description": "The ID of the resource this node represents.", "type": "string"}, "nodeType": {"description": "The type of resource this node represents.", "enum": ["RESOURCE_PATH_NODE_TYPE_UNSPECIFIED", "GCP_ORGANIZATION", "GCP_FOLDER", "GCP_PROJECT", "AWS_ORGANIZATION", "AWS_ORGANIZATIONAL_UNIT", "AWS_ACCOUNT", "AZURE_MANAGEMENT_GROUP", "AZURE_SUBSCRIPTION", "AZURE_RESOURCE_GROUP"], "enumDescriptions": ["Node type is unspecified.", "The node represents a Google Cloud organization.", "The node represents a Google Cloud folder.", "The node represents a Google Cloud project.", "The node represents an AWS organization.", "The node represents an AWS organizational unit.", "The node represents an AWS account.", "The node represents an Azure management group.", "The node represents an Azure subscription.", "The node represents an Azure resource group."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2ResourceValueConfig": {"description": "A resource value configuration (RVC) is a mapping configuration of user's resources to resource values. Used in Attack path simulations.", "id": "GoogleCloudSecuritycenterV2ResourceValueConfig", "properties": {"cloudProvider": {"description": "Cloud provider this configuration applies to", "enum": ["CLOUD_PROVIDER_UNSPECIFIED", "GOOGLE_CLOUD_PLATFORM", "AMAZON_WEB_SERVICES", "MICROSOFT_AZURE"], "enumDescriptions": ["The cloud provider is unspecified.", "The cloud provider is Google Cloud Platform.", "The cloud provider is Amazon Web Services.", "The cloud provider is Microsoft Azure."], "type": "string"}, "createTime": {"description": "Output only. Timestamp this resource value configuration was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Description of the resource value configuration.", "type": "string"}, "name": {"description": "Identifier. Name for the resource value configuration", "type": "string"}, "resourceLabelsSelector": {"additionalProperties": {"type": "string"}, "description": "List of resource labels to search for, evaluated with `AND`. For example, \"resource_labels_selector\": {\"key\": \"value\", \"env\": \"prod\"} will match resources with labels \"key\": \"value\" `AND` \"env\": \"prod\" https://cloud.google.com/resource-manager/docs/creating-managing-labels", "type": "object"}, "resourceType": {"description": "Apply resource_value only to resources that match resource_type. resource_type will be checked with `AND` of other resources. For example, \"storage.googleapis.com/Bucket\" with resource_value \"HIGH\" will apply \"HIGH\" value only to \"storage.googleapis.com/Bucket\" resources.", "type": "string"}, "resourceValue": {"description": "Resource value level this expression represents Only required when there is no Sensitive Data Protection mapping in the request", "enum": ["RESOURCE_VALUE_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "NONE"], "enumDescriptions": ["Unspecific value", "High resource value", "Medium resource value", "Low resource value", "No resource value, e.g. ignore these resources"], "type": "string"}, "scope": {"description": "Project or folder to scope this configuration to. For example, \"project/456\" would apply this configuration only to resources in \"project/456\" scope and will be checked with `AND` of other resources.", "type": "string"}, "sensitiveDataProtectionMapping": {"$ref": "GoogleCloudSecuritycenterV2SensitiveDataProtectionMapping", "description": "A mapping of the sensitivity on Sensitive Data Protection finding to resource values. This mapping can only be used in combination with a resource_type that is related to BigQuery, e.g. \"bigquery.googleapis.com/Dataset\"."}, "tagValues": {"description": "Tag values combined with `AND` to check against. For Google Cloud resources, they are tag value IDs in the form of \"tagValues/123\". Example: `[ \"tagValues/123\", \"tagValues/456\", \"tagValues/789\" ]` https://cloud.google.com/resource-manager/docs/tags/tags-creating-and-managing", "items": {"type": "string"}, "type": "array"}, "updateTime": {"description": "Output only. Timestamp this resource value configuration was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Role": {"description": "Kubernetes Role or ClusterRole.", "id": "GoogleCloudSecuritycenterV2Role", "properties": {"kind": {"description": "Role type.", "enum": ["KIND_UNSPECIFIED", "ROLE", "CLUSTER_ROLE"], "enumDescriptions": ["Role type is not specified.", "Kubernetes Role.", "Kubernetes ClusterRole."], "type": "string"}, "name": {"description": "Role name.", "type": "string"}, "ns": {"description": "Role namespace.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2SecurityBulletin": {"description": "SecurityBulletin are notifications of vulnerabilities of Google products.", "id": "GoogleCloudSecuritycenterV2SecurityBulletin", "properties": {"bulletinId": {"description": "ID of the bulletin corresponding to the vulnerability.", "type": "string"}, "submissionTime": {"description": "Submission time of this Security Bulletin.", "format": "google-datetime", "type": "string"}, "suggestedUpgradeVersion": {"description": "This represents a version that the cluster receiving this notification should be upgraded to, based on its current version. For example, 1.15.0", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2SecurityMarks": {"description": "User specified security marks that are attached to the parent Security Command Center resource. Security marks are scoped within a Security Command Center organization -- they can be modified and viewed by all users who have proper permissions on the organization.", "id": "GoogleCloudSecuritycenterV2SecurityMarks", "properties": {"canonicalName": {"description": "The canonical name of the marks. The following list shows some examples: + `organizations/{organization_id}/assets/{asset_id}/securityMarks` + `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks` + `organizations/{organization_id}/sources/{source_id}/locations/{location}/findings/{finding_id}/securityMarks` + `folders/{folder_id}/assets/{asset_id}/securityMarks` + `folders/{folder_id}/sources/{source_id}/findings/{finding_id}/securityMarks` + `folders/{folder_id}/sources/{source_id}/locations/{location}/findings/{finding_id}/securityMarks` + `projects/{project_number}/assets/{asset_id}/securityMarks` + `projects/{project_number}/sources/{source_id}/findings/{finding_id}/securityMarks` + `projects/{project_number}/sources/{source_id}/locations/{location}/findings/{finding_id}/securityMarks`", "type": "string"}, "marks": {"additionalProperties": {"type": "string"}, "description": "Mutable user specified security marks belonging to the parent resource. Constraints are as follows: * Keys and values are treated as case insensitive * Keys must be between 1 - 256 characters (inclusive) * Keys must be letters, numbers, underscores, or dashes * Values have leading and trailing whitespace trimmed, remaining characters must be between 1 - 4096 characters (inclusive)", "type": "object"}, "name": {"description": "The relative resource name of the SecurityMarks. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name The following list shows some examples: + `organizations/{organization_id}/assets/{asset_id}/securityMarks` + `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks` + `organizations/{organization_id}/sources/{source_id}/locations/{location}/findings/{finding_id}/securityMarks`", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2SecurityPolicy": {"description": "Information about the [Google Cloud Armor security policy](https://cloud.google.com/armor/docs/security-policy-overview) relevant to the finding.", "id": "GoogleCloudSecuritycenterV2SecurityPolicy", "properties": {"name": {"description": "The name of the Google Cloud Armor security policy, for example, \"my-security-policy\".", "type": "string"}, "preview": {"description": "Whether or not the associated rule or policy is in preview mode.", "type": "boolean"}, "type": {"description": "The type of Google Cloud Armor security policy for example, 'backend security policy', 'edge security policy', 'network edge security policy', or 'always-on DDoS protection'.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2SecurityPosture": {"description": "Represents a posture that is deployed on Google Cloud by the Security Command Center Posture Management service. A posture contains one or more policy sets. A policy set is a group of policies that enforce a set of security rules on Google Cloud.", "id": "GoogleCloudSecuritycenterV2SecurityPosture", "properties": {"changedPolicy": {"description": "The name of the updated policy, for example, `projects/{project_id}/policies/{constraint_name}`.", "type": "string"}, "name": {"description": "Name of the posture, for example, `CIS-Posture`.", "type": "string"}, "policy": {"description": "The ID of the updated policy, for example, `compute-policy-1`.", "type": "string"}, "policyDriftDetails": {"description": "The details about a change in an updated policy that violates the deployed posture.", "items": {"$ref": "GoogleCloudSecuritycenterV2PolicyDriftDetails"}, "type": "array"}, "policySet": {"description": "The name of the updated policy set, for example, `cis-policyset`.", "type": "string"}, "postureDeployment": {"description": "The name of the posture deployment, for example, `organizations/{org_id}/posturedeployments/{posture_deployment_id}`.", "type": "string"}, "postureDeploymentResource": {"description": "The project, folder, or organization on which the posture is deployed, for example, `projects/{project_number}`.", "type": "string"}, "revisionId": {"description": "The version of the posture, for example, `c7cfa2a8`.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2SensitiveDataProtectionMapping": {"description": "Resource value mapping for Sensitive Data Protection findings If any of these mappings have a resource value that is not unspecified, the resource_value field will be ignored when reading this configuration.", "id": "GoogleCloudSecuritycenterV2SensitiveDataProtectionMapping", "properties": {"highSensitivityMapping": {"description": "Resource value mapping for high-sensitivity Sensitive Data Protection findings", "enum": ["RESOURCE_VALUE_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "NONE"], "enumDescriptions": ["Unspecific value", "High resource value", "Medium resource value", "Low resource value", "No resource value, e.g. ignore these resources"], "type": "string"}, "mediumSensitivityMapping": {"description": "Resource value mapping for medium-sensitivity Sensitive Data Protection findings", "enum": ["RESOURCE_VALUE_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "NONE"], "enumDescriptions": ["Unspecific value", "High resource value", "Medium resource value", "Low resource value", "No resource value, e.g. ignore these resources"], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2ServiceAccountDelegationInfo": {"description": "Identity delegation history of an authenticated service account.", "id": "GoogleCloudSecuritycenterV2ServiceAccountDelegationInfo", "properties": {"principalEmail": {"description": "The email address of a Google account.", "type": "string"}, "principalSubject": {"description": "A string representing the principal_subject associated with the identity. As compared to `principal_email`, supports principals that aren't associated with email addresses, such as third party principals. For most identities, the format will be `principal://iam.googleapis.com/{identity pool name}/subjects/{subject}` except for some GKE identities (GKE_WORKLOAD, FREEFORM, GKE_HUB_WORKLOAD) that are still in the legacy format `serviceAccount:{identity pool name}[{subject}]`", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2StaticMute": {"description": "Information about the static mute state. A static mute state overrides any dynamic mute rules that apply to this finding. The static mute state can be set by a static mute rule or by muting the finding directly.", "id": "GoogleCloudSecuritycenterV2StaticMute", "properties": {"applyTime": {"description": "When the static mute was applied.", "format": "google-datetime", "type": "string"}, "state": {"description": "The static mute state. If the value is `MUTED` or `UNMUTED`, then the finding's overall mute state will have the same value.", "enum": ["MUTE_UNSPECIFIED", "MUTED", "UNMUTED", "UNDEFINED"], "enumDescriptions": ["Unspecified.", "Finding has been muted.", "Finding has been unmuted.", "Finding has never been muted/unmuted."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Subject": {"description": "Represents a Kubernetes subject.", "id": "GoogleCloudSecuritycenterV2Subject", "properties": {"kind": {"description": "Authentication type for the subject.", "enum": ["AUTH_TYPE_UNSPECIFIED", "USER", "SERVICEACCOUNT", "GROUP"], "enumDescriptions": ["Authentication is not specified.", "User with valid certificate.", "Users managed by Kubernetes API with credentials stored as secrets.", "Collection of users."], "type": "string"}, "name": {"description": "Name for the subject.", "type": "string"}, "ns": {"description": "Namespace for the subject.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2TicketInfo": {"description": "Information about the ticket, if any, that is being used to track the resolution of the issue that is identified by this finding.", "id": "GoogleCloudSecuritycenterV2TicketInfo", "properties": {"assignee": {"description": "The assignee of the ticket in the ticket system.", "type": "string"}, "description": {"description": "The description of the ticket in the ticket system.", "type": "string"}, "id": {"description": "The identifier of the ticket in the ticket system.", "type": "string"}, "status": {"description": "The latest status of the ticket, as reported by the ticket system.", "type": "string"}, "updateTime": {"description": "The time when the ticket was last updated, as reported by the ticket system.", "format": "google-datetime", "type": "string"}, "uri": {"description": "The link to the ticket in the ticket system.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV2ToxicCombination": {"description": "Contains details about a group of security issues that, when the issues occur together, represent a greater risk than when the issues occur independently. A group of such issues is referred to as a toxic combination.", "id": "GoogleCloudSecuritycenterV2ToxicCombination", "properties": {"attackExposureScore": {"description": "The [Attack exposure score](https://cloud.google.com/security-command-center/docs/attack-exposure-learn#attack_exposure_scores) of this toxic combination. The score is a measure of how much this toxic combination exposes one or more high-value resources to potential attack.", "format": "double", "type": "number"}, "relatedFindings": {"description": "List of resource names of findings associated with this toxic combination. For example, `organizations/123/sources/456/findings/789`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV2Vulnerability": {"description": "Refers to common vulnerability fields e.g. cve, cvss, cwe etc.", "id": "GoogleCloudSecuritycenterV2Vulnerability", "properties": {"cve": {"$ref": "GoogleCloudSecuritycenterV2Cve", "description": "CVE stands for Common Vulnerabilities and Exposures (https://cve.mitre.org/about/)"}, "cwes": {"description": "Represents one or more Common Weakness Enumeration (CWE) information on this vulnerability.", "items": {"$ref": "GoogleCloudSecuritycenterV2Cwe"}, "type": "array"}, "fixedPackage": {"$ref": "GoogleCloudSecuritycenterV2Package", "description": "The fixed package is relevant to the finding."}, "offendingPackage": {"$ref": "GoogleCloudSecuritycenterV2Package", "description": "The offending package is relevant to the finding."}, "providerRiskScore": {"description": "Provider provided risk_score based on multiple factors. The higher the risk score, the more risky the vulnerability is.", "format": "int64", "type": "string"}, "reachable": {"description": "Represents whether the vulnerability is reachable (detected via static analysis)", "type": "boolean"}, "securityBulletin": {"$ref": "GoogleCloudSecuritycenterV2SecurityBulletin", "description": "The security bulletin is relevant to this finding."}}, "type": "object"}, "GoogleCloudSecuritycenterV2YaraRuleSignature": {"description": "A signature corresponding to a YARA rule.", "id": "GoogleCloudSecuritycenterV2YaraRuleSignature", "properties": {"yaraRule": {"description": "The name of the YARA rule.", "type": "string"}}, "type": "object"}, "GroupAssetsRequest": {"description": "Request message for grouping by assets.", "id": "GroupAssetsRequest", "properties": {"compareDuration": {"description": "When compare_duration is set, the Asset's \"state\" property is updated to indicate whether the asset was added, removed, or remained present during the compare_duration period of time that precedes the read_time. This is the time between (read_time - compare_duration) and read_time. The state value is derived based on the presence of the asset at the two points in time. Intermediate state changes between the two times don't affect the result. For example, the results aren't affected if the asset is removed and re-created again. Possible \"state\" values when compare_duration is specified: * \"ADDED\": indicates that the asset was not present before compare_duration, but present at reference_time. * \"REMOVED\": indicates that the asset was present at the start of compare_duration, but not present at reference_time. * \"ACTIVE\": indicates that the asset was present at both the start and the end of the time period defined by compare_duration and reference_time. This field is ignored if `state` is not a field in `group_by`.", "format": "google-duration", "type": "string"}, "filter": {"description": "Expression that defines the filter to apply across assets. The expression is a list of zero or more restrictions combined via logical operators `AND` and `OR`. Parentheses are not supported, and `OR` has higher precedence than `AND`. Restrictions have the form ` ` and may have a `-` character in front of them to indicate negation. The fields map to those defined in the Asset resource. Examples include: * name * security_center_properties.resource_name * resource_properties.a_property * security_marks.marks.marka The supported operators are: * `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching, for strings. The supported value types are: * string literals in quotes. * integer literals without quotes. * boolean literals `true` and `false` without quotes. For example, `resource_properties.size = 100` is a valid filter string.", "type": "string"}, "groupBy": {"description": "Required. Expression that defines what assets fields to use for grouping. The string value should follow SQL syntax: comma separated list of fields. For example: \"security_center_properties.resource_project,security_center_properties.project\". The following fields are supported when compare_duration is not set: * security_center_properties.resource_project * security_center_properties.resource_type * security_center_properties.resource_parent The following fields are supported when compare_duration is set: * security_center_properties.resource_type", "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response. Default is 10, minimum is 1, maximum is 1000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "The value returned by the last `GroupAssetsResponse`; indicates that this is a continuation of a prior `GroupAssets` call, and that the system should return the next page of data.", "type": "string"}, "readTime": {"description": "Time used as a reference point when filtering assets. The filter is limited to assets existing at the supplied time and their values are those at that specific time. Absence of this field will default to the API's version of NOW.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GroupAssetsResponse": {"description": "Response message for grouping by assets.", "id": "GroupAssetsResponse", "properties": {"groupByResults": {"description": "Group results. There exists an element for each existing unique combination of property/values. The element contains a count for the number of times those specific property/values appear.", "items": {"$ref": "GroupResult"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results.", "type": "string"}, "readTime": {"description": "Time used for executing the groupBy request.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GroupFindingsRequest": {"description": "Request message for grouping by findings.", "id": "GroupFindingsRequest", "properties": {"filter": {"description": "Expression that defines the filter to apply across findings. The expression is a list of one or more restrictions combined via logical operators `AND` and `OR`. Parentheses are not supported, and `OR` has higher precedence than `AND`. Restrictions have the form ` ` and may have a `-` character in front of them to indicate negation. Examples include: * name * source_properties.a_property * security_marks.marks.marka The supported operators are: * `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching, for strings. The supported value types are: * string literals in quotes. * integer literals without quotes. * boolean literals `true` and `false` without quotes. For example, `source_properties.size = 100` is a valid filter string.", "type": "string"}, "groupBy": {"description": "Required. Expression that defines what assets fields to use for grouping (including `state`). The string value should follow SQL syntax: comma separated list of fields. For example: \"parent,resource_name\". The following fields are supported: * resource_name * category * state * parent", "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response. Default is 10, minimum is 1, maximum is 1000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "The value returned by the last `GroupFindingsResponse`; indicates that this is a continuation of a prior `GroupFindings` call, and that the system should return the next page of data.", "type": "string"}, "readTime": {"description": "Time used as a reference point when filtering findings. The filter is limited to findings existing at the supplied time and their values are those at that specific time. Absence of this field will default to the API's version of NOW.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GroupFindingsResponse": {"description": "Response message for group by findings.", "id": "GroupFindingsResponse", "properties": {"groupByResults": {"description": "Group results. There exists an element for each existing unique combination of property/values. The element contains a count for the number of times those specific property/values appear.", "items": {"$ref": "GroupResult"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results.", "type": "string"}, "readTime": {"description": "Time used for executing the groupBy request.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GroupMembership": {"description": "Contains details about groups of which this finding is a member. A group is a collection of findings that are related in some way.", "id": "GroupMembership", "properties": {"groupId": {"description": "ID of the group.", "type": "string"}, "groupType": {"description": "Type of group.", "enum": ["GROUP_TYPE_UNSPECIFIED", "GROUP_TYPE_TOXIC_COMBINATION", "GROUP_TYPE_CHOKEPOINT"], "enumDescriptions": ["Default value.", "Group represents a toxic combination.", "Group represents a chokepoint."], "type": "string"}}, "type": "object"}, "GroupResult": {"description": "Result containing the properties and count of a groupBy request.", "id": "GroupResult", "properties": {"count": {"description": "Total count of resources for the given properties.", "format": "int64", "type": "string"}, "properties": {"additionalProperties": {"type": "any"}, "description": "Properties matching the groupBy fields in the request.", "type": "object"}}, "type": "object"}, "IamBinding": {"description": "Represents a particular IAM binding, which captures a member's role addition, removal, or state.", "id": "<PERSON>am<PERSON><PERSON><PERSON>", "properties": {"action": {"description": "The action that was performed on a Binding.", "enum": ["ACTION_UNSPECIFIED", "ADD", "REMOVE"], "enumDescriptions": ["Unspecified.", "Addition of a Binding.", "Removal of a Binding."], "type": "string"}, "member": {"description": "A single identity requesting access for a Cloud Platform resource, for example, \"<EMAIL>\".", "type": "string"}, "role": {"description": "Role that is assigned to \"members\". For example, \"roles/viewer\", \"roles/editor\", or \"roles/owner\".", "type": "string"}}, "type": "object"}, "Indicator": {"description": "Represents what's commonly known as an _indicator of compromise_ (IoC) in computer forensics. This is an artifact observed on a network or in an operating system that, with high confidence, indicates a computer intrusion. For more information, see [Indicator of compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).", "id": "Indicator", "properties": {"domains": {"description": "List of domains associated to the Finding.", "items": {"type": "string"}, "type": "array"}, "ipAddresses": {"description": "The list of IP addresses that are associated with the finding.", "items": {"type": "string"}, "type": "array"}, "signatures": {"description": "The list of matched signatures indicating that the given process is present in the environment.", "items": {"$ref": "ProcessSignature"}, "type": "array"}, "uris": {"description": "The list of URIs associated to the Findings.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "IpRule": {"description": "IP rule information.", "id": "IpRule", "properties": {"portRanges": {"description": "Optional. An optional list of ports to which this rule applies. This field is only applicable for the UDP or (S)TCP protocols. Each entry must be either an integer or a range including a min and max port number.", "items": {"$ref": "PortRange"}, "type": "array"}, "protocol": {"description": "The IP protocol this rule applies to. This value can either be one of the following well known protocol strings (TCP, UDP, ICMP, ESP, AH, IPIP, SCTP) or a string representation of the integer value.", "type": "string"}}, "type": "object"}, "IpRules": {"description": "IP rules associated with the finding.", "id": "IpRules", "properties": {"allowed": {"$ref": "Allowed", "description": "Tuple with allowed rules."}, "denied": {"$ref": "Denied", "description": "<PERSON><PERSON> with denied rules."}, "destinationIpRanges": {"description": "If destination IP ranges are specified, the firewall rule applies only to traffic that has a destination IP address in these ranges. These ranges must be expressed in CIDR format. Only supports IPv4.", "items": {"type": "string"}, "type": "array"}, "direction": {"description": "The direction that the rule is applicable to, one of ingress or egress.", "enum": ["DIRECTION_UNSPECIFIED", "INGRESS", "EGRESS"], "enumDescriptions": ["Unspecified direction value.", "Ingress direction value.", "Egress direction value."], "type": "string"}, "exposedServices": {"description": "Name of the network protocol service, such as FTP, that is exposed by the open port. Follows the naming convention available at: https://www.iana.org/assignments/service-names-port-numbers/service-names-port-numbers.xhtml.", "items": {"type": "string"}, "type": "array"}, "sourceIpRanges": {"description": "If source IP ranges are specified, the firewall rule applies only to traffic that has a source IP address in these ranges. These ranges must be expressed in CIDR format. Only supports IPv4.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Job": {"description": "Describes a job", "id": "Job", "properties": {"errorCode": {"description": "Optional. If the job did not complete successfully, this field describes why.", "format": "int32", "type": "integer"}, "location": {"description": "Optional. Gives the location where the job ran, such as `US` or `europe-west1`", "type": "string"}, "name": {"description": "The fully-qualified name for a job. e.g. `projects//jobs/`", "type": "string"}, "state": {"description": "Output only. State of the job, such as `RUNNING` or `PENDING`.", "enum": ["JOB_STATE_UNSPECIFIED", "PENDING", "RUNNING", "SUCCEEDED", "FAILED"], "enumDescriptions": ["Unspecified represents an unknown state and should not be used.", "Job is scheduled and pending for run", "Job in progress", "Job has completed with success", "Job has completed but with failure"], "readOnly": true, "type": "string"}}, "type": "object"}, "KernelRootkit": {"description": "Kernel mode rootkit signatures.", "id": "KernelRootkit", "properties": {"name": {"description": "Rootkit name, when available.", "type": "string"}, "unexpectedCodeModification": {"description": "True if unexpected modifications of kernel code memory are present.", "type": "boolean"}, "unexpectedFtraceHandler": {"description": "True if `ftrace` points are present with callbacks pointing to regions that are not in the expected kernel or module code range.", "type": "boolean"}, "unexpectedInterruptHandler": {"description": "True if interrupt handlers that are are not in the expected kernel or module code regions are present.", "type": "boolean"}, "unexpectedKernelCodePages": {"description": "True if kernel code pages that are not in the expected kernel or module code regions are present.", "type": "boolean"}, "unexpectedKprobeHandler": {"description": "True if `kprobe` points are present with callbacks pointing to regions that are not in the expected kernel or module code range.", "type": "boolean"}, "unexpectedProcessesInRunqueue": {"description": "True if unexpected processes in the scheduler run queue are present. Such processes are in the run queue, but not in the process task list.", "type": "boolean"}, "unexpectedReadOnlyDataModification": {"description": "True if unexpected modifications of kernel read-only data memory are present.", "type": "boolean"}, "unexpectedSystemCallHandler": {"description": "True if system call handlers that are are not in the expected kernel or module code regions are present.", "type": "boolean"}}, "type": "object"}, "Kubernetes": {"description": "Kubernetes-related attributes.", "id": "Kubernetes", "properties": {"accessReviews": {"description": "Provides information on any Kubernetes access reviews (privilege checks) relevant to the finding.", "items": {"$ref": "AccessReview"}, "type": "array"}, "bindings": {"description": "Provides Kubernetes role binding information for findings that involve [RoleBindings or ClusterRoleBindings](https://cloud.google.com/kubernetes-engine/docs/how-to/role-based-access-control).", "items": {"$ref": "GoogleCloudSecuritycenterV1Binding"}, "type": "array"}, "nodePools": {"description": "GKE [node pools](https://cloud.google.com/kubernetes-engine/docs/concepts/node-pools) associated with the finding. This field contains node pool information for each node, when it is available.", "items": {"$ref": "NodePool"}, "type": "array"}, "nodes": {"description": "Provides Kubernetes [node](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture#nodes) information.", "items": {"$ref": "Node"}, "type": "array"}, "objects": {"description": "Kubernetes objects related to the finding.", "items": {"$ref": "Object"}, "type": "array"}, "pods": {"description": "Kubernetes [Pods](https://cloud.google.com/kubernetes-engine/docs/concepts/pod) associated with the finding. This field contains Pod records for each container that is owned by a Pod.", "items": {"$ref": "Pod"}, "type": "array"}, "roles": {"description": "Provides Kubernetes role information for findings that involve [Roles or ClusterRoles](https://cloud.google.com/kubernetes-engine/docs/how-to/role-based-access-control).", "items": {"$ref": "Role"}, "type": "array"}}, "type": "object"}, "Label": {"description": "Represents a generic name-value label. A label has separate name and value fields to support filtering with the `contains()` function. For more information, see [Filtering on array-type fields](https://cloud.google.com/security-command-center/docs/how-to-api-list-findings#array-contains-filtering).", "id": "Label", "properties": {"name": {"description": "Name of the label.", "type": "string"}, "value": {"description": "Value that corresponds to the label's name.", "type": "string"}}, "type": "object"}, "ListAssetsResponse": {"description": "Response message for listing assets.", "id": "ListAssetsResponse", "properties": {"listAssetsResults": {"description": "Assets matching the list request.", "items": {"$ref": "ListAssetsResult"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results.", "type": "string"}, "readTime": {"description": "Time used for executing the list request.", "format": "google-datetime", "type": "string"}, "totalSize": {"description": "The total number of assets matching the query.", "format": "int32", "type": "integer"}}, "type": "object"}, "ListAssetsResult": {"description": "Result containing the Asset and its State.", "id": "ListAssetsResult", "properties": {"asset": {"$ref": "<PERSON><PERSON>", "description": "Asset matching the search request."}, "state": {"description": "State of the asset.", "enum": ["STATE_UNSPECIFIED", "UNUSED", "ADDED", "REMOVED", "ACTIVE"], "enumDescriptions": ["Unspecified state.", "Request did not specify use of this field in the result.", "Asset was added between the points in time.", "Asset was removed between the points in time.", "<PERSON><PERSON> was active at both point(s) in time."], "type": "string"}}, "type": "object"}, "ListFindingsResponse": {"description": "Response message for listing findings.", "id": "ListFindingsResponse", "properties": {"findings": {"description": "Findings matching the list request.", "items": {"$ref": "GoogleCloudSecuritycenterV1beta1Finding"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results.", "type": "string"}, "readTime": {"description": "Time used for executing the list request.", "format": "google-datetime", "type": "string"}, "totalSize": {"description": "The total number of findings matching the query.", "format": "int32", "type": "integer"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListSourcesResponse": {"description": "Response message for listing sources.", "id": "ListSourcesResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results.", "type": "string"}, "sources": {"description": "Sources belonging to the requested parent.", "items": {"$ref": "Source"}, "type": "array"}}, "type": "object"}, "LoadBalancer": {"description": "Contains information related to the load balancer associated with the finding.", "id": "LoadBalancer", "properties": {"name": {"description": "The name of the load balancer associated with the finding.", "type": "string"}}, "type": "object"}, "LogEntry": {"description": "An individual entry in a log.", "id": "LogEntry", "properties": {"cloudLoggingEntry": {"$ref": "CloudLoggingEntry", "description": "An individual entry in a log stored in Cloud Logging."}}, "type": "object"}, "MemoryHashSignature": {"description": "A signature corresponding to memory page hashes.", "id": "MemoryHashSignature", "properties": {"binaryFamily": {"description": "The binary family.", "type": "string"}, "detections": {"description": "The list of memory hash detections contributing to the binary family match.", "items": {"$ref": "Detection"}, "type": "array"}}, "type": "object"}, "MitreAttack": {"description": "MITRE ATT&CK tactics and techniques related to this finding. See: https://attack.mitre.org", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"additionalTactics": {"description": "Additional MITRE ATT&CK tactics related to this finding, if any.", "items": {"enum": ["TACTIC_UNSPECIFIED", "RECONNAISSANCE", "RESOURCE_DEVELOPMENT", "INITIAL_ACCESS", "EXECUTION", "PERSISTENCE", "PRIVILEGE_ESCALATION", "DEFENSE_EVASION", "CREDENTIAL_ACCESS", "DISCOVERY", "LATERAL_MOVEMENT", "COLLECTION", "COMMAND_AND_CONTROL", "EXFILTRATION", "IMPACT"], "enumDescriptions": ["Unspecified value.", "TA0043", "TA0042", "TA0001", "TA0002", "TA0003", "TA0004", "TA0005", "TA0006", "TA0007", "TA0008", "TA0009", "TA0011", "TA0010", "TA0040"], "type": "string"}, "type": "array"}, "additionalTechniques": {"description": "Additional MITRE ATT&CK techniques related to this finding, if any, along with any of their respective parent techniques.", "items": {"enum": ["TECHNIQUE_UNSPECIFIED", "DATA_OBFUSCATION", "DATA_OBFUSCATION_STEGANOGRAPHY", "AUTOMATED_EXFILTRATION", "OBFUSCATED_FILES_OR_INFO", "STEGANOGRAPHY", "COMPILE_AFTER_DELIVERY", "COMMAND_OBFUSCATION", "MASQUERADING", "MATCH_LEGITIMATE_NAME_OR_LOCATION", "BOOT_OR_LOGON_INITIALIZATION_SCRIPTS", "STARTUP_ITEMS", "NETWORK_SERVICE_DISCOVERY", "SCHEDULED_TASK_JOB", "CONTAINER_ORCHESTRATION_JOB", "PROCESS_INJECTION", "PROCESS_DISCOVERY", "COMMAND_AND_SCRIPTING_INTERPRETER", "UNIX_SHELL", "PYTHON", "EXPLOITATION_FOR_PRIVILEGE_ESCALATION", "PERMISSION_GROUPS_DISCOVERY", "CLOUD_GROUPS", "INDICATOR_REMOVAL_FILE_DELETION", "APPLICATION_LAYER_PROTOCOL", "DNS", "SOFTWARE_DEPLOYMENT_TOOLS", "VALID_ACCOUNTS", "DEFAULT_ACCOUNTS", "LOCAL_ACCOUNTS", "CLOUD_ACCOUNTS", "PROXY", "EXTERNAL_PROXY", "MULTI_HOP_PROXY", "ACCOUNT_MANIPULATION", "ADDITIONAL_CLOUD_CREDENTIALS", "ADDITIONAL_CLOUD_ROLES", "SSH_AUTHORIZED_KEYS", "ADDITIONAL_CONTAINER_CLUSTER_ROLES", "MULTI_STAGE_CHANNELS", "INGRESS_TOOL_TRANSFER", "NATIVE_API", "BRUTE_FORCE", "AUTOMATED_COLLECTION", "SHARED_MODULES", "DATA_ENCODING", "STANDARD_ENCODING", "ACCESS_TOKEN_MANIPULATION", "TOKEN_IMPERSONATION_OR_THEFT", "CREATE_ACCOUNT", "LOCAL_ACCOUNT", "DEOBFUSCATE_DECODE_FILES_OR_INFO", "EXPLOIT_PUBLIC_FACING_APPLICATION", "USER_EXECUTION", "DOMAIN_POLICY_MODIFICATION", "DATA_DESTRUCTION", "SERVICE_STOP", "INHIBIT_SYSTEM_RECOVERY", "FIRMWARE_CORRUPTION", "RESOURCE_HIJACKING", "NETWORK_DENIAL_OF_SERVICE", "CLOUD_SERVICE_DISCOVERY", "STEAL_APPLICATION_ACCESS_TOKEN", "ACCOUNT_ACCESS_REMOVAL", "TRANSFER_DATA_TO_CLOUD_ACCOUNT", "STEAL_WEB_SESSION_COOKIE", "CREATE_OR_MODIFY_SYSTEM_PROCESS", "EVENT_TRIGGERED_EXECUTION", "BOOT_OR_LOGON_AUTOSTART_EXECUTION", "KERNEL_MODULES_AND_EXTENSIONS", "ABUSE_ELEVATION_CONTROL_MECHANISM", "UNSECURED_CREDENTIALS", "BASH_HISTORY", "PRIVATE_KEYS", "COMPROMISE_HOST_SOFTWARE_BINARY", "CREDENTIALS_FROM_PASSWORD_STORES", "MODIFY_AUTHENTICATION_PROCESS", "IMPAIR_DEFENSES", "DISABLE_OR_MODIFY_TOOLS", "HIDE_ARTIFACTS", "HIDDEN_FILES_AND_DIRECTORIES", "HIDDEN_USERS", "EXFILTRATION_OVER_WEB_SERVICE", "EXFILTRATION_TO_CLOUD_STORAGE", "DYNAMIC_RESOLUTION", "LATERAL_TOOL_TRANSFER", "MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE", "CREATE_SNAPSHOT", "CLOUD_INFRASTRUCTURE_DISCOVERY", "DEVELOP_CAPABILITIES", "DEVELOP_CAPABILITIES_MALWARE", "OBTAIN_CAPABILITIES", "OBTAIN_CAPABILITIES_MALWARE", "ACTIVE_SCANNING", "SCANNING_IP_BLOCKS", "STAGE_CAPABILITIES", "CONTAINER_ADMINISTRATION_COMMAND", "DEPLOY_CONTAINER", "ESCAPE_TO_HOST", "CONTAINER_AND_RESOURCE_DISCOVERY", "REFLECTIVE_CODE_LOADING", "STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES"], "enumDescriptions": ["Unspecified value.", "T1001", "T1001.002", "T1020", "T1027", "T1027.003", "T1027.004", "T1027.010", "T1036", "T1036.005", "T1037", "T1037.005", "T1046", "T1053", "T1053.007", "T1055", "T1057", "T1059", "T1059.004", "T1059.006", "T1068", "T1069", "T1069.003", "T1070.004", "T1071", "T1071.004", "T1072", "T1078", "T1078.001", "T1078.003", "T1078.004", "T1090", "T1090.002", "T1090.003", "T1098", "T1098.001", "T1098.003", "T1098.004", "T1098.006", "T1104", "T1105", "T1106", "T1110", "T1119", "T1129", "T1132", "T1132.001", "T1134", "T1134.001", "T1136", "T1136.001", "T1140", "T1190", "T1204", "T1484", "T1485", "T1489", "T1490", "T1495", "T1496", "T1498", "T1526", "T1528", "T1531", "T1537", "T1539", "T1543", "T1546", "T1547", "T1547.006", "T1548", "T1552", "T1552.003", "T1552.004", "T1554", "T1555", "T1556", "T1562", "T1562.001", "T1564", "T1564.001", "T1564.002", "T1567", "T1567.002", "T1568", "T1570", "T1578", "T1578.001", "T1580", "T1587", "T1587.001", "T1588", "T1588.001", "T1595", "T1595.001", "T1608", "T1609", "T1610", "T1611", "T1613", "T1620", "T1649"], "type": "string"}, "type": "array"}, "primaryTactic": {"description": "The MITRE ATT&CK tactic most closely represented by this finding, if any.", "enum": ["TACTIC_UNSPECIFIED", "RECONNAISSANCE", "RESOURCE_DEVELOPMENT", "INITIAL_ACCESS", "EXECUTION", "PERSISTENCE", "PRIVILEGE_ESCALATION", "DEFENSE_EVASION", "CREDENTIAL_ACCESS", "DISCOVERY", "LATERAL_MOVEMENT", "COLLECTION", "COMMAND_AND_CONTROL", "EXFILTRATION", "IMPACT"], "enumDescriptions": ["Unspecified value.", "TA0043", "TA0042", "TA0001", "TA0002", "TA0003", "TA0004", "TA0005", "TA0006", "TA0007", "TA0008", "TA0009", "TA0011", "TA0010", "TA0040"], "type": "string"}, "primaryTechniques": {"description": "The MITRE ATT&CK technique most closely represented by this finding, if any. primary_techniques is a repeated field because there are multiple levels of MITRE ATT&CK techniques. If the technique most closely represented by this finding is a sub-technique (e.g. `SCANNING_IP_BLOCKS`), both the sub-technique and its parent technique(s) will be listed (e.g. `SCANNING_IP_BLOCKS`, `ACTIVE_SCANNING`).", "items": {"enum": ["TECHNIQUE_UNSPECIFIED", "DATA_OBFUSCATION", "DATA_OBFUSCATION_STEGANOGRAPHY", "AUTOMATED_EXFILTRATION", "OBFUSCATED_FILES_OR_INFO", "STEGANOGRAPHY", "COMPILE_AFTER_DELIVERY", "COMMAND_OBFUSCATION", "MASQUERADING", "MATCH_LEGITIMATE_NAME_OR_LOCATION", "BOOT_OR_LOGON_INITIALIZATION_SCRIPTS", "STARTUP_ITEMS", "NETWORK_SERVICE_DISCOVERY", "SCHEDULED_TASK_JOB", "CONTAINER_ORCHESTRATION_JOB", "PROCESS_INJECTION", "PROCESS_DISCOVERY", "COMMAND_AND_SCRIPTING_INTERPRETER", "UNIX_SHELL", "PYTHON", "EXPLOITATION_FOR_PRIVILEGE_ESCALATION", "PERMISSION_GROUPS_DISCOVERY", "CLOUD_GROUPS", "INDICATOR_REMOVAL_FILE_DELETION", "APPLICATION_LAYER_PROTOCOL", "DNS", "SOFTWARE_DEPLOYMENT_TOOLS", "VALID_ACCOUNTS", "DEFAULT_ACCOUNTS", "LOCAL_ACCOUNTS", "CLOUD_ACCOUNTS", "PROXY", "EXTERNAL_PROXY", "MULTI_HOP_PROXY", "ACCOUNT_MANIPULATION", "ADDITIONAL_CLOUD_CREDENTIALS", "ADDITIONAL_CLOUD_ROLES", "SSH_AUTHORIZED_KEYS", "ADDITIONAL_CONTAINER_CLUSTER_ROLES", "MULTI_STAGE_CHANNELS", "INGRESS_TOOL_TRANSFER", "NATIVE_API", "BRUTE_FORCE", "AUTOMATED_COLLECTION", "SHARED_MODULES", "DATA_ENCODING", "STANDARD_ENCODING", "ACCESS_TOKEN_MANIPULATION", "TOKEN_IMPERSONATION_OR_THEFT", "CREATE_ACCOUNT", "LOCAL_ACCOUNT", "DEOBFUSCATE_DECODE_FILES_OR_INFO", "EXPLOIT_PUBLIC_FACING_APPLICATION", "USER_EXECUTION", "DOMAIN_POLICY_MODIFICATION", "DATA_DESTRUCTION", "SERVICE_STOP", "INHIBIT_SYSTEM_RECOVERY", "FIRMWARE_CORRUPTION", "RESOURCE_HIJACKING", "NETWORK_DENIAL_OF_SERVICE", "CLOUD_SERVICE_DISCOVERY", "STEAL_APPLICATION_ACCESS_TOKEN", "ACCOUNT_ACCESS_REMOVAL", "TRANSFER_DATA_TO_CLOUD_ACCOUNT", "STEAL_WEB_SESSION_COOKIE", "CREATE_OR_MODIFY_SYSTEM_PROCESS", "EVENT_TRIGGERED_EXECUTION", "BOOT_OR_LOGON_AUTOSTART_EXECUTION", "KERNEL_MODULES_AND_EXTENSIONS", "ABUSE_ELEVATION_CONTROL_MECHANISM", "UNSECURED_CREDENTIALS", "BASH_HISTORY", "PRIVATE_KEYS", "COMPROMISE_HOST_SOFTWARE_BINARY", "CREDENTIALS_FROM_PASSWORD_STORES", "MODIFY_AUTHENTICATION_PROCESS", "IMPAIR_DEFENSES", "DISABLE_OR_MODIFY_TOOLS", "HIDE_ARTIFACTS", "HIDDEN_FILES_AND_DIRECTORIES", "HIDDEN_USERS", "EXFILTRATION_OVER_WEB_SERVICE", "EXFILTRATION_TO_CLOUD_STORAGE", "DYNAMIC_RESOLUTION", "LATERAL_TOOL_TRANSFER", "MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE", "CREATE_SNAPSHOT", "CLOUD_INFRASTRUCTURE_DISCOVERY", "DEVELOP_CAPABILITIES", "DEVELOP_CAPABILITIES_MALWARE", "OBTAIN_CAPABILITIES", "OBTAIN_CAPABILITIES_MALWARE", "ACTIVE_SCANNING", "SCANNING_IP_BLOCKS", "STAGE_CAPABILITIES", "CONTAINER_ADMINISTRATION_COMMAND", "DEPLOY_CONTAINER", "ESCAPE_TO_HOST", "CONTAINER_AND_RESOURCE_DISCOVERY", "REFLECTIVE_CODE_LOADING", "STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES"], "enumDescriptions": ["Unspecified value.", "T1001", "T1001.002", "T1020", "T1027", "T1027.003", "T1027.004", "T1027.010", "T1036", "T1036.005", "T1037", "T1037.005", "T1046", "T1053", "T1053.007", "T1055", "T1057", "T1059", "T1059.004", "T1059.006", "T1068", "T1069", "T1069.003", "T1070.004", "T1071", "T1071.004", "T1072", "T1078", "T1078.001", "T1078.003", "T1078.004", "T1090", "T1090.002", "T1090.003", "T1098", "T1098.001", "T1098.003", "T1098.004", "T1098.006", "T1104", "T1105", "T1106", "T1110", "T1119", "T1129", "T1132", "T1132.001", "T1134", "T1134.001", "T1136", "T1136.001", "T1140", "T1190", "T1204", "T1484", "T1485", "T1489", "T1490", "T1495", "T1496", "T1498", "T1526", "T1528", "T1531", "T1537", "T1539", "T1543", "T1546", "T1547", "T1547.006", "T1548", "T1552", "T1552.003", "T1552.004", "T1554", "T1555", "T1556", "T1562", "T1562.001", "T1564", "T1564.001", "T1564.002", "T1567", "T1567.002", "T1568", "T1570", "T1578", "T1578.001", "T1580", "T1587", "T1587.001", "T1588", "T1588.001", "T1595", "T1595.001", "T1608", "T1609", "T1610", "T1611", "T1613", "T1620", "T1649"], "type": "string"}, "type": "array"}, "version": {"description": "The MITRE ATT&CK version referenced by the above fields. E.g. \"8\".", "type": "string"}}, "type": "object"}, "MuteInfo": {"description": "Mute information about the finding, including whether the finding has a static mute or any matching dynamic mute rules.", "id": "MuteInfo", "properties": {"dynamicMuteRecords": {"description": "The list of dynamic mute rules that currently match the finding.", "items": {"$ref": "DynamicMuteRecord"}, "type": "array"}, "staticMute": {"$ref": "StaticMute", "description": "If set, the static mute applied to this finding. Static mutes override dynamic mutes. If unset, there is no static mute."}}, "type": "object"}, "Network": {"description": "Contains information about a VPC network associated with the finding.", "id": "Network", "properties": {"name": {"description": "The name of the VPC network resource, for example, `//compute.googleapis.com/projects/my-project/global/networks/my-network`.", "type": "string"}}, "type": "object"}, "Node": {"description": "Kubernetes nodes associated with the finding.", "id": "Node", "properties": {"name": {"description": "[Full resource name](https://google.aip.dev/122#full-resource-names) of the Compute Engine VM running the cluster node.", "type": "string"}}, "type": "object"}, "NodePool": {"description": "Provides GKE node pool information.", "id": "NodePool", "properties": {"name": {"description": "Kubernetes node pool name.", "type": "string"}, "nodes": {"description": "Nodes associated with the finding.", "items": {"$ref": "Node"}, "type": "array"}}, "type": "object"}, "Notebook": {"description": "Represents a Jupyter notebook IPYNB file, such as a [Colab Enterprise notebook](https://cloud.google.com/colab/docs/introduction) file, that is associated with a finding.", "id": "Notebook", "properties": {"lastAuthor": {"description": "The user ID of the latest author to modify the notebook.", "type": "string"}, "name": {"description": "The name of the notebook.", "type": "string"}, "notebookUpdateTime": {"description": "The most recent time the notebook was updated.", "format": "google-datetime", "type": "string"}, "service": {"description": "The source notebook service, for example, \"Colab Enterprise\".", "type": "string"}}, "type": "object"}, "Object": {"description": "Kubernetes object related to the finding, uniquely identified by GKNN. Used if the object Kind is not one of Pod, Node, NodePool, Binding, or AccessReview.", "id": "Object", "properties": {"containers": {"description": "Pod containers associated with this finding, if any.", "items": {"$ref": "Container"}, "type": "array"}, "group": {"description": "Kubernetes object group, such as \"policy.k8s.io/v1\".", "type": "string"}, "kind": {"description": "Kubernetes object kind, such as \"Namespace\".", "type": "string"}, "name": {"description": "Kubernetes object name. For details see https://kubernetes.io/docs/concepts/overview/working-with-objects/names/.", "type": "string"}, "ns": {"description": "Kubernetes object namespace. Must be a valid DNS label. Named \"ns\" to avoid collision with C++ namespace keyword. For details see https://kubernetes.io/docs/tasks/administer-cluster/namespaces/.", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OrgPolicy": {"description": "Contains information about the org policies associated with the finding.", "id": "OrgPolicy", "properties": {"name": {"description": "The resource name of the org policy. Example: \"organizations/{organization_id}/policies/{constraint_name}\"", "type": "string"}}, "type": "object"}, "OrganizationSettings": {"description": "User specified settings that are attached to the Security Command Center organization.", "id": "OrganizationSettings", "properties": {"assetDiscoveryConfig": {"$ref": "AssetDiscoveryConfig", "description": "The configuration used for Asset Discovery runs."}, "enableAssetDiscovery": {"description": "A flag that indicates if Asset Discovery should be enabled. If the flag is set to `true`, then discovery of assets will occur. If it is set to `false`, all historical assets will remain, but discovery of future assets will not occur.", "type": "boolean"}, "name": {"description": "The relative resource name of the settings. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/organizationSettings\".", "type": "string"}}, "type": "object"}, "Package": {"description": "Package is a generic definition of a package.", "id": "Package", "properties": {"cpeUri": {"description": "The CPE URI where the vulnerability was detected.", "type": "string"}, "packageName": {"description": "The name of the package where the vulnerability was detected.", "type": "string"}, "packageType": {"description": "Type of package, for example, os, maven, or go.", "type": "string"}, "packageVersion": {"description": "The version of the package.", "type": "string"}}, "type": "object"}, "Pod": {"description": "A Kubernetes Pod.", "id": "Pod", "properties": {"containers": {"description": "Pod containers associated with this finding, if any.", "items": {"$ref": "Container"}, "type": "array"}, "labels": {"description": "Pod labels. For Kubernetes containers, these are applied to the container.", "items": {"$ref": "Label"}, "type": "array"}, "name": {"description": "Kubernetes Pod name.", "type": "string"}, "ns": {"description": "Kubernetes Pod namespace.", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PolicyDriftDetails": {"description": "The policy field that violates the deployed posture and its expected and detected values.", "id": "PolicyDriftDetails", "properties": {"detectedValue": {"description": "The detected value that violates the deployed posture, for example, `false` or `allowed_values={\"projects/22831892\"}`.", "type": "string"}, "expectedValue": {"description": "The value of this field that was configured in a posture, for example, `true` or `allowed_values={\"projects/29831892\"}`.", "type": "string"}, "field": {"description": "The name of the updated field, for example constraint.implementation.policy_rules[0].enforce", "type": "string"}}, "type": "object"}, "PortRange": {"description": "A port range which is inclusive of the min and max values. Values are between 0 and 2^16-1. The max can be equal / must be not smaller than the min value. If min and max are equal this indicates that it is a single port.", "id": "PortRange", "properties": {"max": {"description": "Maximum port value.", "format": "int64", "type": "string"}, "min": {"description": "Minimum port value.", "format": "int64", "type": "string"}}, "type": "object"}, "Process": {"description": "Represents an operating system process.", "id": "Process", "properties": {"args": {"description": "Process arguments as JSON encoded strings.", "items": {"type": "string"}, "type": "array"}, "argumentsTruncated": {"description": "True if `args` is incomplete.", "type": "boolean"}, "binary": {"$ref": "File", "description": "File information for the process executable."}, "envVariables": {"description": "Process environment variables.", "items": {"$ref": "EnvironmentVariable"}, "type": "array"}, "envVariablesTruncated": {"description": "True if `env_variables` is incomplete.", "type": "boolean"}, "libraries": {"description": "File information for libraries loaded by the process.", "items": {"$ref": "File"}, "type": "array"}, "name": {"description": "The process name, as displayed in utilities like `top` and `ps`. This name can be accessed through `/proc/[pid]/comm` and changed with `prctl(PR_SET_NAME)`.", "type": "string"}, "parentPid": {"description": "The parent process ID.", "format": "int64", "type": "string"}, "pid": {"description": "The process ID.", "format": "int64", "type": "string"}, "script": {"$ref": "File", "description": "When the process represents the invocation of a script, `binary` provides information about the interpreter, while `script` provides information about the script file provided to the interpreter."}}, "type": "object"}, "ProcessSignature": {"description": "Indicates what signature matched this process.", "id": "ProcessSignature", "properties": {"memoryHashSignature": {"$ref": "MemoryHashSignature", "description": "Signature indicating that a binary family was matched."}, "signatureType": {"description": "Describes the type of resource associated with the signature.", "enum": ["SIGNATURE_TYPE_UNSPECIFIED", "SIGNATURE_TYPE_PROCESS", "SIGNATURE_TYPE_FILE"], "enumDescriptions": ["The default signature type.", "Used for signatures concerning processes.", "Used for signatures concerning disks."], "type": "string"}, "yaraRuleSignature": {"$ref": "YaraRuleSignature", "description": "Signature indicating that a YARA rule was matched."}}, "type": "object"}, "Reference": {"description": "Additional Links", "id": "Reference", "properties": {"source": {"description": "Source of the reference e.g. NVD", "type": "string"}, "uri": {"description": "Uri for the mentioned source e.g. https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527.", "type": "string"}}, "type": "object"}, "Requests": {"description": "Information about the requests relevant to the finding.", "id": "Requests", "properties": {"longTermAllowed": {"description": "Allowed RPS (requests per second) over the long term.", "format": "int32", "type": "integer"}, "longTermDenied": {"description": "Denied RPS (requests per second) over the long term.", "format": "int32", "type": "integer"}, "ratio": {"description": "For 'Increasing deny ratio', the ratio is the denied traffic divided by the allowed traffic. For 'Allowed traffic spike', the ratio is the allowed traffic in the short term divided by allowed traffic in the long term.", "format": "double", "type": "number"}, "shortTermAllowed": {"description": "Allowed RPS (requests per second) in the short term.", "format": "int32", "type": "integer"}}, "type": "object"}, "ResourcePath": {"description": "Represents the path of resources leading up to the resource this finding is about.", "id": "ResourcePath", "properties": {"nodes": {"description": "The list of nodes that make the up resource path, ordered from lowest level to highest level.", "items": {"$ref": "ResourcePathNode"}, "type": "array"}}, "type": "object"}, "ResourcePathNode": {"description": "A node within the resource path. Each node represents a resource within the resource hierarchy.", "id": "ResourcePathNode", "properties": {"displayName": {"description": "The display name of the resource this node represents.", "type": "string"}, "id": {"description": "The ID of the resource this node represents.", "type": "string"}, "nodeType": {"description": "The type of resource this node represents.", "enum": ["RESOURCE_PATH_NODE_TYPE_UNSPECIFIED", "GCP_ORGANIZATION", "GCP_FOLDER", "GCP_PROJECT", "AWS_ORGANIZATION", "AWS_ORGANIZATIONAL_UNIT", "AWS_ACCOUNT", "AZURE_MANAGEMENT_GROUP", "AZURE_SUBSCRIPTION", "AZURE_RESOURCE_GROUP"], "enumDescriptions": ["Node type is unspecified.", "The node represents a Google Cloud organization.", "The node represents a Google Cloud folder.", "The node represents a Google Cloud project.", "The node represents an AWS organization.", "The node represents an AWS organizational unit.", "The node represents an AWS account.", "The node represents an Azure management group.", "The node represents an Azure subscription.", "The node represents an Azure resource group."], "type": "string"}}, "type": "object"}, "Role": {"description": "Kubernetes Role or ClusterRole.", "id": "Role", "properties": {"kind": {"description": "Role type.", "enum": ["KIND_UNSPECIFIED", "ROLE", "CLUSTER_ROLE"], "enumDescriptions": ["Role type is not specified.", "Kubernetes Role.", "Kubernetes ClusterRole."], "type": "string"}, "name": {"description": "Role name.", "type": "string"}, "ns": {"description": "Role namespace.", "type": "string"}}, "type": "object"}, "RunAssetDiscoveryRequest": {"description": "Request message for running asset discovery for an organization.", "id": "RunAssetDiscoveryRequest", "properties": {}, "type": "object"}, "SecurityBulletin": {"description": "SecurityBulletin are notifications of vulnerabilities of Google products.", "id": "SecurityBulletin", "properties": {"bulletinId": {"description": "ID of the bulletin corresponding to the vulnerability.", "type": "string"}, "submissionTime": {"description": "Submission time of this Security Bulletin.", "format": "google-datetime", "type": "string"}, "suggestedUpgradeVersion": {"description": "This represents a version that the cluster receiving this notification should be upgraded to, based on its current version. For example, 1.15.0", "type": "string"}}, "type": "object"}, "SecurityCenterProperties": {"description": "Security Command Center managed properties. These properties are managed by Security Command Center and cannot be modified by the user.", "id": "SecurityCenterProperties", "properties": {"resourceName": {"description": "Immutable. The full resource name of the Google Cloud resource this asset represents. This field is immutable after create time. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourceOwners": {"description": "Owners of the Google Cloud resource.", "items": {"type": "string"}, "type": "array"}, "resourceParent": {"description": "The full resource name of the immediate parent of the resource. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourceProject": {"description": "The full resource name of the project the resource belongs to. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourceType": {"description": "The type of the Google Cloud resource. Examples include: APPLICATION, PROJECT, and ORGANIZATION. This is a case insensitive field defined by Security Command Center and/or the producer of the resource and is immutable after create time.", "type": "string"}}, "type": "object"}, "SecurityMarks": {"description": "User specified security marks that are attached to the parent Security Command Center resource. Security marks are scoped within a Security Command Center organization -- they can be modified and viewed by all users who have proper permissions on the organization.", "id": "SecurityMarks", "properties": {"canonicalName": {"description": "The canonical name of the marks. Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"folders/{folder_id}/assets/{asset_id}/securityMarks\" \"projects/{project_number}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\" \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}/securityMarks\" \"projects/{project_number}/sources/{source_id}/findings/{finding_id}/securityMarks\"", "type": "string"}, "marks": {"additionalProperties": {"type": "string"}, "description": "Mutable user specified security marks belonging to the parent resource. Constraints are as follows: * Keys and values are treated as case insensitive * Keys must be between 1 - 256 characters (inclusive) * Keys must be letters, numbers, underscores, or dashes * Values have leading and trailing whitespace trimmed, remaining characters must be between 1 - 4096 characters (inclusive)", "type": "object"}, "name": {"description": "The relative resource name of the SecurityMarks. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\".", "type": "string"}}, "type": "object"}, "SecurityPolicy": {"description": "Information about the [Google Cloud Armor security policy](https://cloud.google.com/armor/docs/security-policy-overview) relevant to the finding.", "id": "SecurityPolicy", "properties": {"name": {"description": "The name of the Google Cloud Armor security policy, for example, \"my-security-policy\".", "type": "string"}, "preview": {"description": "Whether or not the associated rule or policy is in preview mode.", "type": "boolean"}, "type": {"description": "The type of Google Cloud Armor security policy for example, 'backend security policy', 'edge security policy', 'network edge security policy', or 'always-on DDoS protection'.", "type": "string"}}, "type": "object"}, "SecurityPosture": {"description": "Represents a posture that is deployed on Google Cloud by the Security Command Center Posture Management service. A posture contains one or more policy sets. A policy set is a group of policies that enforce a set of security rules on Google Cloud.", "id": "SecurityPosture", "properties": {"changedPolicy": {"description": "The name of the updated policy, for example, `projects/{project_id}/policies/{constraint_name}`.", "type": "string"}, "name": {"description": "Name of the posture, for example, `CIS-Posture`.", "type": "string"}, "policy": {"description": "The ID of the updated policy, for example, `compute-policy-1`.", "type": "string"}, "policyDriftDetails": {"description": "The details about a change in an updated policy that violates the deployed posture.", "items": {"$ref": "PolicyDriftDetails"}, "type": "array"}, "policySet": {"description": "The name of the updated policyset, for example, `cis-policyset`.", "type": "string"}, "postureDeployment": {"description": "The name of the posture deployment, for example, `organizations/{org_id}/posturedeployments/{posture_deployment_id}`.", "type": "string"}, "postureDeploymentResource": {"description": "The project, folder, or organization on which the posture is deployed, for example, `projects/{project_number}`.", "type": "string"}, "revisionId": {"description": "The version of the posture, for example, `c7cfa2a8`.", "type": "string"}}, "type": "object"}, "ServiceAccountDelegationInfo": {"description": "Identity delegation history of an authenticated service account.", "id": "ServiceAccountDelegationInfo", "properties": {"principalEmail": {"description": "The email address of a Google account.", "type": "string"}, "principalSubject": {"description": "A string representing the principal_subject associated with the identity. As compared to `principal_email`, supports principals that aren't associated with email addresses, such as third party principals. For most identities, the format will be `principal://iam.googleapis.com/{identity pool name}/subjects/{subject}` except for some GKE identities (GKE_WORKLOAD, FREEFORM, GKE_HUB_WORKLOAD) that are still in the legacy format `serviceAccount:{identity pool name}[{subject}]`", "type": "string"}}, "type": "object"}, "SetFindingStateRequest": {"description": "Request message for updating a finding's state.", "id": "SetFindingStateRequest", "properties": {"startTime": {"description": "Optional. The time at which the updated state takes effect. If not set uses the current time.", "format": "google-datetime", "type": "string"}, "state": {"description": "Required. The desired State of the finding.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "The finding requires attention and has not been addressed yet.", "The finding has been fixed, triaged as a non-issue or otherwise addressed and is no longer active."], "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "Source": {"description": "Security Command Center finding source. A finding source is an entity or a mechanism that can produce a finding. A source is like a container of findings that come from the same scanner, logger, monitor, etc.", "id": "Source", "properties": {"description": {"description": "The description of the source (max of 1024 characters). Example: \"Web Security Scanner is a web security scanner for common vulnerabilities in App Engine applications. It can automatically scan and detect four common vulnerabilities, including cross-site-scripting (XSS), Flash injection, mixed content (HTTP in HTTPS), and outdated/insecure libraries.\"", "type": "string"}, "displayName": {"description": "The source's display name. A source's display name must be unique amongst its siblings, for example, two sources with the same parent can't share the same display name. The display name must have a length between 1 and 64 characters (inclusive).", "type": "string"}, "name": {"description": "The relative resource name of this source. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/sources/{source_id}\"", "type": "string"}}, "type": "object"}, "StaticMute": {"description": "Information about the static mute state. A static mute state overrides any dynamic mute rules that apply to this finding. The static mute state can be set by a static mute rule or by muting the finding directly.", "id": "StaticMute", "properties": {"applyTime": {"description": "When the static mute was applied.", "format": "google-datetime", "type": "string"}, "state": {"description": "The static mute state. If the value is `MUTED` or `UNMUTED`, then the finding's overall mute state will have the same value.", "enum": ["MUTE_UNSPECIFIED", "MUTED", "UNMUTED", "UNDEFINED"], "enumDescriptions": ["Unspecified.", "Finding has been muted.", "Finding has been unmuted.", "Finding has never been muted/unmuted."], "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "Subject": {"description": "Represents a Kubernetes subject.", "id": "Subject", "properties": {"kind": {"description": "Authentication type for the subject.", "enum": ["AUTH_TYPE_UNSPECIFIED", "USER", "SERVICEACCOUNT", "GROUP"], "enumDescriptions": ["Authentication is not specified.", "User with valid certificate.", "Users managed by Kubernetes API with credentials stored as secrets.", "Collection of users."], "type": "string"}, "name": {"description": "Name for the subject.", "type": "string"}, "ns": {"description": "Namespace for the subject.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TicketInfo": {"description": "Information about the ticket, if any, that is being used to track the resolution of the issue that is identified by this finding.", "id": "TicketInfo", "properties": {"assignee": {"description": "The assignee of the ticket in the ticket system.", "type": "string"}, "description": {"description": "The description of the ticket in the ticket system.", "type": "string"}, "id": {"description": "The identifier of the ticket in the ticket system.", "type": "string"}, "status": {"description": "The latest status of the ticket, as reported by the ticket system.", "type": "string"}, "updateTime": {"description": "The time when the ticket was last updated, as reported by the ticket system.", "format": "google-datetime", "type": "string"}, "uri": {"description": "The link to the ticket in the ticket system.", "type": "string"}}, "type": "object"}, "ToxicCombination": {"description": "Contains details about a group of security issues that, when the issues occur together, represent a greater risk than when the issues occur independently. A group of such issues is referred to as a toxic combination.", "id": "ToxicCombination", "properties": {"attackExposureScore": {"description": "The [Attack exposure score](https://cloud.google.com/security-command-center/docs/attack-exposure-learn#attack_exposure_scores) of this toxic combination. The score is a measure of how much this toxic combination exposes one or more high-value resources to potential attack.", "format": "double", "type": "number"}, "relatedFindings": {"description": "List of resource names of findings associated with this toxic combination. For example, `organizations/123/sources/456/findings/789`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Vulnerability": {"description": "Refers to common vulnerability fields e.g. cve, cvss, cwe etc.", "id": "Vulnerability", "properties": {"cve": {"$ref": "Cve", "description": "CVE stands for Common Vulnerabilities and Exposures (https://cve.mitre.org/about/)"}, "cwes": {"description": "Represents one or more Common Weakness Enumeration (CWE) information on this vulnerability.", "items": {"$ref": "Cwe"}, "type": "array"}, "fixedPackage": {"$ref": "Package", "description": "The fixed package is relevant to the finding."}, "offendingPackage": {"$ref": "Package", "description": "The offending package is relevant to the finding."}, "providerRiskScore": {"description": "Provider provided risk_score based on multiple factors. The higher the risk score, the more risky the vulnerability is.", "format": "int64", "type": "string"}, "reachable": {"description": "Represents whether the vulnerability is reachable (detected via static analysis)", "type": "boolean"}, "securityBulletin": {"$ref": "SecurityBulletin", "description": "The security bulletin is relevant to this finding."}}, "type": "object"}, "VulnerabilityCountBySeverity": {"description": "Vulnerability count by severity.", "id": "VulnerabilityCountBySeverity", "properties": {"severityToFindingCount": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Key is the Severity enum.", "type": "object"}}, "type": "object"}, "VulnerabilitySnapshot": {"description": "Result containing the properties and count of a VulnerabilitySnapshot request.", "id": "VulnerabilitySnapshot", "properties": {"cloudProvider": {"description": "The cloud provider for the vulnerability snapshot.", "enum": ["CLOUD_PROVIDER_UNSPECIFIED", "GOOGLE_CLOUD_PLATFORM", "AMAZON_WEB_SERVICES", "MICROSOFT_AZURE"], "enumDescriptions": ["The cloud provider is unspecified.", "The cloud provider is Google Cloud Platform.", "The cloud provider is Amazon Web Services.", "The cloud provider is Microsoft Azure."], "type": "string"}, "findingCount": {"$ref": "VulnerabilityCountBySeverity", "description": "The vulnerability count by severity."}, "name": {"description": "Identifier. The vulnerability snapshot name. Format: //locations//vulnerabilitySnapshots/", "type": "string"}, "snapshotTime": {"description": "The time that the snapshot was taken.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "YaraRuleSignature": {"description": "A signature corresponding to a YARA rule.", "id": "YaraRuleSignature", "properties": {"yaraRule": {"description": "The name of the YARA rule.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Security Command Center API", "version": "v1beta1", "version_module": true}