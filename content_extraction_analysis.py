#!/usr/bin/env python3
"""
Detailed Content Extraction Analysis
Compares successful vs failed content extraction with actual content samples
"""

import sqlite3
import json
import requests
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import pandas as pd
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.syntax import Syntax
from urllib.parse import urlparse
import re
from bs4 import BeautifulSoup
import time

console = Console()

class ContentExtractionAnalyzer:
    """Detailed content extraction analysis"""
    
    def __init__(self, db_path: str = "test_results.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Crawl4AI-ContentAnalyzer/1.0 (Research Purpose)'
        })
    
    def get_successful_tests(self, limit: int = 10) -> List[Dict]:
        """Get successful test cases with high extraction rates"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT test_id, test_name, url, status, execution_time, 
                   extraction_rate, error_message, metadata, timestamp
            FROM test_results 
            WHERE status = 'PASS' AND extraction_rate > 0.8
            ORDER BY extraction_rate DESC
            LIMIT ?
        """, (limit,))
        
        results = []
        for row in cursor.fetchall():
            result = {
                'test_id': row[0],
                'test_name': row[1],
                'url': row[2],
                'status': row[3],
                'execution_time': row[4],
                'extraction_rate': row[5],
                'error_message': row[6],
                'metadata': json.loads(row[7]) if row[7] else {},
                'timestamp': row[8]
            }
            results.append(result)
        
        conn.close()
        return results
    
    def get_failed_tests(self, limit: int = 10) -> List[Dict]:
        """Get failed test cases"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT test_id, test_name, url, status, execution_time, 
                   extraction_rate, error_message, metadata, timestamp
            FROM test_results 
            WHERE status IN ('FAIL', 'ERROR')
            ORDER BY timestamp DESC
            LIMIT ?
        """, (limit,))
        
        results = []
        for row in cursor.fetchall():
            result = {
                'test_id': row[0],
                'test_name': row[1],
                'url': row[2],
                'status': row[3],
                'execution_time': row[4],
                'extraction_rate': row[5],
                'error_message': row[6],
                'metadata': json.loads(row[7]) if row[7] else {},
                'timestamp': row[8]
            }
            results.append(result)
        
        conn.close()
        return results
    
    def extract_detailed_content(self, url: str) -> Dict:
        """Extract detailed content analysis from a URL"""
        try:
            response = self.session.get(url, timeout=10, allow_redirects=True)
            
            analysis = {
                'url': url,
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds(),
                'content_length': len(response.text),
                'headers': dict(response.headers),
                'final_url': response.url,
                'redirected': response.url != url
            }
            
            if response.status_code == 200:
                # Parse with BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Extract text content
                text_content = soup.get_text()
                lines = (line.strip() for line in text_content.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                clean_text = ' '.join(chunk for chunk in chunks if chunk)
                
                analysis.update({
                    'title': soup.title.string.strip() if soup.title and soup.title.string else None,
                    'meta_description': self._get_meta_description(soup),
                    'headings': self._extract_headings(soup),
                    'paragraphs': len(soup.find_all('p')),
                    'links': len(soup.find_all('a', href=True)),
                    'images': len(soup.find_all('img')),
                    'forms': len(soup.find_all('form')),
                    'tables': len(soup.find_all('table')),
                    'lists': len(soup.find_all(['ul', 'ol'])),
                    'clean_text': clean_text[:2000],  # First 2000 chars
                    'text_length': len(clean_text),
                    'word_count': len(clean_text.split()),
                    'language': self._detect_language(soup),
                    'technology_indicators': self._detect_technologies(response.text),
                    'content_quality_score': self._calculate_content_quality(soup, clean_text),
                    'raw_html_sample': response.text[:1000],  # First 1000 chars of HTML
                    'structured_data': self._extract_structured_data(soup)
                })
            
            return analysis
            
        except requests.exceptions.Timeout:
            return {'url': url, 'success': False, 'error': 'Timeout', 'status_code': None}
        except requests.exceptions.ConnectionError:
            return {'url': url, 'success': False, 'error': 'Connection Error', 'status_code': None}
        except Exception as e:
            return {'url': url, 'success': False, 'error': str(e), 'status_code': None}
    
    def _get_meta_description(self, soup):
        """Extract meta description"""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            return meta_desc.get('content', '')
        return None
    
    def _extract_headings(self, soup):
        """Extract heading structure"""
        headings = {}
        for i in range(1, 7):
            headings[f'h{i}'] = len(soup.find_all(f'h{i}'))
        return headings
    
    def _detect_language(self, soup):
        """Detect page language"""
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            return html_tag.get('lang')
        return 'unknown'
    
    def _detect_technologies(self, html_content):
        """Detect web technologies"""
        content_lower = html_content.lower()
        technologies = []
        
        tech_indicators = {
            'React': ['react', 'reactjs', '__react'],
            'Vue.js': ['vue.js', 'vuejs', '__vue'],
            'Angular': ['angular', 'ng-app', 'ng-controller'],
            'jQuery': ['jquery', '$.', 'jquery.min.js'],
            'Bootstrap': ['bootstrap', 'btn-', 'container-fluid'],
            'WordPress': ['wp-content', 'wordpress', 'wp-includes'],
            'Drupal': ['drupal', 'sites/default'],
            'Shopify': ['shopify', 'cdn.shopify.com'],
            'Google Analytics': ['google-analytics', 'gtag', 'ga('],
            'Cloudflare': ['cloudflare', '__cf_bm'],
            'reCAPTCHA': ['recaptcha', 'g-recaptcha']
        }
        
        for tech, indicators in tech_indicators.items():
            if any(indicator in content_lower for indicator in indicators):
                technologies.append(tech)
        
        return technologies
    
    def _calculate_content_quality(self, soup, clean_text):
        """Calculate content quality score (0-100)"""
        score = 0
        
        # Text content quality
        if len(clean_text) > 500:
            score += 20
        elif len(clean_text) > 200:
            score += 10
        
        # Structure quality
        if soup.find('title'):
            score += 10
        if soup.find('meta', attrs={'name': 'description'}):
            score += 10
        if soup.find_all('h1'):
            score += 10
        if len(soup.find_all('p')) > 3:
            score += 15
        if soup.find_all('a', href=True):
            score += 10
        
        # Content diversity
        if soup.find_all('img'):
            score += 5
        if soup.find_all(['ul', 'ol']):
            score += 5
        if soup.find_all('table'):
            score += 5
        
        # Readability
        words = clean_text.split()
        if len(words) > 100:
            avg_word_length = sum(len(word) for word in words) / len(words)
            if 4 <= avg_word_length <= 6:  # Good readability
                score += 10
        
        return min(score, 100)
    
    def _extract_structured_data(self, soup):
        """Extract structured data (JSON-LD, microdata, etc.)"""
        structured_data = []
        
        # JSON-LD
        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        for script in json_ld_scripts:
            try:
                data = json.loads(script.string)
                structured_data.append({'type': 'JSON-LD', 'data': data})
            except:
                pass
        
        # Microdata
        microdata_items = soup.find_all(attrs={'itemscope': True})
        for item in microdata_items[:3]:  # Limit to first 3
            item_type = item.get('itemtype', 'Unknown')
            structured_data.append({'type': 'Microdata', 'itemtype': item_type})
        
        return structured_data
    
    def compare_extraction_results(self):
        """Compare successful vs failed extraction results"""
        console.print(Panel("📊 Content Extraction Comparison Analysis", style="bold blue"))
        
        # Get successful and failed tests
        successful_tests = self.get_successful_tests(5)
        failed_tests = self.get_failed_tests(5)
        
        console.print(f"\n✅ **SUCCESSFUL EXTRACTIONS ANALYSIS**")
        console.print(f"Analyzing {len(successful_tests)} high-performing tests...")
        
        for i, test in enumerate(successful_tests, 1):
            console.print(f"\n--- SUCCESS CASE {i} ---")
            console.print(f"Test ID: {test['test_id']}")
            console.print(f"URL: {test['url']}")
            console.print(f"Extraction Rate: {test['extraction_rate']:.1%}")
            
            # Analyze content
            content_analysis = self.extract_detailed_content(test['url'])
            
            if content_analysis['success']:
                console.print("✅ **Content Analysis Results:**")
                console.print(f"  Title: {content_analysis.get('title', 'None')[:100]}...")
                console.print(f"  Content Quality Score: {content_analysis.get('content_quality_score', 0)}/100")
                console.print(f"  Word Count: {content_analysis.get('word_count', 0)}")
                console.print(f"  Paragraphs: {content_analysis.get('paragraphs', 0)}")
                console.print(f"  Links: {content_analysis.get('links', 0)}")
                console.print(f"  Technologies: {', '.join(content_analysis.get('technology_indicators', []))}")
                
                # Show content sample
                if content_analysis.get('clean_text'):
                    console.print(f"\n📄 **EXTRACTED CONTENT SAMPLE:**")
                    sample_text = content_analysis['clean_text'][:400] + "..."
                    console.print(Panel(sample_text, title="Clean Text Extract", border_style="green"))
            else:
                console.print(f"❌ Content analysis failed: {content_analysis.get('error', 'Unknown')}")
            
            time.sleep(1)  # Rate limiting
        
        console.print(f"\n❌ **FAILED EXTRACTIONS ANALYSIS**")
        console.print(f"Analyzing {len(failed_tests)} failed tests...")
        
        for i, test in enumerate(failed_tests, 1):
            console.print(f"\n--- FAILURE CASE {i} ---")
            console.print(f"Test ID: {test['test_id']}")
            console.print(f"URL: {test['url']}")
            console.print(f"Error: {test['error_message']}")
            
            # Try to analyze content anyway
            content_analysis = self.extract_detailed_content(test['url'])
            
            if content_analysis['success']:
                console.print("⚠️ **Content is actually accessible now:**")
                console.print(f"  Status Code: {content_analysis['status_code']}")
                console.print(f"  Content Quality Score: {content_analysis.get('content_quality_score', 0)}/100")
                console.print(f"  Word Count: {content_analysis.get('word_count', 0)}")
                console.print(f"  Technologies: {', '.join(content_analysis.get('technology_indicators', []))}")
                
                # Show why it might have failed originally
                if content_analysis.get('technology_indicators'):
                    if 'Cloudflare' in content_analysis['technology_indicators']:
                        console.print("  🛡️ **Potential Issue**: Cloudflare protection detected")
                    if 'reCAPTCHA' in content_analysis['technology_indicators']:
                        console.print("  🛡️ **Potential Issue**: reCAPTCHA protection detected")
            else:
                console.print(f"❌ Content still inaccessible: {content_analysis.get('error', 'Unknown')}")
                console.print(f"  Status Code: {content_analysis.get('status_code', 'None')}")
            
            time.sleep(1)  # Rate limiting
        
        # Generate comparison summary
        self.generate_comparison_summary(successful_tests, failed_tests)
    
    def generate_comparison_summary(self, successful_tests: List[Dict], failed_tests: List[Dict]):
        """Generate summary comparison"""
        console.print(f"\n📈 **EXTRACTION COMPARISON SUMMARY**")
        
        # Success patterns
        success_domains = [urlparse(test['url']).netloc for test in successful_tests]
        failure_domains = [urlparse(test['url']).netloc for test in failed_tests]
        
        console.print(f"\n🎯 **SUCCESS PATTERNS:**")
        console.print(f"  Average Extraction Rate: {sum(t['extraction_rate'] for t in successful_tests) / len(successful_tests):.1%}")
        console.print(f"  Common Successful Domains: {', '.join(set(success_domains))}")
        
        console.print(f"\n⚠️ **FAILURE PATTERNS:**")
        failure_reasons = [test['error_message'] for test in failed_tests if test['error_message']]
        reason_counts = {}
        for reason in failure_reasons:
            reason_counts[reason] = reason_counts.get(reason, 0) + 1
        
        console.print(f"  Most Common Failure Reasons:")
        for reason, count in sorted(reason_counts.items(), key=lambda x: x[1], reverse=True):
            console.print(f"    {reason}: {count} occurrences")
        
        console.print(f"\n💡 **RECOMMENDATIONS:**")
        console.print(f"  1. Implement better handling for HTTP 403/401 responses")
        console.print(f"  2. Add retry logic with exponential backoff")
        console.print(f"  3. Implement User-Agent rotation")
        console.print(f"  4. Add Cloudflare bypass capabilities")
        console.print(f"  5. Implement CAPTCHA detection and handling")

def main():
    """Main content extraction analysis"""
    analyzer = ContentExtractionAnalyzer()
    analyzer.compare_extraction_results()

if __name__ == "__main__":
    main()
