#!/usr/bin/env python3
"""
Detailed Failure Report Generator
Creates comprehensive reports with actual content samples and failure analysis
"""

import sqlite3
import json
import requests
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import pandas as pd
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.syntax import Syntax
from urllib.parse import urlparse
import re
from bs4 import BeautifulSoup
import time

console = Console()

class DetailedFailureReporter:
    """Generate detailed failure reports with content analysis"""
    
    def __init__(self, db_path: str = "test_results.db"):
        self.db_path = db_path
        self.session = requests.Session()
        # Use different User-Agents to test
        self.user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Crawl4AI-Tester/1.0 (Educational Testing Purpose)'
        ]
    
    def test_url_with_different_approaches(self, url: str) -> Dict:
        """Test URL with different approaches to understand failure reasons"""
        results = {
            'url': url,
            'approaches': []
        }
        
        for i, user_agent in enumerate(self.user_agents):
            approach_name = f"UserAgent_{i+1}" if i < 3 else "Original_UA"
            
            try:
                self.session.headers.update({'User-Agent': user_agent})
                response = self.session.get(url, timeout=10, allow_redirects=True)
                
                approach_result = {
                    'name': approach_name,
                    'user_agent': user_agent,
                    'success': response.status_code == 200,
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds(),
                    'final_url': response.url,
                    'redirected': response.url != url,
                    'content_length': len(response.text),
                    'headers': dict(response.headers),
                    'content_sample': response.text[:500] if response.status_code == 200 else None,
                    'error': None
                }
                
                # Analyze response content for clues
                if response.status_code != 200:
                    approach_result['error_analysis'] = self._analyze_error_response(response)
                else:
                    approach_result['content_analysis'] = self._analyze_successful_response(response)
                
            except requests.exceptions.Timeout:
                approach_result = {
                    'name': approach_name,
                    'user_agent': user_agent,
                    'success': False,
                    'error': 'Timeout',
                    'status_code': None
                }
            except requests.exceptions.ConnectionError:
                approach_result = {
                    'name': approach_name,
                    'user_agent': user_agent,
                    'success': False,
                    'error': 'Connection Error',
                    'status_code': None
                }
            except Exception as e:
                approach_result = {
                    'name': approach_name,
                    'user_agent': user_agent,
                    'success': False,
                    'error': str(e),
                    'status_code': None
                }
            
            results['approaches'].append(approach_result)
            time.sleep(1)  # Rate limiting between attempts
        
        return results
    
    def _analyze_error_response(self, response) -> Dict:
        """Analyze error response for clues"""
        analysis = {
            'status_code': response.status_code,
            'reason': response.reason,
            'server': response.headers.get('server', 'Unknown'),
            'cloudflare_detected': 'cloudflare' in response.headers.get('server', '').lower(),
            'content_type': response.headers.get('content-type', ''),
            'cache_control': response.headers.get('cache-control', ''),
            'security_headers': {}
        }
        
        # Check for security headers
        security_headers = ['x-frame-options', 'x-content-type-options', 'x-xss-protection', 
                          'strict-transport-security', 'content-security-policy']
        for header in security_headers:
            if header in response.headers:
                analysis['security_headers'][header] = response.headers[header]
        
        # Analyze error page content
        if response.text:
            content_lower = response.text.lower()
            analysis['error_indicators'] = {
                'cloudflare_challenge': 'checking your browser' in content_lower or 'cloudflare' in content_lower,
                'captcha_detected': 'captcha' in content_lower or 'recaptcha' in content_lower,
                'rate_limited': 'rate limit' in content_lower or 'too many requests' in content_lower,
                'blocked_user_agent': 'user agent' in content_lower or 'bot' in content_lower,
                'geo_blocked': 'location' in content_lower or 'country' in content_lower,
                'maintenance_mode': 'maintenance' in content_lower or 'temporarily unavailable' in content_lower
            }
            
            # Extract error page title
            soup = BeautifulSoup(response.text, 'html.parser')
            if soup.title:
                analysis['error_page_title'] = soup.title.string
        
        return analysis
    
    def _analyze_successful_response(self, response) -> Dict:
        """Analyze successful response"""
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Remove script and style elements for clean text
        for script in soup(["script", "style"]):
            script.decompose()
        
        text_content = soup.get_text()
        clean_text = ' '.join(text_content.split())
        
        return {
            'title': soup.title.string if soup.title else None,
            'content_length': len(response.text),
            'text_length': len(clean_text),
            'word_count': len(clean_text.split()),
            'paragraphs': len(soup.find_all('p')),
            'headings': len(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])),
            'links': len(soup.find_all('a', href=True)),
            'images': len(soup.find_all('img')),
            'forms': len(soup.find_all('form')),
            'has_javascript': len(soup.find_all('script')) > 0,
            'meta_description': self._get_meta_description(soup),
            'language': soup.find('html', lang=True).get('lang') if soup.find('html', lang=True) else 'unknown'
        }
    
    def _get_meta_description(self, soup):
        """Extract meta description"""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        return meta_desc.get('content', '') if meta_desc else None
    
    def generate_comprehensive_failure_report(self):
        """Generate comprehensive failure report"""
        console.print(Panel("🔍 Comprehensive Failure Analysis Report", style="bold red"))
        
        # Get all failures
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT test_id, test_name, url, status, execution_time, 
                   extraction_rate, error_message, metadata, timestamp
            FROM test_results 
            WHERE status IN ('FAIL', 'ERROR')
            ORDER BY timestamp DESC
            LIMIT 10
        """)
        
        failures = []
        for row in cursor.fetchall():
            failure = {
                'test_id': row[0],
                'test_name': row[1],
                'url': row[2],
                'status': row[3],
                'execution_time': row[4],
                'extraction_rate': row[5],
                'error_message': row[6],
                'metadata': json.loads(row[7]) if row[7] else {},
                'timestamp': row[8]
            }
            failures.append(failure)
        
        conn.close()
        
        if not failures:
            console.print("🎉 **NO FAILURES FOUND** - All tests are working perfectly!")
            return
        
        console.print(f"\n📊 **ANALYZING {len(failures)} RECENT FAILURES**")
        
        # Analyze each failure in detail
        for i, failure in enumerate(failures, 1):
            console.print(f"\n{'='*80}")
            console.print(f"🔍 **FAILURE ANALYSIS {i}/{len(failures)}**")
            console.print(f"{'='*80}")
            
            # Basic failure info
            console.print(f"**Test ID**: {failure['test_id']}")
            console.print(f"**URL**: {failure['url']}")
            console.print(f"**Original Error**: {failure['error_message']}")
            console.print(f"**Timestamp**: {failure['timestamp']}")
            
            # Test with different approaches
            console.print(f"\n🧪 **TESTING WITH DIFFERENT APPROACHES**")
            test_results = self.test_url_with_different_approaches(failure['url'])
            
            # Display results for each approach
            for approach in test_results['approaches']:
                console.print(f"\n--- {approach['name']} ---")
                
                if approach['success']:
                    console.print(f"✅ **SUCCESS** (Status: {approach['status_code']})")
                    console.print(f"Response Time: {approach['response_time']:.2f}s")
                    console.print(f"Content Length: {approach['content_length']} chars")
                    
                    if 'content_analysis' in approach:
                        analysis = approach['content_analysis']
                        console.print(f"Title: {analysis.get('title', 'None')}")
                        console.print(f"Word Count: {analysis.get('word_count', 0)}")
                        console.print(f"Paragraphs: {analysis.get('paragraphs', 0)}")
                        console.print(f"Has JavaScript: {analysis.get('has_javascript', False)}")
                    
                    # Show content sample
                    if approach.get('content_sample'):
                        console.print(f"\n📄 **CONTENT SAMPLE**:")
                        console.print(Panel(approach['content_sample'], 
                                           title="HTML Content", border_style="green"))
                else:
                    console.print(f"❌ **FAILED** (Status: {approach.get('status_code', 'None')})")
                    console.print(f"Error: {approach.get('error', 'Unknown')}")
                    
                    if 'error_analysis' in approach:
                        error_analysis = approach['error_analysis']
                        console.print(f"Server: {error_analysis.get('server', 'Unknown')}")
                        console.print(f"Cloudflare: {error_analysis.get('cloudflare_detected', False)}")
                        
                        if error_analysis.get('error_indicators'):
                            indicators = error_analysis['error_indicators']
                            active_indicators = [k for k, v in indicators.items() if v]
                            if active_indicators:
                                console.print(f"Protection Detected: {', '.join(active_indicators)}")
            
            # Summary for this failure
            successful_approaches = [a for a in test_results['approaches'] if a['success']]
            if successful_approaches:
                console.print(f"\n💡 **ANALYSIS SUMMARY**:")
                console.print(f"✅ {len(successful_approaches)}/{len(test_results['approaches'])} approaches succeeded")
                console.print(f"🔧 **Recommendation**: Original failure likely due to User-Agent or rate limiting")
            else:
                console.print(f"\n💡 **ANALYSIS SUMMARY**:")
                console.print(f"❌ All approaches failed - likely protected site")
                
                # Analyze common failure patterns
                status_codes = [a.get('status_code') for a in test_results['approaches'] if a.get('status_code')]
                if status_codes:
                    most_common_status = max(set(status_codes), key=status_codes.count)
                    console.print(f"🔧 **Most Common Status**: {most_common_status}")
                    
                    if most_common_status == 403:
                        console.print("🛡️ **Likely Cause**: Bot protection or geographic blocking")
                    elif most_common_status == 401:
                        console.print("🔐 **Likely Cause**: Authentication required")
                    elif most_common_status == 429:
                        console.print("⏱️ **Likely Cause**: Rate limiting")
            
            console.print(f"\n")
        
        # Generate overall recommendations
        self._generate_overall_recommendations(failures)
    
    def _generate_overall_recommendations(self, failures: List[Dict]):
        """Generate overall recommendations based on failure patterns"""
        console.print(f"\n📋 **OVERALL RECOMMENDATIONS**")
        
        # Analyze failure patterns
        error_types = {}
        domains = {}
        
        for failure in failures:
            error = failure.get('error_message', 'Unknown')
            domain = urlparse(failure['url']).netloc
            
            error_types[error] = error_types.get(error, 0) + 1
            domains[domain] = domains.get(domain, 0) + 1
        
        console.print(f"\n🔍 **FAILURE PATTERNS**:")
        console.print(f"Most Common Errors:")
        for error, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:3]:
            console.print(f"  • {error}: {count} occurrences")
        
        console.print(f"\n🌐 **PROBLEMATIC DOMAINS**:")
        for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True)[:5]:
            console.print(f"  • {domain}: {count} failures")
        
        console.print(f"\n💡 **IMPLEMENTATION RECOMMENDATIONS**:")
        console.print(f"1. **User-Agent Rotation**: Implement multiple User-Agent strings")
        console.print(f"2. **Retry Logic**: Add exponential backoff for failed requests")
        console.print(f"3. **Rate Limiting**: Implement more conservative rate limiting")
        console.print(f"4. **Proxy Support**: Add proxy rotation for blocked IPs")
        console.print(f"5. **Error Handling**: Improve error categorization and handling")
        console.print(f"6. **Monitoring**: Add real-time failure monitoring and alerts")

def main():
    """Main detailed failure report execution"""
    reporter = DetailedFailureReporter()
    reporter.generate_comprehensive_failure_report()

if __name__ == "__main__":
    main()
