"""
Crawl4AI Testing Framework
Implements comprehensive testing as specified in TEST.md
"""

import asyncio
import json
import sqlite3
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, TaskID
from rich.panel import Panel
import requests
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from config import test_config, google_config, known_sites, SEARCH_CATEGORIES

# Set up rich console for beautiful output
console = Console()

@dataclass
class TestResult:
    """Test result data structure"""
    test_id: str
    test_name: str
    url: str
    status: str  # PASS, FAIL, SKIP, ERROR
    execution_time: float
    extraction_rate: float
    error_message: Optional[str] = None
    metadata: Optional[Dict] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class WebsiteClassification:
    """Website classification for cache system"""
    domain: str
    technology_stack: List[str]
    authentication_required: bool
    has_javascript: bool
    has_infinite_scroll: bool
    has_ajax: bool
    anti_bot_measures: List[str]
    confidence_score: float
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()

class TestDatabase:
    """SQLite database for test results and cache"""
    
    def __init__(self, db_path: str = "test_results.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Test results table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_id TEXT NOT NULL,
                test_name TEXT NOT NULL,
                url TEXT NOT NULL,
                status TEXT NOT NULL,
                execution_time REAL,
                extraction_rate REAL,
                error_message TEXT,
                metadata TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Website classifications cache
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS website_classifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                domain TEXT UNIQUE NOT NULL,
                technology_stack TEXT,
                authentication_required BOOLEAN,
                has_javascript BOOLEAN,
                has_infinite_scroll BOOLEAN,
                has_ajax BOOLEAN,
                anti_bot_measures TEXT,
                confidence_score REAL,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Discovered websites table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS discovered_websites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT UNIQUE NOT NULL,
                source TEXT,
                category TEXT,
                discovery_method TEXT,
                tested BOOLEAN DEFAULT FALSE,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
    
    def save_test_result(self, result: TestResult):
        """Save test result to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO test_results 
            (test_id, test_name, url, status, execution_time, extraction_rate, error_message, metadata, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            result.test_id,
            result.test_name,
            result.url,
            result.status,
            result.execution_time,
            result.extraction_rate,
            result.error_message,
            json.dumps(result.metadata) if result.metadata else None,
            result.timestamp
        ))
        
        conn.commit()
        conn.close()
    
    def save_classification(self, classification: WebsiteClassification):
        """Save website classification to cache"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO website_classifications
            (domain, technology_stack, authentication_required, has_javascript, 
             has_infinite_scroll, has_ajax, anti_bot_measures, confidence_score, last_updated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            classification.domain,
            json.dumps(classification.technology_stack),
            classification.authentication_required,
            classification.has_javascript,
            classification.has_infinite_scroll,
            classification.has_ajax,
            json.dumps(classification.anti_bot_measures),
            classification.confidence_score,
            classification.last_updated
        ))
        
        conn.commit()
        conn.close()
    
    def get_classification(self, domain: str) -> Optional[WebsiteClassification]:
        """Retrieve website classification from cache"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM website_classifications WHERE domain = ?
        """, (domain,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return WebsiteClassification(
                domain=row[1],
                technology_stack=json.loads(row[2]),
                authentication_required=bool(row[3]),
                has_javascript=bool(row[4]),
                has_infinite_scroll=bool(row[5]),
                has_ajax=bool(row[6]),
                anti_bot_measures=json.loads(row[7]),
                confidence_score=row[8],
                last_updated=datetime.fromisoformat(row[9])
            )
        return None
    
    def get_test_statistics(self) -> Dict:
        """Get comprehensive test statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Total tests
        cursor.execute("SELECT COUNT(*) FROM test_results")
        total_tests = cursor.fetchone()[0]
        
        # Success rate
        cursor.execute("SELECT COUNT(*) FROM test_results WHERE status = 'PASS'")
        passed_tests = cursor.fetchone()[0]
        
        # Average extraction rate
        cursor.execute("SELECT AVG(extraction_rate) FROM test_results WHERE extraction_rate IS NOT NULL")
        avg_extraction_rate = cursor.fetchone()[0] or 0
        
        # Cache statistics
        cursor.execute("SELECT COUNT(*) FROM website_classifications")
        cached_classifications = cursor.fetchone()[0]
        
        # Discovered websites
        cursor.execute("SELECT COUNT(*) FROM discovered_websites")
        discovered_websites = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "average_extraction_rate": avg_extraction_rate * 100 if avg_extraction_rate else 0,
            "cached_classifications": cached_classifications,
            "discovered_websites": discovered_websites
        }

class GoogleSearchDiscovery:
    """Google Search API integration for website discovery"""
    
    def __init__(self):
        self.api_key = google_config.api_key
        self.cse_id = google_config.cse_id
        self.service = build("customsearch", "v1", developerKey=self.api_key)
    
    def search_websites(self, query: str, num_results: int = 10) -> List[str]:
        """Search for websites using Google Custom Search API"""
        try:
            result = self.service.cse().list(
                q=query,
                cx=self.cse_id,
                num=num_results
            ).execute()
            
            urls = []
            if 'items' in result:
                for item in result['items']:
                    urls.append(item['link'])
            
            return urls
            
        except HttpError as e:
            console.print(f"[red]Google Search API Error: {e}[/red]")
            return []
    
    def discover_websites_by_category(self, category: str, max_sites: int = 50) -> List[str]:
        """Discover websites by category"""
        all_urls = []
        queries_per_category = max_sites // 10  # 10 results per query
        
        for i in range(queries_per_category):
            query = f"{category} site:*.com OR site:*.org OR site:*.net"
            urls = self.search_websites(query, 10)
            all_urls.extend(urls)
            
            # Rate limiting - respect API limits
            time.sleep(1)
        
        return list(set(all_urls))  # Remove duplicates

class Crawl4AITester:
    """Main testing framework class"""
    
    def __init__(self):
        self.db = TestDatabase()
        self.search_discovery = GoogleSearchDiscovery()
        self.tested_websites = set()
        self.total_target = test_config.target_websites
        
        # Create output directories
        Path(test_config.results_dir).mkdir(exist_ok=True)
        Path(test_config.reports_dir).mkdir(exist_ok=True)
        Path(test_config.logs_dir).mkdir(exist_ok=True)
    
    def display_progress_dashboard(self):
        """Display current testing progress"""
        stats = self.db.get_test_statistics()
        
        # Create progress table
        table = Table(title="🕷️ Crawl4AI Testing Progress Dashboard")
        table.add_column("Metric", style="cyan")
        table.add_column("Current", style="green")
        table.add_column("Target", style="yellow")
        table.add_column("Status", style="magenta")
        
        # Calculate progress
        websites_tested = len(self.tested_websites)
        progress_pct = (websites_tested / self.total_target * 100) if self.total_target > 0 else 0
        
        table.add_row("Websites Tested", f"{websites_tested}", f"{self.total_target}", f"{progress_pct:.1f}%")
        table.add_row("Total Tests", f"{stats['total_tests']}", "80+", "📊")
        table.add_row("Success Rate", f"{stats['success_rate']:.1f}%", ">80%", "✅" if stats['success_rate'] > 80 else "⚠️")
        table.add_row("Extraction Rate", f"{stats['average_extraction_rate']:.1f}%", ">85%", "✅" if stats['average_extraction_rate'] > 85 else "⚠️")
        table.add_row("Cache Entries", f"{stats['cached_classifications']}", "300+", "🗄️")
        table.add_row("Discovered Sites", f"{stats['discovered_websites']}", "500+", "🔍")
        
        console.print(table)
        console.print()

# Initialize the testing framework
tester = Crawl4AITester()
