{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://assuredworkloads.googleapis.com/", "batchPath": "batch", "canonicalName": "Assuredworkloads", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/learnmoreurl", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "assuredworkloads:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://assuredworkloads.mtls.googleapis.com/", "name": "assuredworkloads", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"resources": {"locations": {"resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "assuredworkloads.organizations.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "assuredworkloads.organizations.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "workloads": {"methods": {"analyzeWorkloadMove": {"description": "Analyzes a hypothetical move of a source resource to a target workload to surface compliance risks. The analysis is best effort and is not guaranteed to be exhaustive.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}:analyzeWorkloadMove", "httpMethod": "GET", "id": "assuredworkloads.organizations.locations.workloads.analyzeWorkloadMove", "parameterOrder": ["target"], "parameters": {"assetTypes": {"description": "Optional. List of asset types to be analyzed, including and under the source resource. If empty, all assets are analyzed. The complete list of asset types is available [here](https://cloud.google.com/asset-inventory/docs/supported-asset-types).", "location": "query", "repeated": true, "type": "string"}, "pageSize": {"description": "Optional. Page size. If a value is not specified, the default value of 10 is used. The maximum value is 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token from the previous response. It needs to be passed in the second and following requests.", "location": "query", "type": "string"}, "project": {"description": "The source type is a project. Specify the project's relative resource name, formatted as either a project number or a project ID: \"projects/{PROJECT_NUMBER}\" or \"projects/{PROJECT_ID}\" For example: \"projects/951040570662\" when specifying a project number, or \"projects/my-project-123\" when specifying a project ID.", "location": "query", "type": "string"}, "target": {"description": "Required. The resource ID of the folder-based destination workload. This workload is where the source resource will hypothetically be moved to. Specify the workload's relative resource name, formatted as: \"organizations/{ORGANIZATION_ID}/locations/{LOCATION_ID}/workloads/{WORKLOAD_ID}\" For example: \"organizations/123/locations/us-east1/workloads/assured-workload-2\"", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+target}:analyzeWorkloadMove", "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1AnalyzeWorkloadMoveResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates Assured Workload.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads", "httpMethod": "POST", "id": "assuredworkloads.organizations.locations.workloads.create", "parameterOrder": ["parent"], "parameters": {"externalId": {"description": "Optional. A identifier associated with the workload and underlying projects which allows for the break down of billing costs for a workload. The value provided for the identifier will add a label to the workload and contained projects with the identifier as the value.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the new Workload's parent. Must be of the form `organizations/{org_id}/locations/{location_id}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/workloads", "request": {"$ref": "GoogleCloudAssuredworkloadsV1beta1Workload"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the workload. Make sure that workload's direct children are already in a deleted state, otherwise the request will fail with a FAILED_PRECONDITION error. In addition to assuredworkloads.workload.delete permission, the user should also have orgpolicy.policy.set permission on the deleted folder to remove Assured Workloads OrgPolicies.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}", "httpMethod": "DELETE", "id": "assuredworkloads.organizations.locations.workloads.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. The etag of the workload. If this is provided, it must match the server's etag.", "location": "query", "type": "string"}, "name": {"description": "Required. The `name` field is used to identify the workload. Format: organizations/{org_id}/locations/{location_id}/workloads/{workload_id}", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enableComplianceUpdates": {"description": "This endpoint enables Assured Workloads service to offer compliance updates for the folder based assured workload. It sets up an Assured Workloads Service Agent, having permissions to read compliance controls (for example: Org Policies) applied on the workload. The caller must have `resourcemanager.folders.getIamPolicy` and `resourcemanager.folders.setIamPolicy` permissions on the assured workload folder.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}:enableComplianceUpdates", "httpMethod": "PUT", "id": "assuredworkloads.organizations.locations.workloads.enableComplianceUpdates", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The `name` field is used to identify the workload. Format: organizations/{org_id}/locations/{location_id}/workloads/{workload_id}", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:enableComplianceUpdates", "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1EnableComplianceUpdatesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enableResourceMonitoring": {"description": "Enable resource violation monitoring for a workload.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}:enableResourceMonitoring", "httpMethod": "POST", "id": "assuredworkloads.organizations.locations.workloads.enableResourceMonitoring", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The `name` field is used to identify the workload. Format: organizations/{org_id}/locations/{location_id}/workloads/{workload_id}", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:enableResourceMonitoring", "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1EnableResourceMonitoringResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets Assured Workload associated with a CRM Node", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}", "httpMethod": "GET", "id": "assuredworkloads.organizations.locations.workloads.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Workload to fetch. This is the workloads's relative path in the API, formatted as \"organizations/{organization_id}/locations/{location_id}/workloads/{workload_id}\". For example, \"organizations/123/locations/us-east1/workloads/assured-workload-1\".", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1Workload"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Assured Workloads under a CRM Node.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads", "httpMethod": "GET", "id": "assuredworkloads.organizations.locations.workloads.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A custom filter for filtering by properties of a workload. At this time, only filtering by labels is supported.", "location": "query", "type": "string"}, "pageSize": {"description": "Page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token returned from previous request. Page token contains context from previous request. Page token needs to be passed in the second and following requests.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent Resource to list workloads from. Must be of the form `organizations/{org_id}/locations/{location}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/workloads", "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ListWorkloadsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing workload. Currently allows updating of workload display_name and labels. For force updates don't set etag field in the Workload. Only one update operation per workload can be in progress.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}", "httpMethod": "PATCH", "id": "assuredworkloads.organizations.locations.workloads.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Optional. The resource name of the workload. Format: organizations/{organization}/locations/{location}/workloads/{workload} Read-only.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudAssuredworkloadsV1beta1Workload"}, "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1Workload"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "restrictAllowedResources": {"description": "Restrict the list of resources allowed in the Workload environment. The current list of allowed products can be found at https://cloud.google.com/assured-workloads/docs/supported-products In addition to assuredworkloads.workload.update permission, the user should also have orgpolicy.policy.set permission on the folder resource to use this functionality.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}:restrictAllowedResources", "httpMethod": "POST", "id": "assuredworkloads.organizations.locations.workloads.restrictAllowedResources", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Workload. This is the workloads's relative path in the API, formatted as \"organizations/{organization_id}/locations/{location_id}/workloads/{workload_id}\". For example, \"organizations/123/locations/us-east1/workloads/assured-workload-1\".", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:restrictAllowedResources", "request": {"$ref": "GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesRequest"}, "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"updates": {"methods": {"apply": {"description": "This endpoint creates a new operation to apply the given update.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}/updates/{updatesId}:apply", "httpMethod": "POST", "id": "assuredworkloads.organizations.locations.workloads.updates.apply", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the update. Format: organizations/{org_id}/locations/{location_id}/workloads/{workload_id}/updates/{update_id}", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+/updates/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:apply", "request": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ApplyWorkloadUpdateRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "This endpoint lists all updates for the given workload.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}/updates", "httpMethod": "GET", "id": "assuredworkloads.organizations.locations.workloads.updates.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Page size. The default value is 20 and the max allowed value is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token returned from previous request.", "location": "query", "type": "string"}, "parent": {"description": "Required. organizations/{org_id}/locations/{location_id}/workloads/{workload_id}", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/updates", "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ListWorkloadUpdatesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "violations": {"methods": {"acknowledge": {"description": "Acknowledges an existing violation. By acknowledging a violation, users acknowledge the existence of a compliance violation in their workload and decide to ignore it due to a valid business justification. Acknowledgement is a permanent operation and it cannot be reverted.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}/violations/{violationsId}:acknowledge", "httpMethod": "POST", "id": "assuredworkloads.organizations.locations.workloads.violations.acknowledge", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Violation to acknowledge. Format: organizations/{organization}/locations/{location}/workloads/{workload}/violations/{violation}", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+/violations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:acknowledge", "request": {"$ref": "GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationRequest"}, "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves Assured Workload Violation based on ID.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}/violations/{violationsId}", "httpMethod": "GET", "id": "assuredworkloads.organizations.locations.workloads.violations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Violation to fetch (ie. Violation.name). Format: organizations/{organization}/locations/{location}/workloads/{workload}/violations/{violation}", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+/violations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1Violation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the Violations in the AssuredWorkload Environment. Callers may also choose to read across multiple Workloads as per [AIP-159](https://google.aip.dev/159) by using '-' (the hyphen or dash character) as a wildcard character instead of workload-id in the parent. Format `organizations/{org_id}/locations/{location}/workloads/-`", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/workloads/{workloadsId}/violations", "httpMethod": "GET", "id": "assuredworkloads.organizations.locations.workloads.violations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A custom filter for filtering by the Violations properties.", "location": "query", "type": "string"}, "interval.endTime": {"description": "The end of the time window.", "format": "google-datetime", "location": "query", "type": "string"}, "interval.startTime": {"description": "The start of the time window.", "format": "google-datetime", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Page token returned from previous request.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Workload name. Format `organizations/{org_id}/locations/{location}/workloads/{workload}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/violations", "response": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ListViolationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250410", "rootUrl": "https://assuredworkloads.googleapis.com/", "schemas": {"GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationRequest": {"description": "Request for acknowledging the violation", "id": "GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationRequest", "properties": {"acknowledgeType": {"description": "Optional. Acknowledge type of specified violation.", "enum": ["ACKNOWLEDGE_TYPE_UNSPECIFIED", "SINGLE_VIOLATION", "EXISTING_CHILD_RESOURCE_VIOLATIONS"], "enumDescriptions": ["Acknowledge type unspecified.", "Acknowledge only the specific violation.", "Acknowledge specified orgPolicy violation and also associated resource violations."], "type": "string"}, "comment": {"description": "Required. Business justification explaining the need for violation acknowledgement", "type": "string"}, "nonCompliantOrgPolicy": {"deprecated": true, "description": "Optional. This field is deprecated and will be removed in future version of the API. Name of the OrgPolicy which was modified with non-compliant change and resulted in this violation. Format: projects/{project_number}/policies/{constraint_name} folders/{folder_id}/policies/{constraint_name} organizations/{organization_id}/policies/{constraint_name}", "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationResponse": {"description": "Response for violation acknowledgement", "id": "GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationResponse", "properties": {}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1AnalyzeWorkloadMoveResponse": {"description": "Response containing the analysis results for the hypothetical resource move.", "id": "GoogleCloudAssuredworkloadsV1beta1AnalyzeWorkloadMoveResponse", "properties": {"assetMoveAnalyses": {"description": "List of analysis results for each asset in scope.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1AssetMoveAnalysis"}, "type": "array"}, "nextPageToken": {"description": "The next page token. Is empty if the last page is reached.", "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ApplyWorkloadUpdateOperationMetadata": {"description": "Operation metadata to give request details of ApplyWorkloadUpdate.", "id": "GoogleCloudAssuredworkloadsV1beta1ApplyWorkloadUpdateOperationMetadata", "properties": {"action": {"description": "Optional. The time the operation was created.", "enum": ["WORKLOAD_UPDATE_ACTION_UNSPECIFIED", "APPLY"], "enumDescriptions": ["Unspecified value.", "The update is applied."], "type": "string"}, "createTime": {"description": "Optional. Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "updateName": {"description": "Required. The resource name of the update", "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ApplyWorkloadUpdateRequest": {"description": "Request to apply update to a workload.", "id": "GoogleCloudAssuredworkloadsV1beta1ApplyWorkloadUpdateRequest", "properties": {"action": {"description": "The action to be performed on the update.", "enum": ["WORKLOAD_UPDATE_ACTION_UNSPECIFIED", "APPLY"], "enumDescriptions": ["Unspecified value.", "The update is applied."], "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ApplyWorkloadUpdateResponse": {"description": "Response for ApplyWorkloadUpdate endpoint.", "id": "GoogleCloudAssuredworkloadsV1beta1ApplyWorkloadUpdateResponse", "properties": {"appliedUpdate": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadUpdate", "description": "The update that was applied."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1AssetMoveAnalysis": {"description": "Represents move analysis results for an asset.", "id": "GoogleCloudAssuredworkloadsV1beta1AssetMoveAnalysis", "properties": {"analysisGroups": {"description": "List of eligible analyses performed for the asset.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1MoveAnalysisGroup"}, "type": "array"}, "asset": {"description": "The full resource name of the asset being analyzed. Example: //compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1", "type": "string"}, "assetType": {"description": "Type of the asset being analyzed. Possible values will be among the ones listed [here](https://cloud.google.com/asset-inventory/docs/supported-asset-types).", "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1CreateWorkloadOperationMetadata": {"description": "Operation metadata to give request details of CreateWorkload.", "id": "GoogleCloudAssuredworkloadsV1beta1CreateWorkloadOperationMetadata", "properties": {"complianceRegime": {"description": "Optional. Compliance controls that should be applied to the resources managed by the workload.", "enum": ["COMPLIANCE_REGIME_UNSPECIFIED", "IL4", "CJIS", "FEDRAMP_HIGH", "FEDRAMP_MODERATE", "US_REGIONAL_ACCESS", "HIPAA", "HITRUST", "EU_REGIONS_AND_SUPPORT", "CA_REGIONS_AND_SUPPORT", "ITAR", "AU_REGIONS_AND_US_SUPPORT", "ASSURED_WORKLOADS_FOR_PARTNERS", "ISR_REGIONS", "ISR_REGIONS_AND_SUPPORT", "CA_PROTECTED_B", "IL5", "IL2", "JP_REGIONS_AND_SUPPORT", "KSA_REGIONS_AND_SUPPORT_WITH_SOVEREIGNTY_CONTROLS", "REGIONAL_CONTROLS", "HEALTHCARE_AND_LIFE_SCIENCES_CONTROLS", "HEALTHCARE_AND_LIFE_SCIENCES_CONTROLS_US_SUPPORT", "IRS_1075", "CANADA_CONTROLLED_GOODS"], "enumDeprecated": [false, false, false, false, false, false, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Unknown compliance regime.", "Information protection as per DoD IL4 requirements.", "Criminal Justice Information Services (CJIS) Security policies.", "FedRAMP High data protection controls", "FedRAMP Moderate data protection controls", "Assured Workloads For US Regions data protection controls", "[DEPRECATED] Health Insurance Portability and Accountability Act controls", "[DEPRECATED] Health Information Trust Alliance controls", "Assured Workloads For EU Regions and Support controls", "Assured Workloads For Canada Regions and Support controls", "International Traffic in Arms Regulations", "Assured Workloads for Australia Regions and Support controls", "Assured Workloads for Partners;", "Assured Workloads for Israel Regions", "Assured Workloads for Israel Regions", "Assured Workloads for Canada Protected B regime", "Information protection as per DoD IL5 requirements.", "Information protection as per DoD IL2 requirements.", "Assured Workloads for Japan Regions", "Assured Workloads Sovereign Controls KSA", "Assured Workloads for Regional Controls", "Healthcare and Life Science Controls", "Healthcare and Life Science Controls with US Support", "Internal Revenue Service 1075 controls", "Canada Controlled Goods"], "type": "string"}, "createTime": {"description": "Optional. Time when the operation was created.", "format": "google-datetime", "type": "string"}, "displayName": {"description": "Optional. The display name of the workload.", "type": "string"}, "parent": {"description": "Optional. The parent of the workload.", "type": "string"}, "resourceSettings": {"description": "Optional. Resource properties in the input that are used for creating/customizing workload resources.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadResourceSettings"}, "type": "array"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1EnableComplianceUpdatesResponse": {"description": "Response for EnableComplianceUpdates endpoint.", "id": "GoogleCloudAssuredworkloadsV1beta1EnableComplianceUpdatesResponse", "properties": {}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1EnableResourceMonitoringResponse": {"description": "Response for EnableResourceMonitoring endpoint.", "id": "GoogleCloudAssuredworkloadsV1beta1EnableResourceMonitoringResponse", "properties": {}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ListViolationsResponse": {"description": "Response of ListViolations endpoint.", "id": "GoogleCloudAssuredworkloadsV1beta1ListViolationsResponse", "properties": {"nextPageToken": {"description": "The next page token. Returns empty if reached the last page.", "type": "string"}, "violations": {"description": "List of Violations under a Workload.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1Violation"}, "type": "array"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ListWorkloadUpdatesResponse": {"description": "Response of listing the compliance updates per workload with pagination.", "id": "GoogleCloudAssuredworkloadsV1beta1ListWorkloadUpdatesResponse", "properties": {"nextPageToken": {"description": "The next page token. Return empty if reached the last page.", "type": "string"}, "workloadUpdates": {"description": "The list of workload updates for a given workload.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadUpdate"}, "type": "array"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ListWorkloadsResponse": {"description": "Response of ListWorkloads endpoint.", "id": "GoogleCloudAssuredworkloadsV1beta1ListWorkloadsResponse", "properties": {"nextPageToken": {"description": "The next page token. Return empty if reached the last page.", "type": "string"}, "workloads": {"description": "List of Workloads under a given parent.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1Workload"}, "type": "array"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1MoveAnalysisGroup": {"description": "Represents a logical group of checks performed for an asset. If successful, the group contains the analysis result, otherwise it contains an error with the failure reason.", "id": "GoogleCloudAssuredworkloadsV1beta1MoveAnalysisGroup", "properties": {"analysisResult": {"$ref": "GoogleCloudAssuredworkloadsV1beta1MoveAnalysisResult", "description": "Result of a successful analysis."}, "displayName": {"description": "Name of the analysis group.", "type": "string"}, "error": {"$ref": "GoogleRpcStatus", "description": "Error details for a failed analysis."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1MoveAnalysisResult": {"description": "Represents the successful move analysis results for a group.", "id": "GoogleCloudAssuredworkloadsV1beta1MoveAnalysisResult", "properties": {"blockers": {"description": "List of blockers. If not resolved, these will result in compliance violations in the target.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1MoveImpact"}, "type": "array"}, "warnings": {"description": "List of warnings. These are risks that may or may not result in compliance violations.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1MoveImpact"}, "type": "array"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1MoveImpact": {"description": "Represents the impact of moving the asset to the target.", "id": "GoogleCloudAssuredworkloadsV1beta1MoveImpact", "properties": {"detail": {"description": "Explanation of the impact.", "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1OrgPolicy": {"description": "This assured workload service object is used to represent the org policy attached to a resource. It servces the same purpose as the orgpolicy.v2.Policy object but with functionality that is limited to what is supported by Assured Workloads(e.g. only one rule under one OrgPolicy object, no conditions, etc).", "id": "GoogleCloudAssuredworkloadsV1beta1OrgPolicy", "properties": {"constraint": {"description": "The constraint name of the OrgPolicy. e.g. \"constraints/gcp.resourceLocations\".", "type": "string"}, "inherit": {"description": "If `inherit` is true, policy rules of the lowest ancestor in the resource hierarchy chain are inherited. If it is false, policy rules are not inherited.", "type": "boolean"}, "reset": {"description": "Ignores policies set above this resource and restores to the `constraint_default` value. `reset` can only be true when `rules` is empty and `inherit` is false.", "type": "boolean"}, "resource": {"description": "Resource that the OrgPolicy attaches to. Format: folders/123\" projects/123\".", "type": "string"}, "rule": {"$ref": "GoogleCloudAssuredworkloadsV1beta1OrgPolicyPolicyRule", "description": "The rule of the OrgPolicy."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1OrgPolicyPolicyRule": {"description": "A rule used to express this policy.", "id": "GoogleCloudAssuredworkloadsV1beta1OrgPolicyPolicyRule", "properties": {"allowAll": {"description": "ListPolicy only when all values are allowed.", "type": "boolean"}, "denyAll": {"description": "ListPolicy only when all values are denied.", "type": "boolean"}, "enforce": {"description": "BooleanPolicy only.", "type": "boolean"}, "values": {"$ref": "GoogleCloudAssuredworkloadsV1beta1OrgPolicyPolicyRuleStringValues", "description": "ListPolicy only when custom values are specified."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1OrgPolicyPolicyRuleStringValues": {"description": "The values allowed for a ListPolicy.", "id": "GoogleCloudAssuredworkloadsV1beta1OrgPolicyPolicyRuleStringValues", "properties": {"allowedValues": {"description": "List of values allowed at this resource.", "items": {"type": "string"}, "type": "array"}, "deniedValues": {"description": "List of values denied at this resource.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1OrgPolicyUpdate": {"description": "Represents an update for an org policy control applied on an Assured Workload resource. The inherited org policy is not considered.", "id": "GoogleCloudAssuredworkloadsV1beta1OrgPolicyUpdate", "properties": {"appliedPolicy": {"$ref": "GoogleCloudAssuredworkloadsV1beta1OrgPolicy", "description": "The org policy currently applied on the assured workload resource."}, "suggestedPolicy": {"$ref": "GoogleCloudAssuredworkloadsV1beta1OrgPolicy", "description": "The suggested org policy that replaces the applied policy."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesRequest": {"description": "Request for restricting list of available resources in Workload environment.", "id": "GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesRequest", "properties": {"restrictionType": {"description": "Required. The type of restriction for using gcp products in the Workload environment.", "enum": ["RESTRICTION_TYPE_UNSPECIFIED", "ALLOW_ALL_GCP_RESOURCES", "ALLOW_COMPLIANT_RESOURCES", "APPEND_COMPLIANT_RESOURCES"], "enumDescriptions": ["Unknown restriction type.", "Allow the use all of all gcp products, irrespective of the compliance posture. This effectively removes gcp.restrictServiceUsage OrgPolicy on the AssuredWorkloads Folder.", "Based on Workload's compliance regime, allowed list changes. See - https://cloud.google.com/assured-workloads/docs/supported-products for the list of supported resources.", "Similar to ALLOW_COMPLIANT_RESOURCES but adds the list of compliant resources to the existing list of compliant resources. Effective org-policy of the Folder is considered to ensure there is no disruption to the existing customer workflows."], "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesResponse": {"description": "Response for restricting the list of allowed resources.", "id": "GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesResponse", "properties": {}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1UpdateDetails": {"description": "The details of the update.", "id": "GoogleCloudAssuredworkloadsV1beta1UpdateDetails", "properties": {"orgPolicyUpdate": {"$ref": "GoogleCloudAssuredworkloadsV1beta1OrgPolicyUpdate", "description": "Update to one org policy, e.g. gcp.resourceLocation."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1Violation": {"description": "Workload monitoring Violation.", "id": "GoogleCloudAssuredworkloadsV1beta1Violation", "properties": {"acknowledged": {"description": "A boolean that indicates if the violation is acknowledged", "type": "boolean"}, "acknowledgementTime": {"description": "Optional. Timestamp when this violation was acknowledged first. Check exception_contexts to find the last time the violation was acknowledged when there are more than one violations. This field will be absent when acknowledged field is marked as false.", "format": "google-datetime", "type": "string"}, "associatedOrgPolicyViolationId": {"description": "Optional. Output only. Violation Id of the org-policy violation due to which the resource violation is caused. Empty for org-policy violations.", "readOnly": true, "type": "string"}, "auditLogLink": {"description": "Output only. Immutable. Audit Log Link for violated resource Format: https://console.cloud.google.com/logs/query;query={logName}{protoPayload.resourceName}{timeRange}{folder}", "readOnly": true, "type": "string"}, "beginTime": {"description": "Output only. Time of the event which triggered the Violation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "category": {"description": "Output only. Category under which this violation is mapped. e.g. Location, Service Usage, Access, Encryption, etc.", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Description for the Violation. e.g. OrgPolicy gcp.resourceLocations has non compliant value.", "readOnly": true, "type": "string"}, "exceptionAuditLogLink": {"description": "Output only. Immutable. Audit Log link to find business justification provided for violation exception. Format: https://console.cloud.google.com/logs/query;query={logName}{protoPayload.resourceName}{protoPayload.methodName}{timeRange}{organization}", "readOnly": true, "type": "string"}, "exceptionContexts": {"description": "Output only. List of all the exception detail added for the violation.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ViolationExceptionContext"}, "readOnly": true, "type": "array"}, "name": {"description": "Output only. Immutable. Name of the Violation. Format: organizations/{organization}/locations/{location}/workloads/{workload_id}/violations/{violations_id}", "readOnly": true, "type": "string"}, "nonCompliantOrgPolicy": {"description": "Output only. Immutable. Name of the OrgPolicy which was modified with non-compliant change and resulted this violation. Format: projects/{project_number}/policies/{constraint_name} folders/{folder_id}/policies/{constraint_name} organizations/{organization_id}/policies/{constraint_name}", "readOnly": true, "type": "string"}, "orgPolicyConstraint": {"deprecated": true, "description": "Output only. Immutable. The org-policy-constraint that was incorrectly changed, which resulted in this violation.", "readOnly": true, "type": "string"}, "parentProjectNumber": {"description": "Optional. Output only. Parent project number where resource is present. Empty for org-policy violations.", "readOnly": true, "type": "string"}, "remediation": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ViolationRemediation", "description": "Output only. Compliance violation remediation", "readOnly": true}, "resolveTime": {"description": "Output only. Time of the event which fixed the Violation. If the violation is ACTIVE this will be empty.", "format": "google-datetime", "readOnly": true, "type": "string"}, "resourceName": {"description": "Optional. Output only. Name of the resource like //storage.googleapis.com/myprojectxyz-testbucket. Empty for org-policy violations.", "readOnly": true, "type": "string"}, "resourceType": {"description": "Optional. Output only. Type of the resource like compute.googleapis.com/Disk, etc. Empty for org-policy violations.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. State of the violation", "enum": ["STATE_UNSPECIFIED", "RESOLVED", "UNRESOLVED", "EXCEPTION"], "enumDescriptions": ["Unspecified state.", "Violation is resolved.", "Violation is Unresolved", "Violation is Exception"], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last time when the Violation record was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "violationType": {"description": "Output only. Type of the violation", "enum": ["VIOLATION_TYPE_UNSPECIFIED", "ORG_POLICY", "RESOURCE"], "enumDescriptions": ["Unspecified type.", "Org Policy Violation.", "Resource Violation."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ViolationExceptionContext": {"description": "Violation exception detail.", "id": "GoogleCloudAssuredworkloadsV1beta1ViolationExceptionContext", "properties": {"acknowledgementTime": {"description": "Timestamp when the violation was acknowledged.", "format": "google-datetime", "type": "string"}, "comment": {"description": "Business justification provided towards the acknowledgement of the violation.", "type": "string"}, "userName": {"description": "Name of the user (or service account) who acknowledged the violation.", "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ViolationRemediation": {"description": "Represents remediation guidance to resolve compliance violation for AssuredWorkload", "id": "GoogleCloudAssuredworkloadsV1beta1ViolationRemediation", "properties": {"compliantValues": {"description": "Values that can resolve the violation For example: for list org policy violations, this will either be the list of allowed or denied values", "items": {"type": "string"}, "type": "array"}, "instructions": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructions", "description": "Required. Remediation instructions to resolve violations"}, "remediationType": {"description": "Output only. Reemediation type based on the type of org policy values violated", "enum": ["REMEDIATION_TYPE_UNSPECIFIED", "REMEDIATION_BOOLEAN_ORG_POLICY_VIOLATION", "REMEDIATION_LIST_ALLOWED_VALUES_ORG_POLICY_VIOLATION", "REMEDIATION_LIST_DENIED_VALUES_ORG_POLICY_VIOLATION", "REMEDIATION_RESTRICT_CMEK_CRYPTO_KEY_PROJECTS_ORG_POLICY_VIOLATION", "REMEDIATION_RESOURCE_VIOLATION", "REMEDIATION_RESOURCE_VIOLATION_NON_CMEK_SERVICES"], "enumDescriptions": ["Unspecified remediation type", "Remediation type for boolean org policy", "Remediation type for list org policy which have allowed values in the monitoring rule", "Remediation type for list org policy which have denied values in the monitoring rule", "Remediation type for gcp.restrictCmekCryptoKeyProjects", "Remediation type for resource violation.", "Remediation type for resource violation due to gcp.restrictNonCmekServices"], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructions": {"description": "Instructions to remediate violation", "id": "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructions", "properties": {"consoleInstructions": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsConsole", "description": "Remediation instructions to resolve violation via cloud console"}, "gcloudInstructions": {"$ref": "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsGcloud", "description": "Remediation instructions to resolve violation via gcloud cli"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsConsole": {"description": "Remediation instructions to resolve violation via cloud console", "id": "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsConsole", "properties": {"additionalLinks": {"description": "Additional urls for more information about steps", "items": {"type": "string"}, "type": "array"}, "consoleUris": {"description": "Link to console page where violations can be resolved", "items": {"type": "string"}, "type": "array"}, "steps": {"description": "Steps to resolve violation via cloud console", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsGcloud": {"description": "Remediation instructions to resolve violation via gcloud cli", "id": "GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsGcloud", "properties": {"additionalLinks": {"description": "Additional urls for more information about steps", "items": {"type": "string"}, "type": "array"}, "gcloudCommands": {"description": "Gcloud command to resolve violation", "items": {"type": "string"}, "type": "array"}, "steps": {"description": "Steps to resolve violation via gcloud cli", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1Workload": {"description": "A Workload object for managing highly regulated workloads of cloud customers.", "id": "GoogleCloudAssuredworkloadsV1beta1Workload", "properties": {"availableUpdates": {"description": "Output only. The number of updates available for the workload.", "format": "int32", "readOnly": true, "type": "integer"}, "billingAccount": {"description": "Optional. The billing account used for the resources which are direct children of workload. This billing account is initially associated with the resources created as part of Workload creation. After the initial creation of these resources, the customer can change the assigned billing account. The resource name has the form `billingAccounts/{billing_account_id}`. For example, `billingAccounts/012345-567890-ABCDEF`.", "type": "string"}, "cjisSettings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadCJISSettings", "deprecated": true, "description": "Input only. Immutable. Settings specific to resources needed for CJIS."}, "complianceRegime": {"description": "Required. Immutable. Compliance Regime associated with this workload.", "enum": ["COMPLIANCE_REGIME_UNSPECIFIED", "IL4", "CJIS", "FEDRAMP_HIGH", "FEDRAMP_MODERATE", "US_REGIONAL_ACCESS", "HIPAA", "HITRUST", "EU_REGIONS_AND_SUPPORT", "CA_REGIONS_AND_SUPPORT", "ITAR", "AU_REGIONS_AND_US_SUPPORT", "ASSURED_WORKLOADS_FOR_PARTNERS", "ISR_REGIONS", "ISR_REGIONS_AND_SUPPORT", "CA_PROTECTED_B", "IL5", "IL2", "JP_REGIONS_AND_SUPPORT", "KSA_REGIONS_AND_SUPPORT_WITH_SOVEREIGNTY_CONTROLS", "REGIONAL_CONTROLS", "HEALTHCARE_AND_LIFE_SCIENCES_CONTROLS", "HEALTHCARE_AND_LIFE_SCIENCES_CONTROLS_US_SUPPORT", "IRS_1075", "CANADA_CONTROLLED_GOODS"], "enumDeprecated": [false, false, false, false, false, false, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Unknown compliance regime.", "Information protection as per DoD IL4 requirements.", "Criminal Justice Information Services (CJIS) Security policies.", "FedRAMP High data protection controls", "FedRAMP Moderate data protection controls", "Assured Workloads For US Regions data protection controls", "[DEPRECATED] Health Insurance Portability and Accountability Act controls", "[DEPRECATED] Health Information Trust Alliance controls", "Assured Workloads For EU Regions and Support controls", "Assured Workloads For Canada Regions and Support controls", "International Traffic in Arms Regulations", "Assured Workloads for Australia Regions and Support controls", "Assured Workloads for Partners;", "Assured Workloads for Israel Regions", "Assured Workloads for Israel Regions", "Assured Workloads for Canada Protected B regime", "Information protection as per DoD IL5 requirements.", "Information protection as per DoD IL2 requirements.", "Assured Workloads for Japan Regions", "Assured Workloads Sovereign Controls KSA", "Assured Workloads for Regional Controls", "Healthcare and Life Science Controls", "Healthcare and Life Science Controls with US Support", "Internal Revenue Service 1075 controls", "Canada Controlled Goods"], "type": "string"}, "complianceStatus": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadComplianceStatus", "description": "Output only. Count of active Violations in the Workload.", "readOnly": true}, "complianceUpdatesEnabled": {"description": "Output only. Indicates whether the compliance updates feature is enabled for a workload. The compliance updates feature can be enabled via the EnableComplianceUpdates endpoint.", "readOnly": true, "type": "boolean"}, "compliantButDisallowedServices": {"description": "Output only. Urls for services which are compliant for this Assured Workload, but which are currently disallowed by the ResourceUsageRestriction org policy. Invoke RestrictAllowedResources endpoint to allow your project developers to use these services in their environment.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. Immutable. The Workload creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The user-assigned display name of the Workload. When present it must be between 4 to 30 characters. Allowed characters are: lowercase and uppercase letters, numbers, hyphen, and spaces. Example: My Workload", "type": "string"}, "ekmProvisioningResponse": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadEkmProvisioningResponse", "description": "Output only. Represents the Ekm Provisioning State of the given workload.", "readOnly": true}, "enableSovereignControls": {"description": "Optional. Indicates the sovereignty status of the given workload. Currently meant to be used by Europe/Canada customers.", "type": "boolean"}, "etag": {"description": "Optional. ETag of the workload, it is calculated on the basis of the Workload contents. It will be used in Update & Delete operations.", "type": "string"}, "fedrampHighSettings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampHighSettings", "deprecated": true, "description": "Input only. Immutable. Settings specific to resources needed for FedRAMP High."}, "fedrampModerateSettings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampModerateSettings", "deprecated": true, "description": "Input only. Immutable. Settings specific to resources needed for FedRAMP Moderate."}, "il4Settings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadIL4Settings", "deprecated": true, "description": "Input only. Immutable. Settings specific to resources needed for IL4."}, "kajEnrollmentState": {"description": "Output only. Represents the KAJ enrollment state of the given workload.", "enum": ["KAJ_ENROLLMENT_STATE_UNSPECIFIED", "KAJ_ENROLLMENT_STATE_PENDING", "KAJ_ENROLLMENT_STATE_COMPLETE"], "enumDescriptions": ["Default State for KAJ Enrollment.", "Pending State for KAJ Enrollment.", "Complete State for KAJ Enrollment."], "readOnly": true, "type": "string"}, "kmsSettings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings", "deprecated": true, "description": "Input only. Settings used to create a CMEK crypto key. When set, a project with a KMS CMEK key is provisioned. This field is deprecated as of Feb 28, 2022. In order to create a Keyring, callers should specify, ENCRYPTION_KEYS_PROJECT or KEYRING in ResourceSettings.resource_type field."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels applied to the workload.", "type": "object"}, "name": {"description": "Optional. The resource name of the workload. Format: organizations/{organization}/locations/{location}/workloads/{workload} Read-only.", "type": "string"}, "partner": {"description": "Optional. Partner regime associated with this workload.", "enum": ["PARTNER_UNSPECIFIED", "LOCAL_CONTROLS_BY_S3NS", "SOVEREIGN_CONTROLS_BY_T_SYSTEMS", "SOVEREIGN_CONTROLS_BY_SIA_MINSAIT", "SOVEREIGN_CONTROLS_BY_PSN", "SOVEREIGN_CONTROLS_BY_CNTXT", "SOVEREIGN_CONTROLS_BY_CNTXT_NO_EKM"], "enumDescriptions": ["", "<PERSON><PERSON> representing S3NS (Thales) partner.", "Enum representing T_SYSTEM (TSI) partner.", "Enum representing SIA_MINSAIT (Indra) partner.", "<PERSON><PERSON> representing PSN (TIM) partner.", "<PERSON><PERSON> representing CNTXT (Kingdom of Saudi Arabia) partner.", "Enum representing CNTXT (Kingdom of Saudi Arabia) partner offering without EKM."], "type": "string"}, "partnerPermissions": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadPartnerPermissions", "description": "Optional. Permissions granted to the AW Partner SA account for the customer workload"}, "partnerServicesBillingAccount": {"description": "Optional. Billing account necessary for purchasing services from Sovereign Partners. This field is required for creating SIA/PSN/CNTXT partner workloads. The caller should have 'billing.resourceAssociations.create' IAM permission on this billing-account. The format of this string is billingAccounts/AAAAAA-BBBBBB-CCCCCC", "type": "string"}, "provisionedResourcesParent": {"description": "Input only. The parent resource for the resources managed by this Assured Workload. May be either empty or a folder resource which is a child of the Workload parent. If not specified all resources are created under the parent organization. Format: folders/{folder_id}", "type": "string"}, "resourceMonitoringEnabled": {"description": "Output only. Indicates whether resource monitoring is enabled for workload or not. It is true when Resource feed is subscribed to AWM topic and AWM Service Agent Role is binded to AW Service Account for resource Assured workload.", "readOnly": true, "type": "boolean"}, "resourceSettings": {"description": "Input only. Resource properties that are used to customize workload resources. These properties (such as custom project id) will be used to create workload resources if possible. This field is optional.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadResourceSettings"}, "type": "array"}, "resources": {"description": "Output only. The resources associated with this workload. These resources will be created when creating the workload. If any of the projects already exist, the workload creation will fail. Always read only.", "items": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadResourceInfo"}, "readOnly": true, "type": "array"}, "saaEnrollmentResponse": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadSaaEnrollmentResponse", "description": "Output only. Represents the SAA enrollment response of the given workload. SAA enrollment response is queried during GetWorkload call. In failure cases, user friendly error message is shown in SAA details page.", "readOnly": true}, "violationNotificationsEnabled": {"description": "Optional. Indicates whether the e-mail notification for a violation is enabled for a workload. This value will be by default True, and if not present will be considered as true. This should only be updated via updateWorkload call. Any Changes to this field during the createWorkload call will not be honored. This will always be true while creating the workload.", "type": "boolean"}, "workloadOptions": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadWorkloadOptions", "description": "Optional. Options to be set for the given created workload."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadCJISSettings": {"deprecated": true, "description": "Settings specific to resources needed for CJIS.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadCJISSettings", "properties": {"kmsSettings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings", "description": "Input only. Immutable. Settings used to create a CMEK crypto key."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadComplianceStatus": {"description": "Represents the Compliance Status of this workload", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadComplianceStatus", "properties": {"acknowledgedResourceViolationCount": {"description": "Number of current resource violations which are not acknowledged.", "format": "int32", "type": "integer"}, "acknowledgedViolationCount": {"description": "Number of current orgPolicy violations which are acknowledged.", "format": "int32", "type": "integer"}, "activeResourceViolationCount": {"description": "Number of current resource violations which are acknowledged.", "format": "int32", "type": "integer"}, "activeViolationCount": {"description": "Number of current orgPolicy violations which are not acknowledged.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadEkmProvisioningResponse": {"description": "External key management systems(EKM) Provisioning response", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadEkmProvisioningResponse", "properties": {"ekmProvisioningErrorDomain": {"description": "Indicates Ekm provisioning error if any.", "enum": ["EKM_PROVISIONING_ERROR_DOMAIN_UNSPECIFIED", "UNSPECIFIED_ERROR", "GOOGLE_SERVER_ERROR", "EXTERNAL_USER_ERROR", "EXTERNAL_PARTNER_ERROR", "TIMEOUT_ERROR"], "enumDescriptions": ["No error domain", "Error but domain is unspecified.", "Internal logic breaks within provisioning code.", "Error occurred with the customer not granting permission/creating resource.", "Error occurred within the partner's provisioning cluster.", "Resource wasn't provisioned in the required 7 day time period"], "type": "string"}, "ekmProvisioningErrorMapping": {"description": "Detailed error message if Ekm provisioning fails", "enum": ["EKM_PROVISIONING_ERROR_MAPPING_UNSPECIFIED", "INVALID_SERVICE_ACCOUNT", "MISSING_METRICS_SCOPE_ADMIN_PERMISSION", "MISSING_EKM_CONNECTION_ADMIN_PERMISSION"], "enumDescriptions": ["Error is unspecified.", "Service account is used is invalid.", "Iam permission monitoring.MetricsScopeAdmin wasn't applied.", "Iam permission cloudkms.ekmConnectionsAdmin wasn't applied."], "type": "string"}, "ekmProvisioningState": {"description": "Output only. Indicates Ekm enrollment Provisioning of a given workload.", "enum": ["EKM_PROVISIONING_STATE_UNSPECIFIED", "EKM_PROVISIONING_STATE_PENDING", "EKM_PROVISIONING_STATE_FAILED", "EKM_PROVISIONING_STATE_COMPLETED"], "enumDescriptions": ["Default State for Ekm Provisioning", "Pending State for Ekm Provisioning", "Failed State for Ekm Provisioning", "Completed State for Ekm Provisioning"], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampHighSettings": {"deprecated": true, "description": "Settings specific to resources needed for FedRAMP High.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampHighSettings", "properties": {"kmsSettings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings", "description": "Input only. Immutable. Settings used to create a CMEK crypto key."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampModerateSettings": {"deprecated": true, "description": "Settings specific to resources needed for FedRAMP Moderate.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampModerateSettings", "properties": {"kmsSettings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings", "description": "Input only. Immutable. Settings used to create a CMEK crypto key."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadIL4Settings": {"deprecated": true, "description": "Settings specific to resources needed for IL4.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadIL4Settings", "properties": {"kmsSettings": {"$ref": "GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings", "description": "Input only. Immutable. Settings used to create a CMEK crypto key."}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings": {"deprecated": true, "description": "Settings specific to the Key Management Service.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings", "properties": {"nextRotationTime": {"description": "Required. Input only. Immutable. The time at which the Key Management Service will automatically create a new version of the crypto key and mark it as the primary.", "format": "google-datetime", "type": "string"}, "rotationPeriod": {"description": "Required. Input only. Immutable. [next_rotation_time] will be advanced by this period when the Key Management Service automatically rotates a key. Must be at least 24 hours and at most 876,000 hours.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadPartnerPermissions": {"description": "Permissions granted to the AW Partner SA account for the customer workload", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadPartnerPermissions", "properties": {"accessTransparencyLogsSupportCaseViewer": {"description": "Optional. Allow partner to view support case details for an AXT log", "type": "boolean"}, "assuredWorkloadsMonitoring": {"description": "Optional. Allow partner to view violation alerts.", "type": "boolean"}, "dataLogsViewer": {"description": "Optional. Allow the partner to view inspectability logs and monitoring violations.", "type": "boolean"}, "serviceAccessApprover": {"description": "Optional. Allow partner to view access approval logs.", "type": "boolean"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadResourceInfo": {"description": "Represent the resources that are children of this Workload.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadResourceInfo", "properties": {"resourceId": {"description": "Output only. Resource identifier. For a project this represents project_number.", "format": "int64", "readOnly": true, "type": "string"}, "resourceType": {"description": "Indicates the type of resource.", "enum": ["RESOURCE_TYPE_UNSPECIFIED", "CONSUMER_PROJECT", "CONSUMER_FOLDER", "ENCRYPTION_KEYS_PROJECT", "KEYRING"], "enumDeprecated": [false, true, false, false, false], "enumDescriptions": ["Unknown resource type.", "Deprecated. Existing workloads will continue to support this, but new CreateWorkloadRequests should not specify this as an input value.", "Consumer Folder.", "Consumer project containing encryption keys.", "Keyring resource that hosts encryption keys."], "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadResourceSettings": {"description": "Represent the custom settings for the resources to be created.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadResourceSettings", "properties": {"displayName": {"description": "User-assigned resource display name. If not empty it will be used to create a resource with the specified name.", "type": "string"}, "resourceId": {"description": "Resource identifier. For a project this represents project_id. If the project is already taken, the workload creation will fail. For KeyRing, this represents the keyring_id. For a folder, don't set this value as folder_id is assigned by Google.", "type": "string"}, "resourceType": {"description": "Indicates the type of resource. This field should be specified to correspond the id to the right project type (CONSUMER_PROJECT or ENCRYPTION_KEYS_PROJECT)", "enum": ["RESOURCE_TYPE_UNSPECIFIED", "CONSUMER_PROJECT", "CONSUMER_FOLDER", "ENCRYPTION_KEYS_PROJECT", "KEYRING"], "enumDeprecated": [false, true, false, false, false], "enumDescriptions": ["Unknown resource type.", "Deprecated. Existing workloads will continue to support this, but new CreateWorkloadRequests should not specify this as an input value.", "Consumer Folder.", "Consumer project containing encryption keys.", "Keyring resource that hosts encryption keys."], "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadSaaEnrollmentResponse": {"description": "Signed Access Approvals (SAA) enrollment response.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadSaaEnrollmentResponse", "properties": {"setupErrors": {"description": "Indicates SAA enrollment setup error if any.", "items": {"enum": ["SETUP_ERROR_UNSPECIFIED", "ERROR_INVALID_BASE_SETUP", "ERROR_MISSING_EXTERNAL_SIGNING_KEY", "ERROR_NOT_ALL_SERVICES_ENROLLED", "ERROR_SETUP_CHECK_FAILED"], "enumDescriptions": ["Unspecified.", "Invalid states for all customers, to be redirected to AA UI for additional details.", "Returned when there is not an EKM key configured.", "Returned when there are no enrolled services or the customer is enrolled in CAA only for a subset of services.", "Returned when exception was encountered during evaluation of other criteria."], "type": "string"}, "type": "array"}, "setupStatus": {"description": "Output only. Indicates SAA enrollment status of a given workload.", "enum": ["SETUP_STATE_UNSPECIFIED", "STATUS_PENDING", "STATUS_COMPLETE"], "enumDescriptions": ["Unspecified.", "SAA enrollment pending.", "SAA enrollment comopleted."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadUpdate": {"description": "A workload update is a change to the workload's compliance configuration.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadUpdate", "properties": {"createTime": {"description": "The time the update was created.", "format": "google-datetime", "type": "string"}, "details": {"$ref": "GoogleCloudAssuredworkloadsV1beta1UpdateDetails", "description": "The details of the update."}, "name": {"description": "Output only. Immutable. Identifier. Resource name of the WorkloadUpdate. Format: organizations/{organization}/locations/{location}/workloads/{workload}/updates/{update}", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the update.", "enum": ["STATE_UNSPECIFIED", "AVAILABLE", "APPLIED", "WITHDRAWN"], "enumDescriptions": ["Unspecified.", "The update is available to be applied.", "The update has been applied.", "The update has been withdrawn by the service."], "readOnly": true, "type": "string"}, "updateTime": {"description": "The time the update was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudAssuredworkloadsV1beta1WorkloadWorkloadOptions": {"description": "Options to be set for the given created workload.", "id": "GoogleCloudAssuredworkloadsV1beta1WorkloadWorkloadOptions", "properties": {"kajEnrollmentType": {"description": "Optional. Specifies type of KAJ Enrollment if provided.", "enum": ["KAJ_ENROLLMENT_TYPE_UNSPECIFIED", "KEY_ACCESS_TRANSPARENCY_OFF"], "enumDescriptions": ["KAJ Enrollment type is unspecified", "KAT sets External, Hardware, and Software key feature logging only to TRUE."], "type": "string"}}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Assured Workloads API", "version": "v1beta1", "version_module": true}