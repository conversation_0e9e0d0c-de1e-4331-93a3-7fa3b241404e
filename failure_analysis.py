#!/usr/bin/env python3
"""
Comprehensive Failure Analysis for Crawl4AI Testing
Analyzes test failures, content extraction issues, and patterns
"""

import sqlite3
import json
import requests
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import pandas as pd
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.syntax import Syntax
from urllib.parse import urlparse
import re
from bs4 import BeautifulSoup

console = Console()

class FailureAnalyzer:
    """Comprehensive failure analysis for crawl4ai testing"""
    
    def __init__(self, db_path: str = "test_results.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Crawl4AI-FailureAnalyzer/1.0 (Analysis Purpose)'
        })
    
    def get_all_failures(self) -> List[Dict]:
        """Get all failed test cases with detailed information"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT test_id, test_name, url, status, execution_time, 
                   extraction_rate, error_message, metadata, timestamp
            FROM test_results 
            WHERE status IN ('FAIL', 'ERROR')
            ORDER BY timestamp DESC
        """)
        
        failures = []
        for row in cursor.fetchall():
            failure = {
                'test_id': row[0],
                'test_name': row[1],
                'url': row[2],
                'status': row[3],
                'execution_time': row[4],
                'extraction_rate': row[5],
                'error_message': row[6],
                'metadata': json.loads(row[7]) if row[7] else {},
                'timestamp': row[8]
            }
            failures.append(failure)
        
        conn.close()
        return failures
    
    def get_partial_successes(self) -> List[Dict]:
        """Get tests that passed but with low extraction rates"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT test_id, test_name, url, status, execution_time, 
                   extraction_rate, error_message, metadata, timestamp
            FROM test_results 
            WHERE status = 'PASS' AND extraction_rate < 0.7
            ORDER BY extraction_rate ASC
        """)
        
        partial_successes = []
        for row in cursor.fetchall():
            result = {
                'test_id': row[0],
                'test_name': row[1],
                'url': row[2],
                'status': row[3],
                'execution_time': row[4],
                'extraction_rate': row[5],
                'error_message': row[6],
                'metadata': json.loads(row[7]) if row[7] else {},
                'timestamp': row[8]
            }
            partial_successes.append(result)
        
        conn.close()
        return partial_successes
    
    def analyze_content_extraction(self, url: str) -> Dict:
        """Analyze content extraction for a specific URL"""
        try:
            response = self.session.get(url, timeout=15, allow_redirects=True)
            
            if response.status_code != 200:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'content_length': 0,
                    'content_sample': None
                }
            
            # Parse content with BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract various content types
            analysis = {
                'success': True,
                'status_code': response.status_code,
                'content_length': len(response.text),
                'content_sample': response.text[:1000],  # First 1000 chars
                'title': soup.title.string if soup.title else None,
                'meta_description': None,
                'headings': {
                    'h1': len(soup.find_all('h1')),
                    'h2': len(soup.find_all('h2')),
                    'h3': len(soup.find_all('h3'))
                },
                'paragraphs': len(soup.find_all('p')),
                'links': len(soup.find_all('a')),
                'images': len(soup.find_all('img')),
                'forms': len(soup.find_all('form')),
                'scripts': len(soup.find_all('script')),
                'text_content': soup.get_text()[:500],  # First 500 chars of text
                'has_javascript': 'script' in response.text.lower(),
                'has_ajax': any(term in response.text.lower() for term in ['ajax', 'xhr', 'fetch']),
                'has_react': 'react' in response.text.lower(),
                'has_vue': 'vue' in response.text.lower(),
                'has_angular': 'angular' in response.text.lower(),
                'content_type': response.headers.get('content-type', ''),
                'server': response.headers.get('server', ''),
                'response_headers': dict(response.headers)
            }
            
            # Extract meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                analysis['meta_description'] = meta_desc.get('content', '')
            
            return analysis
            
        except requests.exceptions.Timeout:
            return {'success': False, 'error': 'Request timeout', 'content_length': 0}
        except requests.exceptions.ConnectionError:
            return {'success': False, 'error': 'Connection error', 'content_length': 0}
        except Exception as e:
            return {'success': False, 'error': str(e), 'content_length': 0}
    
    def categorize_failures(self, failures: List[Dict]) -> Dict[str, List[Dict]]:
        """Categorize failures by type"""
        categories = {
            'timeout': [],
            'connection_error': [],
            'http_error': [],
            'content_error': [],
            'unknown': []
        }
        
        for failure in failures:
            error_msg = failure.get('error_message', '').lower()
            
            if 'timeout' in error_msg:
                categories['timeout'].append(failure)
            elif 'connection' in error_msg or 'network' in error_msg:
                categories['connection_error'].append(failure)
            elif any(code in error_msg for code in ['404', '403', '500', '502', '503']):
                categories['http_error'].append(failure)
            elif 'content' in error_msg or 'parsing' in error_msg:
                categories['content_error'].append(failure)
            else:
                categories['unknown'].append(failure)
        
        return categories
    
    def generate_failure_report(self):
        """Generate comprehensive failure analysis report"""
        console.print(Panel("🔍 Comprehensive Failure Analysis Report", style="bold red"))
        
        # Get all failures
        failures = self.get_all_failures()
        partial_successes = self.get_partial_successes()
        
        console.print(f"\n📊 **FAILURE OVERVIEW**")
        console.print(f"Total Failures: {len(failures)}")
        console.print(f"Partial Successes (Low Extraction): {len(partial_successes)}")
        
        if not failures and not partial_successes:
            console.print("🎉 **NO FAILURES DETECTED** - All tests are performing excellently!")
            return
        
        # Categorize failures
        if failures:
            categories = self.categorize_failures(failures)
            
            console.print(f"\n🏷️ **FAILURE CATEGORIES**")
            for category, items in categories.items():
                if items:
                    console.print(f"  {category.upper()}: {len(items)} failures")
        
        # Detailed failure analysis
        if failures:
            console.print(f"\n❌ **DETAILED FAILURE ANALYSIS**")
            self.analyze_detailed_failures(failures[:10])  # Top 10 failures
        
        # Partial success analysis
        if partial_successes:
            console.print(f"\n⚠️ **PARTIAL SUCCESS ANALYSIS**")
            self.analyze_partial_successes(partial_successes[:5])  # Top 5 partial successes
        
        # Pattern analysis
        console.print(f"\n🔍 **FAILURE PATTERN ANALYSIS**")
        self.analyze_failure_patterns(failures)
        
        # Content quality analysis
        console.print(f"\n📄 **CONTENT QUALITY ANALYSIS**")
        self.analyze_content_quality(failures + partial_successes)
    
    def analyze_detailed_failures(self, failures: List[Dict]):
        """Analyze detailed failure information"""
        
        for i, failure in enumerate(failures, 1):
            console.print(f"\n--- FAILURE {i} ---")
            
            # Basic failure info
            table = Table(title=f"Failure Details: {failure['test_id']}")
            table.add_column("Property", style="cyan")
            table.add_column("Value", style="red")
            
            table.add_row("Test ID", failure['test_id'])
            table.add_row("URL", failure['url'])
            table.add_row("Status", failure['status'])
            table.add_row("Error Message", failure['error_message'] or "None")
            table.add_row("Execution Time", f"{failure['execution_time']:.2f}s")
            table.add_row("Extraction Rate", f"{failure['extraction_rate']:.1%}")
            table.add_row("Timestamp", failure['timestamp'])
            
            console.print(table)
            
            # Try to analyze the actual content
            console.print(f"\n🔍 **CONTENT ANALYSIS FOR**: {failure['url']}")
            content_analysis = self.analyze_content_extraction(failure['url'])
            
            if content_analysis['success']:
                console.print("✅ **Content Successfully Retrieved for Analysis**")
                console.print(f"Content Length: {content_analysis['content_length']} characters")
                console.print(f"Title: {content_analysis.get('title', 'None')}")
                console.print(f"Paragraphs: {content_analysis['paragraphs']}")
                console.print(f"Links: {content_analysis['links']}")
                console.print(f"Images: {content_analysis['images']}")
                console.print(f"JavaScript: {'Yes' if content_analysis['has_javascript'] else 'No'}")
                
                # Show content sample
                if content_analysis.get('text_content'):
                    console.print(f"\n📄 **CONTENT SAMPLE**:")
                    console.print(Panel(content_analysis['text_content'][:300] + "...", 
                                       title="Extracted Text", border_style="blue"))
            else:
                console.print(f"❌ **Content Analysis Failed**: {content_analysis['error']}")
    
    def analyze_partial_successes(self, partial_successes: List[Dict]):
        """Analyze partial successes with low extraction rates"""
        
        for i, result in enumerate(partial_successes, 1):
            console.print(f"\n--- PARTIAL SUCCESS {i} ---")
            console.print(f"URL: {result['url']}")
            console.print(f"Extraction Rate: {result['extraction_rate']:.1%}")
            
            # Analyze what might be causing low extraction
            content_analysis = self.analyze_content_extraction(result['url'])
            
            if content_analysis['success']:
                console.print(f"✅ Content accessible, analyzing quality...")
                
                # Identify potential issues
                issues = []
                if content_analysis['content_length'] < 1000:
                    issues.append("Very short content")
                if content_analysis['paragraphs'] < 3:
                    issues.append("Few paragraphs")
                if content_analysis['has_javascript'] and content_analysis['paragraphs'] < 5:
                    issues.append("JavaScript-heavy with minimal static content")
                if 'application/json' in content_analysis.get('content_type', ''):
                    issues.append("JSON response instead of HTML")
                
                if issues:
                    console.print(f"⚠️ **Potential Issues**: {', '.join(issues)}")
                else:
                    console.print("🤔 **No obvious issues detected** - may need deeper analysis")
            else:
                console.print(f"❌ Content analysis failed: {content_analysis['error']}")
    
    def analyze_failure_patterns(self, failures: List[Dict]):
        """Analyze patterns in failures"""
        
        if not failures:
            console.print("No failures to analyze patterns")
            return
        
        # Domain analysis
        domains = {}
        for failure in failures:
            domain = urlparse(failure['url']).netloc
            domains[domain] = domains.get(domain, 0) + 1
        
        if domains:
            console.print(f"\n🌐 **DOMAINS WITH FAILURES**:")
            for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True)[:5]:
                console.print(f"  {domain}: {count} failures")
        
        # Error message patterns
        error_patterns = {}
        for failure in failures:
            error = failure.get('error_message', 'Unknown')
            error_patterns[error] = error_patterns.get(error, 0) + 1
        
        if error_patterns:
            console.print(f"\n📝 **ERROR MESSAGE PATTERNS**:")
            for error, count in sorted(error_patterns.items(), key=lambda x: x[1], reverse=True)[:5]:
                console.print(f"  {error}: {count} occurrences")
    
    def analyze_content_quality(self, results: List[Dict]):
        """Analyze content quality across all results"""
        
        console.print(f"\n📊 **CONTENT QUALITY METRICS**")
        
        # Sample a few URLs for detailed content analysis
        sample_urls = [r['url'] for r in results[:5]]
        
        quality_metrics = {
            'accessible': 0,
            'has_title': 0,
            'has_content': 0,
            'has_javascript': 0,
            'total_analyzed': 0
        }
        
        for url in sample_urls:
            analysis = self.analyze_content_extraction(url)
            quality_metrics['total_analyzed'] += 1
            
            if analysis['success']:
                quality_metrics['accessible'] += 1
                if analysis.get('title'):
                    quality_metrics['has_title'] += 1
                if analysis.get('paragraphs', 0) > 0:
                    quality_metrics['has_content'] += 1
                if analysis.get('has_javascript'):
                    quality_metrics['has_javascript'] += 1
        
        # Display metrics
        total = quality_metrics['total_analyzed']
        if total > 0:
            console.print(f"Accessibility Rate: {quality_metrics['accessible']/total:.1%}")
            console.print(f"Title Presence: {quality_metrics['has_title']/total:.1%}")
            console.print(f"Content Presence: {quality_metrics['has_content']/total:.1%}")
            console.print(f"JavaScript Usage: {quality_metrics['has_javascript']/total:.1%}")

def main():
    """Main failure analysis execution"""
    
    analyzer = FailureAnalyzer()
    analyzer.generate_failure_report()

if __name__ == "__main__":
    main()
