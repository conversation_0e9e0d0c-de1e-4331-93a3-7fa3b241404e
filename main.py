#!/usr/bin/env python3
"""
Crawl4AI Comprehensive Testing Suite
Main execution script following TEST.md specifications
"""

import asyncio
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from test_framework import Crawl4AITester
from test_runner import TestExecutor
from config import test_config, SEARCH_CATEGORIES

console = Console()

async def main():
    """Main testing execution following TEST.md Section 11.1"""
    
    # Display startup banner
    banner = Text("🕷️ Crawl4AI Comprehensive Testing Suite", style="bold blue")
    console.print(Panel(banner, title="Starting Tests", border_style="blue"))
    
    # Initialize testing framework
    tester = Crawl4AITester()
    executor = TestExecutor(tester)
    
    console.print("📋 Following TEST.md Enhanced Test Sequence:")
    console.print("1. Environment Setup (API-01 through API-04)")
    console.print("2. Known Test Sites Validation (KT-01 through KT-10)")
    console.print("3. Google Search Discovery (GS-01 through GS-08)")
    console.print("4. Technology Stack Tests (TS-01 through TS-09)")
    console.print("5. Scale to 300+ websites with cache building")
    console.print()
    
    # Phase 1: Environment Setup Tests
    console.print(Panel("🔧 Phase 1: Environment Setup", style="cyan"))
    api_results = await executor.execute_api_tests()
    
    # Check if API tests passed before continuing
    api_success = all(result.status == "PASS" for result in api_results)
    if not api_success:
        console.print("[red]❌ API tests failed. Please check Google Search API configuration.[/red]")
        console.print("Continuing with limited functionality...")
    
    # Display progress after Phase 1
    tester.display_progress_dashboard()
    
    # Phase 2: Known Test Sites Validation
    console.print(Panel("🎯 Phase 2: Known Test Sites Validation", style="green"))
    known_sites_results = await executor.execute_known_test_sites()
    
    # Display progress after Phase 2
    tester.display_progress_dashboard()
    
    # Phase 3: Google Search Discovery (if API works)
    if api_success:
        console.print(Panel("🔍 Phase 3: Google Search Discovery", style="yellow"))
        await discover_and_test_websites(tester, executor)
    else:
        console.print("[yellow]⚠️ Skipping Google Search Discovery due to API issues[/yellow]")
    
    # Display final results
    console.print(Panel("📊 Final Test Results", style="magenta"))
    tester.display_progress_dashboard()
    
    # Generate summary report
    generate_summary_report(tester)

async def discover_and_test_websites(tester: Crawl4AITester, executor: TestExecutor):
    """Discover and test websites using Google Search API"""
    
    target_remaining = test_config.target_websites - len(tester.tested_websites)
    sites_per_category = min(50, target_remaining // len(SEARCH_CATEGORIES))
    
    console.print(f"🎯 Target: {target_remaining} more websites")
    console.print(f"📊 Testing ~{sites_per_category} sites per category")
    
    for category in SEARCH_CATEGORIES[:6]:  # Limit to 6 categories for now
        console.print(f"\n🔍 Discovering websites for: {category}")
        
        try:
            # Discover websites in this category
            urls = tester.search_discovery.discover_websites_by_category(category, sites_per_category)
            
            console.print(f"Found {len(urls)} websites in {category}")
            
            # Test discovered websites
            for i, url in enumerate(urls[:sites_per_category]):
                if len(tester.tested_websites) >= test_config.target_websites:
                    break
                
                if url not in tester.tested_websites:
                    result = await executor.test_discovered_website(url, category)
                    tester.tested_websites.add(url)
                    
                    # Show progress every 10 sites
                    if (i + 1) % 10 == 0:
                        console.print(f"  Tested {i + 1}/{len(urls)} sites in {category}")
        
        except Exception as e:
            console.print(f"[red]Error discovering {category}: {e}[/red]")
            continue
        
        # Check if we've reached our target
        if len(tester.tested_websites) >= test_config.target_websites:
            console.print(f"🎉 Reached target of {test_config.target_websites} websites!")
            break
        
        # Display progress
        tester.display_progress_dashboard()

def generate_summary_report(tester: Crawl4AITester):
    """Generate comprehensive summary report"""
    
    stats = tester.db.get_test_statistics()
    
    # Create summary report
    report_content = f"""
# Crawl4AI Testing Summary Report
Generated: {tester.db.get_test_statistics()}

## Overall Results
- **Total Websites Tested**: {len(tester.tested_websites)}
- **Total Test Cases**: {stats['total_tests']}
- **Success Rate**: {stats['success_rate']:.1f}%
- **Average Extraction Rate**: {stats['average_extraction_rate']:.1f}%
- **Cache Entries**: {stats['cached_classifications']}

## Success Criteria Evaluation
- ✅ Target Websites: {len(tester.tested_websites)}/{test_config.target_websites}
- {'✅' if stats['success_rate'] > 80 else '❌'} Success Rate: {stats['success_rate']:.1f}% (Target: >80%)
- {'✅' if stats['average_extraction_rate'] > 85 else '❌'} Extraction Rate: {stats['average_extraction_rate']:.1f}% (Target: >85%)
- ✅ Cache System: {stats['cached_classifications']} classifications stored

## Next Steps
1. Continue testing to reach 300+ websites
2. Implement advanced crawl4ai integration
3. Add more sophisticated content analysis
4. Enhance cache system with machine learning
"""
    
    # Save report
    report_path = Path(test_config.reports_dir) / "summary_report.md"
    with open(report_path, 'w') as f:
        f.write(report_content)
    
    console.print(f"📄 Summary report saved to: {report_path}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("\n[yellow]Testing interrupted by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n[red]Fatal error: {e}[/red]")
        sys.exit(1)
